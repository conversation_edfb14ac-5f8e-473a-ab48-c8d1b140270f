//! 测试SIMD借用冲突修复
use rustnum_core::array::array_impl::RustArray;
use rustnum_core::array::simd_math::SimdMath;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("测试SIMD借用冲突修复...");
    
    // 创建测试数组
    let arr1 = RustArray::new(vec![1.0f32, 2.0, 3.0, 4.0]);
    let arr2 = RustArray::new(vec![2.0f32, 3.0, 4.0, 5.0]);
    
    // 测试SIMD加法
    let result_add = arr1.simd_add(&arr2)?;
    println!("SIMD加法结果: {:?}", result_add.data());
    
    // 测试SIMD减法
    let result_sub = arr1.simd_sub(&arr2)?;
    println!("SIMD减法结果: {:?}", result_sub.data());
    
    // 测试SIMD乘法
    let result_mul = arr1.simd_mul(&arr2)?;
    println!("SIMD乘法结果: {:?}", result_mul.data());
    
    // 测试SIMD除法
    let result_div = arr1.simd_div(&arr2)?;
    println!("SIMD除法结果: {:?}", result_div.data());
    
    // 测试标量运算
    let result_add_scalar = arr1.simd_add_scalar(10.0)?;
    println!("SIMD标量加法结果: {:?}", result_add_scalar.data());
    
    let result_mul_scalar = arr1.simd_mul_scalar(2.0)?;
    println!("SIMD标量乘法结果: {:?}", result_mul_scalar.data());
    
    println!("✅ 所有SIMD操作成功完成，借用冲突问题已解决！");
    Ok(())
}