//! Arrow 集成演示
//! 
//! 本示例展示了 RustNum 与 Apache Arrow 的深度集成功能，
//! 包括零拷贝数据转换、向量化计算和高性能数据处理。

use rustnum_core::{
    array::{RustArray, StorageOrder, ArrowIntegration, ArrowCompute},
    memory::create_default_pool,
    error::RustNumError,
};
use parking_lot::RwLock;
use std::sync::Arc;
use std::time::Instant;

fn main() -> Result<(), RustNumError> {
    println!("🚀 RustNum Arrow 集成演示");
    println!("=" .repeat(50));
    
    // 初始化 RustNum
    rustnum_core::initialize();
    
    // 演示1: 基础数组创建和Arrow转换
    demo_basic_arrow_integration()?;
    
    // 演示2: Arrow计算引擎性能测试
    demo_arrow_compute_performance()?;
    
    // 演示3: Arrow聚合运算
    demo_arrow_aggregations()?;
    
    // 演示4: Arrow比较和过滤操作
    demo_arrow_filtering()?;
    
    // 演示5: 大规模数据处理
    demo_large_scale_processing()?;
    
    println!("\n✅ 所有演示完成！");
    Ok(())
}

fn demo_basic_arrow_integration() -> Result<(), RustNumError> {
    println!("\n📊 演示1: 基础 Arrow 集成");
    println!("-".repeat(30));
    
    let pool = Arc::new(RwLock::new(create_default_pool()));
    
    // 创建测试数组
    let mut array1 = RustArray::new(vec![1000], StorageOrder::RowMajor, pool.clone())?;
    let mut array2 = RustArray::new(vec![1000], StorageOrder::RowMajor, pool)?;
    
    // 填充测试数据
    for i in 0..1000 {
        array1.data_mut()[i] = i as f64;
        array2.data_mut()[i] = (i * 2) as f64;
    }
    
    println!("✓ 创建了两个包含1000个元素的数组");
    
    // 测试Arrow转换（如果启用了arrow特性）
    #[cfg(feature = "arrow")]
    {
        let start = Instant::now();
        let arrow_array1 = array1.to_arrow()?;
        let converted_back = RustArray::from_arrow(arrow_array1)?;
        let duration = start.elapsed();
        
        println!("✓ Arrow 零拷贝转换耗时: {:?}", duration);
        println!("✓ 转换后数据完整性验证: {}", 
            converted_back.data()[0..10] == array1.data()[0..10]);
    }
    
    #[cfg(not(feature = "arrow"))]
    {
        println!("⚠️  Arrow 特性未启用，跳过转换测试");
    }
    
    Ok(())
}

fn demo_arrow_compute_performance() -> Result<(), RustNumError> {
    println!("\n⚡ 演示2: Arrow 计算引擎性能");
    println!("-".repeat(30));
    
    let pool = Arc::new(RwLock::new(create_default_pool()));
    let size = 100_000;
    
    // 创建大型测试数组
    let mut array1 = RustArray::new(vec![size], StorageOrder::RowMajor, pool.clone())?;
    let mut array2 = RustArray::new(vec![size], StorageOrder::RowMajor, pool)?;
    
    // 填充随机数据
    for i in 0..size {
        array1.data_mut()[i] = (i as f64).sin();
        array2.data_mut()[i] = (i as f64).cos();
    }
    
    println!("✓ 创建了两个包含{}个元素的大型数组", size);
    
    // 测试Arrow计算性能
    let operations = vec![
        ("加法", "arrow_add"),
        ("减法", "arrow_sub"),
        ("乘法", "arrow_mul"),
        ("除法", "arrow_div"),
    ];
    
    for (name, _op) in operations {
        let start = Instant::now();
        
        let _result = match name {
            "加法" => array1.arrow_add(&array2)?,
            "减法" => array1.arrow_sub(&array2)?,
            "乘法" => array1.arrow_mul(&array2)?,
            "除法" => array1.arrow_div(&array2)?,
            _ => unreachable!(),
        };
        
        let duration = start.elapsed();
        let throughput = (size as f64) / duration.as_secs_f64() / 1_000_000.0;
        
        println!("  {} 运算: {:?} ({:.2} M元素/秒)", name, duration, throughput);
    }
    
    Ok(())
}

fn demo_arrow_aggregations() -> Result<(), RustNumError> {
    println!("\n📈 演示3: Arrow 聚合运算");
    println!("-".repeat(30));
    
    let pool = Arc::new(RwLock::new(create_default_pool()));
    let mut array = RustArray::new(vec![10000], StorageOrder::RowMajor, pool)?;
    
    // 生成正态分布样本数据
    for i in 0..10000 {
        let x = (i as f64 - 5000.0) / 1000.0;
        array.data_mut()[i] = (-x * x / 2.0).exp(); // 近似正态分布
    }
    
    println!("✓ 生成了10000个正态分布样本");
    
    // 执行各种聚合运算
    let start = Instant::now();
    let sum = array.arrow_sum()?;
    let mean = array.arrow_mean()?;
    let min = array.arrow_min()?;
    let max = array.arrow_max()?;
    let std = array.arrow_std()?;
    let var = array.arrow_var()?;
    let duration = start.elapsed();
    
    println!("\n📊 聚合统计结果:");
    println!("  总和:     {:.6}", sum);
    println!("  均值:     {:.6}", mean);
    println!("  最小值:   {:.6}", min);
    println!("  最大值:   {:.6}", max);
    println!("  标准差:   {:.6}", std);
    println!("  方差:     {:.6}", var);
    println!("  计算耗时: {:?}", duration);
    
    Ok(())
}

fn demo_arrow_filtering() -> Result<(), RustNumError> {
    println!("\n🔍 演示4: Arrow 比较和过滤");
    println!("-".repeat(30));
    
    let pool = Arc::new(RwLock::new(create_default_pool()));
    let mut array1 = RustArray::new(vec![1000], StorageOrder::RowMajor, pool.clone())?;
    let mut array2 = RustArray::new(vec![1000], StorageOrder::RowMajor, pool)?;
    
    // 生成测试数据
    for i in 0..1000 {
        array1.data_mut()[i] = (i as f64).sin();
        array2.data_mut()[i] = 0.5; // 阈值
    }
    
    println!("✓ 生成了正弦波数据和阈值数组");
    
    // 比较运算
    let start = Instant::now();
    let gt_mask = array1.arrow_gt(&array2)?;
    let eq_mask = array1.arrow_eq(&array2)?;
    let lt_mask = array1.arrow_lt(&array2)?;
    let compare_duration = start.elapsed();
    
    let gt_count = gt_mask.iter().filter(|&&x| x).count();
    let eq_count = eq_mask.iter().filter(|&&x| x).count();
    let lt_count = lt_mask.iter().filter(|&&x| x).count();
    
    println!("\n🔢 比较结果统计:");
    println!("  大于阈值: {} 个元素", gt_count);
    println!("  等于阈值: {} 个元素", eq_count);
    println!("  小于阈值: {} 个元素", lt_count);
    println!("  比较耗时: {:?}", compare_duration);
    
    // 过滤操作
    let start = Instant::now();
    let filtered = array1.arrow_filter(&gt_mask)?;
    let filter_duration = start.elapsed();
    
    println!("\n🎯 过滤操作结果:");
    println!("  原始数组大小: {}", array1.data().len());
    println!("  过滤后大小:   {}", filtered.data().len());
    println!("  过滤耗时:     {:?}", filter_duration);
    
    // 索引操作
    let indices: Vec<usize> = (0..100).step_by(10).collect();
    let start = Instant::now();
    let sampled = array1.arrow_take(&indices)?;
    let take_duration = start.elapsed();
    
    println!("\n📍 索引采样结果:");
    println!("  采样索引: {:?}", indices);
    println!("  采样结果大小: {}", sampled.data().len());
    println!("  采样耗时: {:?}", take_duration);
    
    Ok(())
}

fn demo_large_scale_processing() -> Result<(), RustNumError> {
    println!("\n🏭 演示5: 大规模数据处理");
    println!("-".repeat(30));
    
    let pool = Arc::new(RwLock::new(create_default_pool()));
    let size = 1_000_000; // 100万个元素
    
    println!("📦 创建包含{}个元素的大型数组...", size);
    let start = Instant::now();
    
    let mut array = RustArray::new(vec![size], StorageOrder::RowMajor, pool)?;
    
    // 并行填充数据（模拟真实数据加载）
    for i in 0..size {
        array.data_mut()[i] = (i as f64 * 0.001).sin() + (i as f64 * 0.002).cos();
    }
    
    let creation_duration = start.elapsed();
    println!("✓ 数组创建和填充耗时: {:?}", creation_duration);
    
    // 大规模聚合计算
    let start = Instant::now();
    let sum = array.arrow_sum()?;
    let mean = array.arrow_mean()?;
    let std = array.arrow_std()?;
    let aggregation_duration = start.elapsed();
    
    println!("\n📊 大规模聚合计算结果:");
    println!("  数据量:   {} 个元素", size);
    println!("  总和:     {:.6}", sum);
    println!("  均值:     {:.6}", mean);
    println!("  标准差:   {:.6}", std);
    println!("  计算耗时: {:?}", aggregation_duration);
    
    // 计算吞吐量
    let throughput = (size as f64) / aggregation_duration.as_secs_f64() / 1_000_000.0;
    println!("  处理速度: {:.2} M元素/秒", throughput);
    
    // 内存使用情况
    let memory_usage = size * std::mem::size_of::<f64>();
    println!("  内存使用: {:.2} MB", memory_usage as f64 / 1_048_576.0);
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_arrow_integration_demo() {
        // 测试演示代码的基本功能
        rustnum_core::initialize();
        
        let pool = Arc::new(RwLock::new(create_default_pool()));
        let mut array = RustArray::new(vec![100], StorageOrder::RowMajor, pool).unwrap();
        
        for i in 0..100 {
            array.data_mut()[i] = i as f64;
        }
        
        // 测试聚合运算
        let sum = array.arrow_sum().unwrap();
        let expected_sum = (0..100).sum::<usize>() as f64;
        assert!((sum - expected_sum).abs() < 1e-10);
        
        let mean = array.arrow_mean().unwrap();
        let expected_mean = expected_sum / 100.0;
        assert!((mean - expected_mean).abs() < 1e-10);
    }
    
    #[test]
    fn test_arrow_compute_operations() {
        rustnum_core::initialize();
        
        let pool = Arc::new(RwLock::new(create_default_pool()));
        let mut array1 = RustArray::new(vec![10], StorageOrder::RowMajor, pool.clone()).unwrap();
        let mut array2 = RustArray::new(vec![10], StorageOrder::RowMajor, pool).unwrap();
        
        for i in 0..10 {
            array1.data_mut()[i] = i as f64;
            array2.data_mut()[i] = 2.0;
        }
        
        // 测试加法
        let result = array1.arrow_add(&array2).unwrap();
        for i in 0..10 {
            assert!((result.data()[i] - (i as f64 + 2.0)).abs() < 1e-10);
        }
        
        // 测试乘法
        let result = array1.arrow_mul(&array2).unwrap();
        for i in 0..10 {
            assert!((result.data()[i] - (i as f64 * 2.0)).abs() < 1e-10);
        }
    }
}