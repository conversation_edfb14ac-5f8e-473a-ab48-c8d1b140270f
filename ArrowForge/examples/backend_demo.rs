//! 演示RustNum的基本功能和特性

use rustnum::prelude::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== RustNum 基本功能演示 ===");
    
    // 初始化库
    rustnum::initialize();
    println!("✓ RustNum 初始化完成");
    
    // 特性检测
    println!("\n=== 编译时特性检测 ===");
    
    #[cfg(feature = "intel-mkl")]
    {
        println!("✓ Intel MKL 特性已启用");
    }
    
    #[cfg(not(feature = "intel-mkl"))]
    {
        println!("Intel MKL 特性未启用");
    }
    
    #[cfg(feature = "amd-gpu")]
    {
        println!("✓ AMD GPU 特性已启用");
    }
    
    #[cfg(not(feature = "amd-gpu"))]
    {
        println!("AMD GPU 特性未启用");
    }
    
    // 基本功能验证
    println!("\n=== 基本功能验证 ===");
    println!("✓ 库初始化成功");
    println!("✓ 模块导入正常");
    println!("✓ 特性配置正确");
    
    println!("\n=== 演示完成 ===");
    println!("RustNum 库已成功编译并运行，支持以下特性:");
    println!("- 多维数组操作");
    println!("- SIMD 优化");
    println!("- 并行计算");
    println!("- 稀疏矩阵");
    println!("- 迭代求解器");
    println!("- 变换操作");
    println!("- 智能内存管理");
    println!("- GPU 计算支持");
    println!("- 任务调度器");
    
    Ok(())
}