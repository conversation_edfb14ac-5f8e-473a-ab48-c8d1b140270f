//! faer-rs 集成功能演示
//! 
//! 本示例展示如何在 RustNum 项目中使用 faer-rs 高性能线性代数功能
//! 
//! 运行方式：
//! ```bash
//! cargo run --example faer_integration_demo --features faer-comparison
//! ```

#[cfg(feature = "faer-comparison")]
use rustnum_core::faer_integration::*;

#[cfg(feature = "faer-comparison")]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== RustNum faer-rs 集成功能演示 ===");
    
    // 1. 基础矩阵操作演示
    println!("\n1. 基础矩阵操作");
    basic_matrix_operations()?;
    
    // 2. 线性代数操作演示
    println!("\n2. 线性代数操作");
    linear_algebra_operations()?;
    
    // 3. 性能基准测试演示
    println!("\n3. 性能基准测试");
    performance_benchmark_demo();
    
    println!("\n=== 演示完成 ===");
    Ok(())
}

#[cfg(feature = "faer-comparison")]
fn basic_matrix_operations() -> Result<(), Box<dyn std::error::Error>> {
    // 创建矩阵
    let a = FaerMatrix::identity(3);
    let b = FaerMatrix::zeros(3, 3);
    
    println!("创建了 3x3 单位矩阵和零矩阵");
    println!("单位矩阵形状: {:?}", a.shape());
    println!("零矩阵形状: {:?}", b.shape());
    
    // 矩阵乘法
    let result = a.matmul(&a)?;
    println!("单位矩阵自乘结果形状: {:?}", result.shape());
    
    Ok(())
}

#[cfg(feature = "faer-comparison")]
fn linear_algebra_operations() -> Result<(), Box<dyn std::error::Error>> {
    // 创建一个 4x4 单位矩阵进行分解
    let matrix = FaerMatrix::identity(4);
    println!("使用 4x4 单位矩阵进行线性代数操作");
    
    // LU 分解
    let lu = matrix.lu_decomposition()?;
    let det = lu.determinant();
    println!("LU 分解完成，行列式值: {:.6}", det);
    
    // QR 分解
    let qr = matrix.qr_decomposition()?;
    let q = qr.q_matrix();
    let r = qr.r_matrix();
    println!("QR 分解完成");
    println!("Q 矩阵形状: {:?}", q.shape());
    println!("R 矩阵形状: {:?}", r.shape());
    
    // 线性方程组求解
    let b = FaerMatrix::identity(4); // 使用单位矩阵作为右端项
    let solution = lu.solve(&b)?;
    println!("线性方程组求解完成，解的形状: {:?}", solution.shape());
    
    Ok(())
}

#[cfg(feature = "faer-comparison")]
fn performance_benchmark_demo() {
    println!("运行快速性能测试...");
    
    // 运行快速性能测试
    let results = quick_performance_test();
    
    // 打印结果
    print_benchmark_results(&results);
    
    // 分析结果
    println!("\n=== 性能分析 ===");
    for result in &results {
        println!(
            "{} ({}x{}): {:.3} ms",
            result.operation,
            result.matrix_size,
            result.matrix_size,
            result.faer_time_ms
        );
    }
    
    // 计算平均性能
    let matmul_results: Vec<_> = results.iter()
        .filter(|r| r.operation.contains("Multiplication"))
        .collect();
    
    let lu_results: Vec<_> = results.iter()
        .filter(|r| r.operation.contains("LU"))
        .collect();
    
    if !matmul_results.is_empty() {
        let avg_matmul_time: f64 = matmul_results.iter()
            .map(|r| r.faer_time_ms)
            .sum::<f64>() / matmul_results.len() as f64;
        println!("\n矩阵乘法平均时间: {:.3} ms", avg_matmul_time);
    }
    
    if !lu_results.is_empty() {
        let avg_lu_time: f64 = lu_results.iter()
            .map(|r| r.faer_time_ms)
            .sum::<f64>() / lu_results.len() as f64;
        println!("LU 分解平均时间: {:.3} ms", avg_lu_time);
    }
}

#[cfg(feature = "faer-comparison")]
fn advanced_benchmark_demo() {
    println!("\n=== 高级性能基准测试 ===");
    
    // 创建自定义基准测试套件
    let suite = FaerBenchmarkSuite::new(
        vec![50, 100, 200, 500, 1000], // 不同的矩阵大小
        10 // 迭代次数
    );
    
    // 运行完整的基准测试
    let results = suite.run_all_benchmarks();
    
    // 按操作类型分组显示结果
    let mut matmul_results = Vec::new();
    let mut lu_results = Vec::new();
    
    for result in results {
        if result.operation.contains("Multiplication") {
            matmul_results.push(result);
        } else if result.operation.contains("LU") {
            lu_results.push(result);
        }
    }
    
    // 显示矩阵乘法性能趋势
    println!("\n矩阵乘法性能趋势:");
    for result in &matmul_results {
        let ops_per_sec = 1000.0 / result.faer_time_ms;
        println!("  {}x{}: {:.3} ms ({:.1} ops/sec)", 
            result.matrix_size, result.matrix_size, 
            result.faer_time_ms, ops_per_sec);
    }
    
    // 显示 LU 分解性能趋势
    println!("\nLU 分解性能趋势:");
    for result in &lu_results {
        let ops_per_sec = 1000.0 / result.faer_time_ms;
        println!("  {}x{}: {:.3} ms ({:.1} ops/sec)", 
            result.matrix_size, result.matrix_size, 
            result.faer_time_ms, ops_per_sec);
    }
    
    // 计算性能缩放因子
    if matmul_results.len() >= 2 {
        let first = &matmul_results[0];
        let last = &matmul_results[matmul_results.len() - 1];
        let size_ratio = (last.matrix_size as f64 / first.matrix_size as f64).powi(3);
        let time_ratio = last.faer_time_ms / first.faer_time_ms;
        let efficiency = size_ratio / time_ratio;
        
        println!("\n矩阵乘法缩放效率: {:.2} (理想值: 1.0)", efficiency);
        if efficiency > 0.8 {
            println!("✓ 优秀的性能缩放");
        } else if efficiency > 0.6 {
            println!("○ 良好的性能缩放");
        } else {
            println!("△ 性能缩放有待优化");
        }
    }
}

#[cfg(not(feature = "faer-comparison"))]
fn main() {
    eprintln!("错误: 此示例需要启用 'faer-comparison' 特性");
    eprintln!("请使用以下命令运行:");
    eprintln!("cargo run --example faer_integration_demo --features faer-comparison");
    std::process::exit(1);
}