//! RustNum vs faer-rs 性能对比测试示例
//! 
//! 运行命令:
//! cargo run --example faer_benchmark --features "intel-mkl,faer-comparison"

use rustnum_core::benchmarks::FaerComparison;
use std::process;

fn main() {
    println!("🔬 RustNum vs faer-rs 性能对比测试");
    println!("=" .repeat(50));
    
    // 检查是否启用了 faer-comparison 特性
    #[cfg(not(feature = "faer-comparison"))]
    {
        eprintln!("❌ 错误: faer-comparison 特性未启用!");
        eprintln!("请使用以下命令运行:");
        eprintln!("cargo run --example faer_benchmark --features \"intel-mkl,faer-comparison\"");
        process::exit(1);
    }
    
    #[cfg(feature = "faer-comparison")]
    {
        match FaerComparison::run_full_benchmark() {
            Ok(results) => {
                println!("\n🎉 对比测试完成! 共运行了 {} 个测试。", results.len());
                
                // 保存结果到文件
                if let Err(e) = save_results_to_file(&results) {
                    eprintln!("⚠️  保存结果失败: {}", e);
                } else {
                    println!("📄 测试结果已保存到 benchmark_results.txt");
                }
            }
            Err(e) => {
                eprintln!("❌ 对比测试失败: {}", e);
                process::exit(1);
            }
        }
    }
}

#[cfg(feature = "faer-comparison")]
fn save_results_to_file(results: &[rustnum_core::benchmarks::BenchmarkResult]) -> std::io::Result<()> {
    use std::fs::File;
    use std::io::Write;
    
    let mut file = File::create("benchmark_results.txt")?;
    
    writeln!(file, "RustNum vs faer-rs 性能对比测试结果")?;
    writeln!(file, "=" .repeat(50))?;
    writeln!(file, "测试时间: {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"))?;
    writeln!(file)?;
    
    for result in results {
        writeln!(file, "{}", result)?;
    }
    
    writeln!(file)?;
    writeln!(file, "说明:")?;
    writeln!(file, "- 加速比 > 1.0 表示 RustNum 较慢")?;
    writeln!(file, "- 加速比 < 1.0 表示 RustNum 较快")?;
    writeln!(file, "- 精度差表示两个库计算结果的最大差异")?;
    
    Ok(())
}