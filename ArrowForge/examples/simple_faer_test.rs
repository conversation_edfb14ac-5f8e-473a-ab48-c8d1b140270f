//! 简单的 faer-rs 对比测试
//! 这个示例独立运行，不依赖 RustNum 的内部实现

use std::time::Instant;

#[cfg(feature = "faer-comparison")]
use faer::Mat;

fn main() {
    println!("=== faer-rs 基础功能测试 ===");
    
    #[cfg(feature = "faer-comparison")]
    {
        test_faer_matrix_operations();
    }
    
    #[cfg(not(feature = "faer-comparison"))]
    {
        println!("faer-comparison 特性未启用");
        println!("请使用以下命令运行:");
        println!("cargo run --example simple_faer_test --features faer-comparison");
    }
}

#[cfg(feature = "faer-comparison")]
fn test_faer_matrix_operations() {
    println!("\n1. 测试 faer-rs 矩阵创建和基本操作");
    
    // 创建测试矩阵
    let size = 100;
    let a = Mat::from_fn(size, size, |i, j| (i + j) as f64 * 0.01);
    let b = Mat::from_fn(size, size, |i, j| (i * j + 1) as f64 * 0.01);
    
    println!("创建了两个 {}x{} 的矩阵", size, size);
    
    // 矩阵乘法性能测试
    println!("\n2. 矩阵乘法性能测试");
    
    let start = Instant::now();
    let _result = &a * &b;
    let duration = start.elapsed();
    
    println!("faer-rs 矩阵乘法 ({}x{}) 耗时: {:.2}ms", 
             size, size, duration.as_secs_f64() * 1000.0);
    
    // 测试不同大小的矩阵
    println!("\n3. 不同大小矩阵的性能对比");
    
    let sizes = vec![50, 100, 200, 300];
    
    for &test_size in &sizes {
        let test_a = Mat::from_fn(test_size, test_size, |i, j| (i + j) as f64 * 0.01);
        let test_b = Mat::from_fn(test_size, test_size, |i, j| (i * j + 1) as f64 * 0.01);
        
        let start = Instant::now();
        let _result = &test_a * &test_b;
        let duration = start.elapsed();
        
        println!("  {}x{} 矩阵乘法: {:.2}ms", 
                 test_size, test_size, duration.as_secs_f64() * 1000.0);
    }
    
    // 测试其他操作
    println!("\n4. 其他线性代数操作测试");
    
    let test_matrix = Mat::from_fn(50, 50, |i, j| {
        if i == j {
            10.0 + i as f64  // 对角占优
        } else {
            1.0 / (i + j + 1) as f64
        }
    });
    
    // LU 分解
    let start = Instant::now();
    let _lu = test_matrix.partial_piv_lu();
    let lu_time = start.elapsed();
    println!("  LU 分解 (50x50): {:.2}ms", lu_time.as_secs_f64() * 1000.0);
    
    // QR 分解
    let start = Instant::now();
    let _qr = test_matrix.qr();
    let qr_time = start.elapsed();
    println!("  QR 分解 (50x50): {:.2}ms", qr_time.as_secs_f64() * 1000.0);
    
    println!("\n=== faer-rs 测试完成 ===");
    println!("faer-rs 是一个高性能的线性代数库，专为 Rust 设计");
    println!("它提供了优秀的性能和内存安全性");
}