# RustNum faer-rs 集成基准测试配置文件
# 
# 此文件定义了性能基准测试的各种参数和设置

[benchmark]
# 基准测试的矩阵大小范围
matrix_sizes = [50, 100, 200, 500, 1000, 2000]

# 每个测试的迭代次数
iterations = 10

# 预热迭代次数（避免冷启动影响）
warmup_iterations = 3

# 性能回归阈值（百分比）
performance_threshold = 5.0

# 超时设置（秒）
timeout_seconds = 300

[output]
# 输出格式："markdown", "json", "csv"
format = "markdown"

# 是否包含图表
include_plots = false

# 是否导出 CSV 数据
export_csv = true

# 输出目录
output_dir = "benchmark_results"

# 报告文件名模板
report_filename = "faer_benchmark_{timestamp}.md"

[operations]
# 要测试的操作类型
[operations.matrix_multiplication]
enabled = true
description = "矩阵乘法性能测试"
weight = 1.0

[operations.lu_decomposition]
enabled = true
description = "LU 分解性能测试"
weight = 1.5

[operations.qr_decomposition]
enabled = true
description = "QR 分解性能测试"
weight = 1.5

[operations.linear_solve]
enabled = true
description = "线性方程组求解性能测试"
weight = 2.0

[operations.eigenvalue_decomposition]
enabled = false
description = "特征值分解性能测试"
weight = 3.0

[operations.svd]
enabled = false
description = "奇异值分解性能测试"
weight = 3.0

[comparison]
# 是否与其他库进行对比
enable_comparison = true

# 对比库列表
libraries = ["ndarray", "nalgebra"]

# 是否显示相对性能提升
show_speedup = true

# 是否显示内存使用情况
show_memory_usage = false

[environment]
# 环境信息收集
collect_system_info = true
collect_cpu_info = true
collect_memory_info = true
collect_compiler_info = true

# 是否固定 CPU 频率（需要 root 权限）
fix_cpu_frequency = false

# 是否禁用超线程（需要 root 权限）
disable_hyperthreading = false

[advanced]
# 高级设置

# 是否使用多线程测试
test_multithreading = true

# 线程数范围
thread_counts = [1, 2, 4, 8]

# 是否测试不同的 BLAS 后端
test_blas_backends = ["openblas", "intel-mkl"]

# 是否进行内存分配测试
test_memory_allocation = false

# 是否进行缓存友好性测试
test_cache_efficiency = false

# 数值精度测试
test_numerical_accuracy = true

# 精度阈值
accuracy_threshold = 1e-12

[reporting]
# 报告生成设置

# 是否生成详细报告
detailed_report = true

# 是否包含原始数据
include_raw_data = false

# 是否生成性能趋势图
generate_trend_plots = false

# 是否发送邮件通知
email_notifications = false

# 邮件设置（如果启用）
[reporting.email]
smtp_server = "smtp.example.com"
smtp_port = 587
username = "<EMAIL>"
recipients = ["<EMAIL>"]

[ci_integration]
# 持续集成设置

# 是否在 CI 中运行
run_in_ci = true

# CI 中的简化测试（更少的矩阵大小和迭代）
ci_matrix_sizes = [100, 500]
ci_iterations = 3

# 性能回归检测
fail_on_regression = false

# 基准数据存储
benchmark_data_repo = "benchmark-data"

# 是否自动提交结果
auto_commit_results = false