//! 核心功能测试
//! 
//! 测试数组创建和数学运算功能

// 模拟核心模块的简化版本
mod core_test {
    
    #[derive(Debu<PERSON>, <PERSON>lone)]
    pub struct SimpleArray<T> {
        data: Vec<T>,
        shape: Vec<usize>,
    }
    
    impl<T> SimpleArray<T> 
    where 
        T: Copy + Default + std::ops::Add<Output = T> + std::ops::Sub<Output = T> + 
           std::ops::Mul<Output = T> + std::ops::Div<Output = T> + PartialEq
    {
        pub fn zeros(shape: &[usize]) -> Self {
            let size = shape.iter().product();
            Self {
                data: vec![T::default(); size],
                shape: shape.to_vec(),
            }
        }
        
        pub fn ones(shape: &[usize]) -> Self
        where
            T: From<u8>
        {
            let size = shape.iter().product();
            Self {
                data: vec![T::from(1u8); size],
                shape: shape.to_vec(),
            }
        }
        
        pub fn full(shape: &[usize], value: T) -> Self {
            let size = shape.iter().product();
            Self {
                data: vec![value; size],
                shape: shape.to_vec(),
            }
        }
        
        pub fn shape(&self) -> &[usize] {
            &self.shape
        }
        
        pub fn data(&self) -> &[T] {
            &self.data
        }
        
        pub fn len(&self) -> usize {
            self.data.len()
        }
        
        pub fn add(&self, other: &Self) -> Result<Self, String> {
            if self.shape != other.shape {
                return Err(format!("Shape mismatch: {:?} vs {:?}", self.shape, other.shape));
            }
            
            let result_data: Vec<T> = self.data.iter()
                .zip(other.data.iter())
                .map(|(&a, &b)| a + b)
                .collect();
            
            Ok(Self {
                data: result_data,
                shape: self.shape.clone(),
            })
        }
        
        pub fn sub(&self, other: &Self) -> Result<Self, String> {
            if self.shape != other.shape {
                return Err(format!("Shape mismatch: {:?} vs {:?}", self.shape, other.shape));
            }
            
            let result_data: Vec<T> = self.data.iter()
                .zip(other.data.iter())
                .map(|(&a, &b)| a - b)
                .collect();
            
            Ok(Self {
                data: result_data,
                shape: self.shape.clone(),
            })
        }
        
        pub fn mul(&self, other: &Self) -> Result<Self, String> {
            if self.shape != other.shape {
                return Err(format!("Shape mismatch: {:?} vs {:?}", self.shape, other.shape));
            }
            
            let result_data: Vec<T> = self.data.iter()
                .zip(other.data.iter())
                .map(|(&a, &b)| a * b)
                .collect();
            
            Ok(Self {
                data: result_data,
                shape: self.shape.clone(),
            })
        }
        
        pub fn div(&self, other: &Self) -> Result<Self, String> {
            if self.shape != other.shape {
                return Err(format!("Shape mismatch: {:?} vs {:?}", self.shape, other.shape));
            }
            
            let result_data: Vec<T> = self.data.iter()
                .zip(other.data.iter())
                .map(|(&a, &b)| a / b)
                .collect();
            
            Ok(Self {
                data: result_data,
                shape: self.shape.clone(),
            })
        }
        
        pub fn add_scalar(&self, scalar: T) -> Self {
            let result_data: Vec<T> = self.data.iter()
                .map(|&x| x + scalar)
                .collect();
            
            Self {
                data: result_data,
                shape: self.shape.clone(),
            }
        }
        
        pub fn mul_scalar(&self, scalar: T) -> Self {
            let result_data: Vec<T> = self.data.iter()
                .map(|&x| x * scalar)
                .collect();
            
            Self {
                data: result_data,
                shape: self.shape.clone(),
            }
        }
    }
}

fn main() {
    use core_test::SimpleArray;
    
    println!("🚀 RustNum 核心功能测试开始...");
    
    // 测试数组创建
    println!("\n📊 1. 数组创建测试");
    
    let zeros = SimpleArray::<f64>::zeros(&[2, 3]);
    println!("✓ zeros({:?}): len={}, data={:?}", zeros.shape(), zeros.len(), &zeros.data()[..3]);
    assert_eq!(zeros.shape(), &[2, 3]);
    assert_eq!(zeros.len(), 6);
    for &val in zeros.data() {
        assert_eq!(val, 0.0);
    }
    
    let ones = SimpleArray::<f64>::ones(&[2, 2]);
    println!("✓ ones({:?}): len={}, data={:?}", ones.shape(), ones.len(), ones.data());
    assert_eq!(ones.shape(), &[2, 2]);
    for &val in ones.data() {
        assert_eq!(val, 1.0);
    }
    
    let full = SimpleArray::<f64>::full(&[2, 2], 3.14);
    println!("✓ full({:?}, 3.14): data={:?}", full.shape(), full.data());
    for &val in full.data() {
        assert_eq!(val, 3.14);
    }
    
    // 测试数学运算
    println!("\n🧮 2. 数学运算测试");
    
    let a = SimpleArray::<f64>::full(&[2, 2], 2.0);
    let b = SimpleArray::<f64>::full(&[2, 2], 3.0);
    
    let add_result = a.add(&b).unwrap();
    println!("✓ [2,2,2,2] + [3,3,3,3] = {:?}", add_result.data());
    for &val in add_result.data() {
        assert_eq!(val, 5.0);
    }
    
    let sub_result = a.sub(&b).unwrap();
    println!("✓ [2,2,2,2] - [3,3,3,3] = {:?}", sub_result.data());
    for &val in sub_result.data() {
        assert_eq!(val, -1.0);
    }
    
    let mul_result = a.mul(&b).unwrap();
    println!("✓ [2,2,2,2] * [3,3,3,3] = {:?}", mul_result.data());
    for &val in mul_result.data() {
        assert_eq!(val, 6.0);
    }
    
    let div_result = a.div(&b).unwrap();
    println!("✓ [2,2,2,2] / [3,3,3,3] = {:?}", div_result.data());
    for &val in div_result.data() {
        assert!((val - 2.0/3.0).abs() < 1e-10);
    }
    
    // 测试标量运算
    println!("\n📈 3. 标量运算测试");
    
    let scalar_add = a.add_scalar(10.0);
    println!("✓ [2,2,2,2] + 10 = {:?}", scalar_add.data());
    for &val in scalar_add.data() {
        assert_eq!(val, 12.0);
    }
    
    let scalar_mul = a.mul_scalar(5.0);
    println!("✓ [2,2,2,2] * 5 = {:?}", scalar_mul.data());
    for &val in scalar_mul.data() {
        assert_eq!(val, 10.0);
    }
    
    // 测试错误处理
    println!("\n❌ 4. 错误处理测试");
    
    let c = SimpleArray::<f64>::ones(&[3, 3]);
    let shape_error = a.add(&c);
    println!("✓ 形状不匹配错误: {:?}", shape_error);
    assert!(shape_error.is_err());
    
    println!("\n🎉 所有测试通过！");
    println!("✅ 数组创建功能正常");
    println!("✅ 数学运算功能正常");
    println!("✅ 错误处理功能正常");
    println!("✅ 核心功能验证成功！");
}
