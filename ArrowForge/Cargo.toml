[package]
name = "rustnum"
version = "0.1.0"
edition = "2021"
authors = ["RustNum Team"]
repository = "https://github.com/rustnum/rustnum"
license = "MIT OR Apache-2.0"
description = "A high-performance scientific computing library for Rust"

[workspace]
resolver = "2"
members = [
    "crates/rustnum-core",
    "crates/rustnum-python",
    "crates/rustnum-distributed",
    "crates/rustnum-benches"
, "faer_benchmark_standalone"]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["RustNum Team"]
repository = "https://github.com/rustnum/rustnum"
license = "MIT OR Apache-2.0"

[dependencies]
rustnum-core = { path = "crates/rustnum-core" }
rustnum-distributed = { path = "crates/rustnum-distributed", optional = true }

# 根包的特性配置
[features]
default = ["rustnum-core/default"]

# BLAS后端选择
openblas = ["rustnum-core/openblas"]
intel-mkl = ["rustnum-core/intel-mkl"]
system-blas = ["rustnum-core/system-blas"]

# GPU支持
amd-gpu = ["rustnum-core/amd-gpu"]
cuda-gpu = ["rustnum-core/cuda-gpu"]
opencl = ["rustnum-core/opencl"]

# 核心功能
simd = ["rustnum-core/simd"]
parallel = ["rustnum-core/parallel"]
ml_scheduler = ["rustnum-core/ml_scheduler"]
fft = ["rustnum-core/fft"]
stats = ["rustnum-core/stats"]
optimization = ["rustnum-core/optimization"]

# 高性能线性代数
faer-comparison = ["rustnum-core/faer-comparison"]
distributed = ["rustnum-core/distributed", "dep:rustnum-distributed"]

# 完整功能集
full = [
    "simd", "parallel", "intel-mkl", "amd-gpu", 
    "ml_scheduler", "fft", "stats", "optimization", "distributed"
]
