use pyo3::prelude::*;
use pyo3::Bound;
use rustnum_core::array::{RustArray, StorageOrder, ArrowIntegration, ArrowCompute};
use rustnum_core::memory::create_default_pool;
use parking_lot::RwLock;
use std::sync::Arc;

#[cfg(feature = "arrow")]
use arrow::array::ArrayRef;

#[pymodule]
fn rustnum(m: &Bound<'_, PyModule>) -> PyResult<()> {
    // 初始化RustNum
    rustnum_core::initialize();
    
    // 注册Python类型
    m.add_class::<PyRustArray>()?;
    Ok(())
}

#[pyclass(name = "Array", unsendable)]
struct PyRustArray {
    inner: rustnum_core::array::RustArray<f64>,
}

#[pymethods]
impl PyRustArray {
    #[new]
    fn new(shape: Vec<usize>) -> PyResult<Self> {
        let pool = Arc::new(RwLock::new(create_default_pool()));
        let array = RustArray::new(shape, StorageOrder::RowMajor, pool)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
            
        Ok(Self { inner: array })
    }

    #[getter]
    fn shape(&self) -> Vec<usize> {
        self.inner.shape().to_vec()
    }
    
    #[getter]
    fn size(&self) -> usize {
        self.inner.data().len()
    }
    
    /// 从Arrow数组创建RustArray
    #[cfg(feature = "arrow")]
    #[staticmethod]
    fn from_arrow(arrow_array: &PyAny) -> PyResult<Self> {
        // 这里需要实现从Python Arrow数组到Rust Arrow数组的转换
        // 暂时返回错误，等待pyo3-arrow的完整实现
        Err(PyErr::new::<pyo3::exceptions::PyNotImplementedError, _>(
            "Arrow integration not yet fully implemented"
        ))
    }
    
    /// 转换为Arrow数组
    #[cfg(feature = "arrow")]
    fn to_arrow(&self) -> PyResult<PyObject> {
        // 这里需要实现从Rust Arrow数组到Python Arrow数组的转换
        // 暂时返回错误，等待pyo3-arrow的完整实现
        Err(PyErr::new::<pyo3::exceptions::PyNotImplementedError, _>(
            "Arrow integration not yet fully implemented"
        ))
    }
    
    /// 使用Arrow计算引擎进行加法
    fn arrow_add(&self, other: &PyRustArray) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_add(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// 使用Arrow计算引擎进行减法
    fn arrow_sub(&self, other: &PyRustArray) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_sub(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// 使用Arrow计算引擎进行乘法
    fn arrow_mul(&self, other: &PyRustArray) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_mul(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// 使用Arrow计算引擎进行除法
    fn arrow_div(&self, other: &PyRustArray) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_div(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// Arrow聚合运算
    fn arrow_sum(&self) -> PyResult<f64> {
        self.inner.arrow_sum()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_mean(&self) -> PyResult<f64> {
        self.inner.arrow_mean()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_min(&self) -> PyResult<f64> {
        self.inner.arrow_min()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_max(&self) -> PyResult<f64> {
        self.inner.arrow_max()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_std(&self) -> PyResult<f64> {
        self.inner.arrow_std()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_var(&self) -> PyResult<f64> {
        self.inner.arrow_var()
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    /// Arrow比较运算
    fn arrow_eq(&self, other: &PyRustArray) -> PyResult<Vec<bool>> {
        self.inner.arrow_eq(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_gt(&self, other: &PyRustArray) -> PyResult<Vec<bool>> {
        self.inner.arrow_gt(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    fn arrow_lt(&self, other: &PyRustArray) -> PyResult<Vec<bool>> {
        self.inner.arrow_lt(&other.inner)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))
    }
    
    /// Arrow过滤操作
    fn arrow_filter(&self, mask: Vec<bool>) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_filter(&mask)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// Arrow索引操作
    fn arrow_take(&self, indices: Vec<usize>) -> PyResult<PyRustArray> {
        let result = self.inner.arrow_take(&indices)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        Ok(PyRustArray { inner: result })
    }
    
    /// 获取数据的Python表示
    fn to_list(&self) -> Vec<f64> {
        self.inner.data().to_vec()
    }
    
    /// 从列表创建数组
    #[staticmethod]
    fn from_list(data: Vec<f64>, shape: Vec<usize>) -> PyResult<Self> {
        let total_size: usize = shape.iter().product();
        if data.len() != total_size {
            return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
                format!("Data length {} doesn't match shape size {}", data.len(), total_size)
            ));
        }
        
        let pool = Arc::new(RwLock::new(create_default_pool()));
        let mut array = RustArray::new(shape, StorageOrder::RowMajor, pool)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(e.to_string()))?;
        
        array.data_mut().copy_from_slice(&data);
        Ok(PyRustArray { inner: array })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_array_creation_from_python() {
        Python::with_gil(|_py| {
            let array = PyRustArray::new(vec![2, 3]).unwrap();
            assert_eq!(array.shape(), vec![2, 3]);
        });
    }
}
