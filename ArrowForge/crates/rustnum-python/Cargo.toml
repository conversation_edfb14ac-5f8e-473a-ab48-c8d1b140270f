[package]
name = "rustnum-python"
version = "0.1.0"
edition = "2021"

[lib]
name = "rustnum"
crate-type = ["cdylib"]

[dependencies]
rustnum-core = { path = "../rustnum-core", features = ["arrow", "arrow-compute", "faer-comparison"] }
pyo3 = { version = "0.25.1", features = ["extension-module", "abi3-py38"] }
numpy = { version = "0.25.0", features = ["nalgebra"] }
parking_lot = "0.12"
# pyo3-arrow = { version = "0.3.0", optional = true }
arrow = { version = "55.1.0", optional = true }

[features]
default = ["pyo3/extension-module"]
arrow = ["dep:arrow"]
arrow-full = ["arrow"]
