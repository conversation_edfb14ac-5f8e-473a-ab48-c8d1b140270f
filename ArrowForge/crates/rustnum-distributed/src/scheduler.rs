use async_trait::async_trait;
use dashmap::DashMap;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tokio::time::{Duration, Instant};
use tracing::{info, warn, error, debug};
use std::collections::{BinaryHeap, HashMap, VecDeque};
use std::cmp::Ordering;
use arrow::datatypes::Schema;
use arrow::record_batch::RecordBatch;
use crate::{DistributedTask, ResourceManager, ResourceRequirements, ExecutionContext, ResourceStatus, DistributedError, Result};
use crate::resource::DynamicResourceManager;
use serde::{Serialize, Deserialize};

/// 模拟任务实现
#[derive(Debug, Clone)]
pub struct MockTask {
    id: String,
    work_duration: Duration,
}

#[async_trait]
impl DistributedTask for MockTask {
    fn task_id(&self) -> &str {
        &self.id
    }

    fn priority(&self) -> u32 {
        1
    }

    fn estimate_resources(&self) -> ResourceRequirements {
        ResourceRequirements {
            cpu_cores: 1.0,
            memory_mb: 1024,
            gpu_memory_mb: None,
            network_bandwidth_mbps: 100,
        }
    }

    async fn execute(&self, _context: &ExecutionContext) -> Result<RecordBatch> {
        tokio::time::sleep(self.work_duration).await;
        Ok(RecordBatch::new_empty(Arc::new(Schema::empty())))
    }
}

/// 具体任务类型枚举
#[derive(Debug, Clone)]
pub enum ConcreteTask {
    Mock(MockTask),
    // 暂时移除 FailingTask，因为 fault_tolerance 模块中未找到该类型
}

#[async_trait]
impl DistributedTask for ConcreteTask {
    fn task_id(&self) -> &str {
        match self {
            ConcreteTask::Mock(task) => task.task_id(),
        }
    }

    fn priority(&self) -> u32 {
        match self {
            ConcreteTask::Mock(task) => task.priority(),
        }
    }

    fn estimate_resources(&self) -> ResourceRequirements {
        match self {
            ConcreteTask::Mock(task) => task.estimate_resources(),
        }
    }

    async fn execute(&self, context: &ExecutionContext) -> Result<RecordBatch> {
        match self {
            ConcreteTask::Mock(task) => task.execute(context).await,
        }
    }
}

/// 任务优先级队列中的任务包装
#[derive(Debug, Clone)]
struct PrioritizedTask {
    priority: u32,
    submit_time: Instant,
    task: Arc<ConcreteTask>,
}



impl PartialEq for PrioritizedTask {
    fn eq(&self, other: &Self) -> bool {
        self.priority == other.priority && self.submit_time == other.submit_time
    }
}

impl Eq for PrioritizedTask {}

impl PartialOrd for PrioritizedTask {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for PrioritizedTask {
    fn cmp(&self, other: &Self) -> Ordering {
        // 优先级越高越优先，时间越早越优先
        other.priority.cmp(&self.priority)
            .then_with(|| self.submit_time.cmp(&other.submit_time))
    }
}

/// 任务状态追踪
#[derive(Debug, Clone)]
struct TaskStatus {
    state: TaskState,
    start_time: Option<Instant>,
    completion_time: Option<Instant>,
    attempts: u32,
    last_error: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum TaskState {
    Queued,
    Running,
    Completed,
    Failed,
}

/// 智能任务调度器
pub struct SmartTaskScheduler {
    // 资源管理器
    resource_manager: Arc<crate::resource::DynamicResourceManager>,
    // 任务优先级队列
    task_queue: Arc<RwLock<BinaryHeap<PrioritizedTask>>>,
    // 任务状态表
    task_status: DashMap<String, TaskStatus>,
    // 任务执行历史（用于优化）
    execution_history: Arc<RwLock<HashMap<String, Vec<ExecutionRecord>>>>,
    // 工作线程数量
    worker_count: usize,
    // 调度统计
    stats: Arc<RwLock<SchedulerStats>>,
}

/// 任务执行记录
#[derive(Debug, Clone)]
struct ExecutionRecord {
    task_id: String,
    start_time: Instant,
    duration: Duration,
    cpu_usage: f32,
    memory_usage: u64,
    success: bool,
}

/// 调度器统计信息
#[derive(Debug, Default, Clone)]
struct SchedulerStats {
    total_tasks: u64,
    completed_tasks: u64,
    failed_tasks: u64,
    total_execution_time: Duration,
    average_wait_time: Duration,
}

impl SmartTaskScheduler {
    pub fn new(resource_manager: Arc<crate::resource::DynamicResourceManager>, worker_count: usize) -> Self {
        Self {
            resource_manager,
            task_queue: Arc::new(RwLock::new(BinaryHeap::new())),
            task_status: DashMap::new(),
            execution_history: Arc::new(RwLock::new(HashMap::new())),
            worker_count,
            stats: Arc::new(RwLock::new(SchedulerStats::default())),
        }
    }

    /// 提交任务
    pub async fn submit_task(&self, task: Arc<ConcreteTask>) -> Result<()> {
        let prioritized_task = PrioritizedTask {
            priority: task.priority(),
            submit_time: Instant::now(),
            task,
        };

        // 初始化任务状态
        self.task_status.insert(prioritized_task.task.task_id().to_string(), TaskStatus {
            state: TaskState::Queued,
            start_time: None,
            completion_time: None,
            attempts: 0,
            last_error: None,
        });

        // 加入队列
        self.task_queue.write().await.push(prioritized_task.clone());
        
        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_tasks += 1;
        }
        
        debug!("任务已提交: {}", prioritized_task.task.task_id());
        
        // 立即尝试执行任务
        self.execute_next_task().await;
        
        Ok(())
    }

    /// 启动调度器
    pub async fn start(&self) -> Result<()> {
        info!("启动调度器，工作线程数: {}", self.worker_count);
        
        // 启动工作线程
        for i in 0..self.worker_count {
            let task_queue = Arc::clone(&self.task_queue);
            let resource_manager = Arc::clone(&self.resource_manager);
            let task_status = self.task_status.clone();
            let execution_history = Arc::clone(&self.execution_history);
            let stats = Arc::clone(&self.stats);

            tokio::spawn(async move {
                info!("工作线程 {} 已启动", i);
                loop {
                    // 从任务队列获取任务
                    let task = {
                        let mut queue = task_queue.write().await;
                        queue.pop().map(|pt| pt.task)
                    };
                    
                    if let Some(task) = task {
                    let start_time = Instant::now();
                    let task_id = task.task_id().to_string();

                    // 更新任务状态
                    if let Some(mut status) = task_status.get_mut(&task_id) {
                        status.state = TaskState::Running;
                        status.start_time = Some(start_time);
                    }

                    // 获取资源需求
                    let requirements = task.estimate_resources();
                    match resource_manager.acquire_resources(&requirements).await {
                        Ok(token) => {
                            // 执行任务
                            let schema = Arc::new(arrow::datatypes::Schema::new(vec![] as Vec<arrow::datatypes::Field>));
                            let result = task.execute(&ExecutionContext {
                                schema,
                                partition_id: 0,
                                total_partitions: 1,
                                resource_manager: resource_manager.clone(),
                            }).await;

                            // 释放资源
                            resource_manager.release_resources(token).await;

                            // 记录执行结果
                            let duration = start_time.elapsed();
                            let success = result.is_ok();
                            
                            if let Some(mut status) = task_status.get_mut(&task_id) {
                                match result {
                                    Ok(_) => {
                                        status.state = TaskState::Completed;
                                        status.completion_time = Some(Instant::now());
                                    }
                                    Err(ref e) => {
                                        status.state = TaskState::Failed;
                                        status.last_error = Some(e.to_string());
                                        error!("任务执行失败: {} - {}", task_id, e);
                                    }
                                }
                            }

                            // 更新执行历史
                            let record = ExecutionRecord {
                                task_id: task_id.clone(),
                                start_time,
                                duration,
                                cpu_usage: requirements.cpu_cores,
                                memory_usage: requirements.memory_mb,
                                success,
                            };

                            let mut history = execution_history.write().await;
                            history.entry(task_id.clone())
                                .or_insert_with(Vec::new)
                                .push(record);
                            
                            // 更新统计信息
                            let mut stats_guard = stats.write().await;
                            stats_guard.total_tasks += 1;
                            drop(stats_guard);
                        }
                        Err(e) => {
                            error!("资源分配失败: {} - {}", task_id, e);
                            if let Some(mut status) = task_status.get_mut(&task_id) {
                                status.state = TaskState::Failed;
                                status.last_error = Some(e.to_string());
                            }
                        }
                    }
                    } else {
                        // 如果没有任务，等待一段时间
                        tokio::time::sleep(Duration::from_millis(100)).await;
                    }
                }
            });
        }

        Ok(())
    }

    async fn execute_next_task(&self) {
        let mut queue = self.task_queue.write().await;
        if let Some(prioritized_task) = queue.pop() {
            let task_id = prioritized_task.task.task_id().to_string();
            drop(queue); // 释放锁
            
            // 更新任务状态为运行中
            if let Some(mut status) = self.task_status.get_mut(&task_id) {
                status.state = TaskState::Running;
                status.start_time = Some(Instant::now());
                status.attempts += 1;
            }
            
            debug!("开始执行任务: {}", task_id);
            
            // 执行任务（简化版本，不使用资源管理器）
             let schema = Arc::new(arrow::datatypes::Schema::new(vec![] as Vec<arrow::datatypes::Field>));
             let result = prioritized_task.task.execute(&ExecutionContext {
                 schema,
                 partition_id: 0,
                 total_partitions: 1,
                 resource_manager: self.resource_manager.clone(),
             }).await;

             match result {
                 Ok(_) => {
                     // 任务成功完成
                     if let Some(mut status) = self.task_status.get_mut(&task_id) {
                         status.state = TaskState::Completed;
                         status.completion_time = Some(Instant::now());
                     }
                     
                     // 更新统计信息
                     let mut stats = self.stats.write().await;
                     stats.completed_tasks += 1;
                     
                     debug!("任务执行成功: {}", task_id);
                 }
                 Err(e) => {
                     // 任务执行失败
                     if let Some(mut status) = self.task_status.get_mut(&task_id) {
                         status.state = TaskState::Failed;
                         status.completion_time = Some(Instant::now());
                         status.last_error = Some(e.to_string());
                     }
                     
                     // 更新统计信息
                     let mut stats = self.stats.write().await;
                     stats.failed_tasks += 1;
                     
                     error!("任务执行失败: {} - {}", task_id, e);
                 }
             }
        }
    }

    /// 获取任务状态
    pub fn get_task_status(&self, task_id: &str) -> Option<TaskStatus> {
        self.task_status.get(task_id).map(|s| s.clone())
    }

    /// 更新调度器统计信息
    async fn update_stats(&self) {
        let mut stats = self.stats.write().await;
        let total_tasks = self.task_status.len() as u64;
        let completed_tasks = self.task_status
            .iter()
            .filter(|r| r.state == TaskState::Completed)
            .count() as u64;
        let failed_tasks = self.task_status
            .iter()
            .filter(|r| r.state == TaskState::Failed)
            .count() as u64;

        stats.total_tasks = total_tasks;
        stats.completed_tasks = completed_tasks;
        stats.failed_tasks = failed_tasks;

        // 计算平均等待时间
        let mut total_wait_time = Duration::default();
        let mut count = 0;
        for record in self.task_status.iter() {
            if let (Some(start), Some(completion)) = (record.start_time, record.completion_time) {
                total_wait_time += completion - start;
                count += 1;
            }
        }
        
        if count > 0 {
            stats.average_wait_time = total_wait_time / count;
        }
    }

    /// 获取调度器统计信息
    pub async fn get_stats(&self) -> SchedulerStats {
        self.stats.read().await.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;



    #[tokio::test]
    async fn test_task_scheduling() {
        let resource_manager = Arc::new(DynamicResourceManager::new(ResourceStatus {
            total_cpu_cores: 4.0,
            available_cpu_cores: 4.0,
            total_memory_mb: 8192,
            available_memory_mb: 8192,
            total_gpu_memory_mb: None,
            available_gpu_memory_mb: None,
            network_bandwidth_mbps: 1000,
        }));

        let scheduler = SmartTaskScheduler::new(resource_manager, 2);
        scheduler.start().await.unwrap();

        // 提交测试任务
        let task1 = Arc::new(ConcreteTask::Mock(MockTask {
            id: "task1".to_string(),
            work_duration: Duration::from_millis(100),
        }));
        let task2 = Arc::new(ConcreteTask::Mock(MockTask {
            id: "task2".to_string(),
            work_duration: Duration::from_millis(200),
        }));

        scheduler.submit_task(task1.clone()).await.unwrap();
        scheduler.submit_task(task2.clone()).await.unwrap();

        // 等待任务完成
        tokio::time::sleep(Duration::from_secs(1)).await;

        // 验证任务状态
        assert_eq!(scheduler.get_task_status("task1").unwrap().state, TaskState::Completed);
        assert_eq!(scheduler.get_task_status("task2").unwrap().state, TaskState::Completed);

        // 验证统计信息
        let stats = scheduler.get_stats().await;
        assert_eq!(stats.total_tasks, 2);
        assert_eq!(stats.completed_tasks, 2);
        assert_eq!(stats.failed_tasks, 0);
    }
}
