use crate::{ResourceManager, ResourceRequirements, ResourceStatus, ResourceToken, Result, DistributedError};
use async_trait::async_trait;
use dashmap::DashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{info, warn, error};

/// 动态资源管理器实现
pub struct DynamicResourceManager {
    // 当前可用资源状态
    available_resources: RwLock<ResourceStatus>,
    // 活跃资源令牌
    active_tokens: DashMap<String, ResourceToken>,
    // 令牌计数器
    token_counter: AtomicU64,
    // 资源利用率历史记录
    utilization_history: RwLock<Vec<(Instant, ResourceStatus)>>,
    // 资源预测模型
    predictor: RwLock<ResourcePredictor>,
}

impl DynamicResourceManager {
    pub fn new(initial_resources: ResourceStatus) -> Self {
        Self {
            available_resources: RwLock::new(initial_resources.clone()),
            active_tokens: DashMap::new(),
            token_counter: AtomicU64::new(0),
            utilization_history: RwLock::new(Vec::new()),
            predictor: RwLock::new(ResourcePredictor::new(initial_resources)),
        }
    }

    // 更新资源利用率历史
    async fn update_history(&self) {
        let current_status = self.available_resources.read().await.clone();
        let now = Instant::now();
        let mut history = self.utilization_history.write().await;
        history.push((now, current_status));

        // 清理超过1小时的历史数据
        let one_hour_ago = now - std::time::Duration::from_secs(3600);
        history.retain(|(timestamp, _)| *timestamp > one_hour_ago);
    }

    // 预测未来资源需求
    async fn predict_future_demand(&self, window_secs: u64) -> ResourceStatus {
        let predictor = self.predictor.read().await;
        predictor.predict_demand(window_secs)
    }

    // 检查资源压力
    async fn check_resource_pressure(&self) -> ResourcePressure {
        let current = self.available_resources.read().await;
        let predicted = self.predict_future_demand(300).await; // 预测5分钟后

        ResourcePressure {
            cpu_pressure: (current.total_cpu_cores - current.available_cpu_cores) / current.total_cpu_cores,
            memory_pressure: (current.total_memory_mb - current.available_memory_mb) as f32 / current.total_memory_mb as f32,
            gpu_pressure: current.total_gpu_memory_mb.map(|total| {
                let available = current.available_gpu_memory_mb.unwrap_or(0);
                (total - available) as f32 / total as f32
            }),
            predicted_pressure: (predicted.total_cpu_cores - predicted.available_cpu_cores) / predicted.total_cpu_cores,
        }
    }
}

#[async_trait]
impl ResourceManager for DynamicResourceManager {
    async fn acquire_resources(&self, requirements: &ResourceRequirements) -> Result<ResourceToken> {
        let mut resources = self.available_resources.write().await;
        
        // 检查资源是否足够
        if requirements.cpu_cores > resources.available_cpu_cores ||
           requirements.memory_mb > resources.available_memory_mb ||
           (requirements.gpu_memory_mb.is_some() && 
            requirements.gpu_memory_mb > resources.available_gpu_memory_mb) {
            
            // 计算当前资源压力（避免死锁，直接使用当前资源状态）
            let cpu_pressure = (resources.total_cpu_cores - resources.available_cpu_cores) / resources.total_cpu_cores;
            let memory_pressure = (resources.total_memory_mb - resources.available_memory_mb) as f32 / resources.total_memory_mb as f32;
            
            error!("资源不足: CPU压力 {:.2}, 内存压力 {:.2}", 
                   cpu_pressure, memory_pressure);
                   
            return Err(DistributedError::ResourceError(
                format!("资源不足: 需要 {:.1} CPU核心, {}MB内存", 
                        requirements.cpu_cores, requirements.memory_mb)
            ));
        }

        // 分配资源
        resources.available_cpu_cores -= requirements.cpu_cores;
        resources.available_memory_mb -= requirements.memory_mb;
        if let Some(gpu_mem) = requirements.gpu_memory_mb {
            resources.available_gpu_memory_mb = 
                resources.available_gpu_memory_mb.map(|mem| mem - gpu_mem);
        }

        // 创建资源令牌
        let token = ResourceToken {
            id: format!("token-{}", self.token_counter.fetch_add(1, Ordering::SeqCst)),
            requirements: requirements.clone(),
            acquired_at: Instant::now(),
        };

        // 记录令牌
        self.active_tokens.insert(token.id.clone(), token.clone());
        
        info!("资源分配成功: token {}", token.id);
        Ok(token)
    }

    async fn release_resources(&self, token: ResourceToken) {
        let mut resources = self.available_resources.write().await;
        
        // 恢复资源
        resources.available_cpu_cores += token.requirements.cpu_cores;
        resources.available_memory_mb += token.requirements.memory_mb;
        if let Some(gpu_mem) = token.requirements.gpu_memory_mb {
            resources.available_gpu_memory_mb = 
                resources.available_gpu_memory_mb.map(|mem| mem + gpu_mem);
        }

        // 移除令牌记录
        self.active_tokens.remove(&token.id);
        
        info!("资源释放成功: token {}", token.id);
    }

    async fn available_resources(&self) -> ResourceStatus {
        self.available_resources.read().await.clone()
    }
}

/// 资源压力指标
#[derive(Debug)]
struct ResourcePressure {
    cpu_pressure: f32,
    memory_pressure: f32,
    gpu_pressure: Option<f32>,
    predicted_pressure: f32,
}

/// 资源需求预测器
struct ResourcePredictor {
    base_resources: ResourceStatus,
    // 使用简单的指数移动平均进行预测
    cpu_ema: f32,
    memory_ema: f32,
    gpu_ema: Option<f32>,
    alpha: f32, // 平滑因子
}

impl ResourcePredictor {
    fn new(base_resources: ResourceStatus) -> Self {
        Self {
            base_resources,
            cpu_ema: 0.0,
            memory_ema: 0.0,
            gpu_ema: None,
            alpha: 0.2, // 经验值，可调整
        }
    }

    // 更新预测模型
    fn update(&mut self, usage: &ResourceStatus) {
        let cpu_usage = (self.base_resources.total_cpu_cores - usage.available_cpu_cores) 
                       / self.base_resources.total_cpu_cores;
        self.cpu_ema = self.alpha * cpu_usage + (1.0 - self.alpha) * self.cpu_ema;

        let memory_usage = (self.base_resources.total_memory_mb - usage.available_memory_mb) as f32
                          / self.base_resources.total_memory_mb as f32;
        self.memory_ema = self.alpha * memory_usage + (1.0 - self.alpha) * self.memory_ema;

        if let (Some(total), Some(available)) = (self.base_resources.total_gpu_memory_mb, 
                                               usage.available_gpu_memory_mb) {
            let gpu_usage = (total - available) as f32 / total as f32;
            self.gpu_ema = Some(self.alpha * gpu_usage + 
                              (1.0 - self.alpha) * self.gpu_ema.unwrap_or(0.0));
        }
    }

    // 预测未来资源需求
    fn predict_demand(&self, _window_secs: u64) -> ResourceStatus {
        let predicted_cpu = self.base_resources.total_cpu_cores * (1.0 - self.cpu_ema);
        let predicted_memory = (self.base_resources.total_memory_mb as f32 
                              * (1.0 - self.memory_ema)) as u64;
        let predicted_gpu = self.base_resources.total_gpu_memory_mb.map(|total| {
            (total as f32 * (1.0 - self.gpu_ema.unwrap_or(0.0))) as u64
        });

        ResourceStatus {
            total_cpu_cores: self.base_resources.total_cpu_cores,
            available_cpu_cores: predicted_cpu,
            total_memory_mb: self.base_resources.total_memory_mb,
            available_memory_mb: predicted_memory,
            total_gpu_memory_mb: self.base_resources.total_gpu_memory_mb,
            available_gpu_memory_mb: predicted_gpu,
            network_bandwidth_mbps: self.base_resources.network_bandwidth_mbps,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_resource_allocation() {
        let initial_resources = ResourceStatus {
            total_cpu_cores: 8.0,
            available_cpu_cores: 8.0,
            total_memory_mb: 16384,
            available_memory_mb: 16384,
            total_gpu_memory_mb: Some(8192),
            available_gpu_memory_mb: Some(8192),
            network_bandwidth_mbps: 1000,
        };

        let manager = DynamicResourceManager::new(initial_resources);

        // 测试资源分配
        let requirements = ResourceRequirements {
            cpu_cores: 2.0,
            memory_mb: 4096,
            gpu_memory_mb: Some(2048),
            network_bandwidth_mbps: 100,
        };

        let token = manager.acquire_resources(&requirements).await.unwrap();
        let status = manager.available_resources().await;

        assert_eq!(status.available_cpu_cores, 6.0);
        assert_eq!(status.available_memory_mb, 12288);
        assert_eq!(status.available_gpu_memory_mb, Some(6144));

        // 测试资源释放
        manager.release_resources(token).await;
        let final_status = manager.available_resources().await;

        assert_eq!(final_status.available_cpu_cores, 8.0);
        assert_eq!(final_status.available_memory_mb, 16384);
        assert_eq!(final_status.available_gpu_memory_mb, Some(8192));
    }
}
