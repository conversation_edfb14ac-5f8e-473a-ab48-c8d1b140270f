use crate::{DistributedTask, Result, DistributedError};
use async_trait::async_trait;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use tracing::{info, warn, error};

/// 任务重试策略
#[derive(Debug, Clone)]
pub enum RetryPolicy {
    NoRetry,
    Fixed { max_attempts: u32 },
    ExponentialBackoff { max_attempts: u32, base_delay_ms: u64 },
}

/// 检查点信息
#[derive(Debug, Clone)]
pub struct Checkpoint {
    pub task_id: String,
    pub state: Vec<u8>, // 可序列化的任务状态
    pub timestamp: u64,
}

/// 容错与恢复管理器
pub struct FaultToleranceManager {
    retry_policies: RwLock<HashMap<String, RetryPolicy>>,
    checkpoints: RwLock<HashMap<String, Checkpoint>>,
}

impl FaultToleranceManager {
    pub fn new() -> Self {
        Self {
            retry_policies: RwLock::new(HashMap::new()),
            checkpoints: RwLock::new(HashMap::new()),
        }
    }

    /// 设置任务重试策略
    pub async fn set_retry_policy(&self, task_id: &str, policy: RetryPolicy) {
        self.retry_policies.write().await.insert(task_id.to_string(), policy);
    }

    /// 获取任务重试策略
    pub async fn get_retry_policy(&self, task_id: &str) -> RetryPolicy {
        self.retry_policies.read().await.get(task_id).cloned().unwrap_or(RetryPolicy::NoRetry)
    }

    /// 保存检查点
    pub async fn save_checkpoint(&self, checkpoint: Checkpoint) {
        self.checkpoints.write().await.insert(checkpoint.task_id.clone(), checkpoint);
    }

    /// 恢复检查点
    pub async fn load_checkpoint(&self, task_id: &str) -> Option<Checkpoint> {
        self.checkpoints.read().await.get(task_id).cloned()
    }

    /// 清理过期检查点
    pub async fn cleanup_checkpoints(&self, max_age_secs: u64) {
        let now = std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs();
        self.checkpoints.write().await.retain(|_, cp| now - cp.timestamp < max_age_secs);
    }

    /// 任务执行带重试与检查点
    pub async fn execute_with_fault_tolerance<T: DistributedTask + ?Sized>(
        &self,
        task: &T,
        context: &crate::ExecutionContext,
    ) -> Result<crate::RecordBatch> {
        let policy = self.get_retry_policy(task.task_id()).await;
        let mut attempts = 0;
        let mut delay = 100;
        loop {
            attempts += 1;
            match task.execute(context).await {
                Ok(result) => {
                    info!("任务{}执行成功，尝试次数{}", task.task_id(), attempts);
                    return Ok(result);
                }
                Err(e) => {
                    error!("任务{}执行失败: {}，第{}次尝试", task.task_id(), e, attempts);
                    // 检查是否可重试
                    match &policy {
                        RetryPolicy::NoRetry => return Err(e),
                        RetryPolicy::Fixed { max_attempts } => {
                            if attempts >= *max_attempts {
                                return Err(e);
                            }
                        }
                        RetryPolicy::ExponentialBackoff { max_attempts, base_delay_ms: _ } => {
                            if attempts >= *max_attempts {
                                return Err(e);
                            }
                            tokio::time::sleep(std::time::Duration::from_millis(delay)).await;
                            delay *= 2;
                            if delay > 10_000 { delay = 10_000; }
                        }
                    }
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{ResourceRequirements, ExecutionContext};
    use arrow::record_batch::RecordBatch;
    use arrow::datatypes::Schema;
    use std::sync::Arc;

    #[derive(Debug)]
    pub struct FailingTask {
        id: String,
        fail_times: u32,
        counter: RwLock<u32>,
    }

    #[async_trait]
    impl DistributedTask for FailingTask {
        fn task_id(&self) -> &str { &self.id }
        fn priority(&self) -> u32 { 1 }
        fn estimate_resources(&self) -> ResourceRequirements {
            ResourceRequirements {
                cpu_cores: 1.0,
                memory_mb: 128,
                gpu_memory_mb: None,
                network_bandwidth_mbps: 10,
            }
        }
        async fn execute(&self, _context: &ExecutionContext) -> Result<RecordBatch> {
            let mut c = self.counter.write().await;
            if *c < self.fail_times {
                *c += 1;
                Err(DistributedError::ComputeError("模拟失败".to_string()))
            } else {
                Ok(RecordBatch::new_empty(Arc::new(Schema::empty())))
            }
        }
    }

    #[tokio::test]
    async fn test_retry_policy() {
        let manager = FaultToleranceManager::new();
        let task = FailingTask {
            id: "t1".to_string(),
            fail_times: 2,
            counter: RwLock::new(0),
        };
        manager.set_retry_policy("t1", RetryPolicy::Fixed { max_attempts: 3 }).await;
        let ctx = ExecutionContext {
            schema: Arc::new(Schema::empty()),
            partition_id: 0,
            total_partitions: 1,
            resource_manager: Arc::new(crate::resource::DynamicResourceManager::new(crate::ResourceStatus {
                total_cpu_cores: 1.0,
                available_cpu_cores: 1.0,
                total_memory_mb: 1024,
                available_memory_mb: 1024,
                total_gpu_memory_mb: None,
                available_gpu_memory_mb: None,
                network_bandwidth_mbps: 100,
            })),
        };
        let result = manager.execute_with_fault_tolerance(&task, &ctx).await;
        assert!(result.is_ok());
    }
}
