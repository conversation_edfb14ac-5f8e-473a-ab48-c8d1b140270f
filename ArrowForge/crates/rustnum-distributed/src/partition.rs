use crate::{ResourceStatus, Result, DistributedError};
use crate::PartitionStrategy;
use arrow::record_batch::RecordBatch;
use async_trait::async_trait;
use std::sync::Arc;

/// 智能分区策略
pub struct SmartPartitionStrategy {
    // 分区粒度参数
    min_partition_size: usize,
    max_partitions: u32,
}

impl SmartPartitionStrategy {
    pub fn new(min_partition_size: usize, max_partitions: u32) -> Self {
        Self { min_partition_size, max_partitions }
    }
}

#[async_trait]
impl PartitionStrategy for SmartPartitionStrategy {
    async fn compute_partitions(&self, data: &RecordBatch, resources: &ResourceStatus) -> Result<u32> {
        let total_rows = data.num_rows();
        let mut partitions = (total_rows / self.min_partition_size).max(1) as u32;
        let max_parallel = resources.available_cpu_cores as u32;
        partitions = partitions.min(self.max_partitions).min(max_parallel);
        Ok(partitions.max(1))
    }

    async fn create_partitions(&self, data: &RecordBatch, num_partitions: u32) -> Result<Vec<RecordBatch>> {
        let total_rows = data.num_rows();
        let mut batches = Vec::with_capacity(num_partitions as usize);
        let rows_per_partition = (total_rows + num_partitions as usize - 1) / num_partitions as usize;
        for i in 0..num_partitions {
            let start = (i as usize) * rows_per_partition;
            let end = ((i as usize + 1) * rows_per_partition).min(total_rows);
            if start < end {
                let slice = data.slice(start, end - start);
                batches.push(slice);
            }
        }
        Ok(batches)
    }

    async fn merge_partitions(&self, partitions: Vec<RecordBatch>) -> Result<RecordBatch> {
        if partitions.is_empty() {
            return Err(DistributedError::ComputeError("无分区可合并".to_string()));
        }
        let schema = partitions[0].schema();
        let arrays: Vec<_> = partitions.iter().flat_map(|b| b.columns().to_vec()).collect();
        let merged = RecordBatch::try_new(schema, arrays)
            .map_err(|e| DistributedError::ComputeError(e.to_string()))?;
        Ok(merged)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use arrow::array::{Int32Array, ArrayRef};
    use arrow::datatypes::Schema;
    use std::sync::Arc;

    #[tokio::test]
    async fn test_partitioning() {
        let schema = Arc::new(Schema::new(vec![] as Vec<arrow::datatypes::Field>));
        let data = RecordBatch::new_empty(schema.clone());
        let resources = ResourceStatus {
            total_cpu_cores: 4.0,
            available_cpu_cores: 4.0,
            total_memory_mb: 8192,
            available_memory_mb: 8192,
            total_gpu_memory_mb: None,
            available_gpu_memory_mb: None,
            network_bandwidth_mbps: 1000,
        };
        let strategy = SmartPartitionStrategy::new(100, 8);
        let n = strategy.compute_partitions(&data, &resources).await.unwrap();
        assert!(n >= 1);
    }
}
