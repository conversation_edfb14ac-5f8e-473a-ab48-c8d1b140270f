use crate::{DistributedError, Result};
use arrow::record_batch::RecordBatch;
use async_trait::async_trait;
use serde::{Serialize, Deserialize};
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tracing::{info, error};

/// 网络消息类型
#[derive(Debug, Serialize, Deserialize)]
pub enum NetworkMessage {
    Data(Vec<u8>),
    Heartbeat,
    Ack,
    Error(String),
}

/// 高效序列化工具
pub fn serialize_batch(batch: &RecordBatch) -> Result<Vec<u8>> {
    let mut buf = Vec::new();
    arrow::ipc::writer::StreamWriter::try_new(&mut buf, &batch.schema())
        .map_err(|e| DistributedError::NetworkError(e.to_string()))?
        .write(batch)
        .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
    Ok(buf)
}

pub fn deserialize_batch(data: &[u8]) -> Result<RecordBatch> {
    let reader = arrow::ipc::reader::StreamReader::try_new(data, None)
        .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
    let batches: Vec<_> = reader.collect::<std::result::Result<_, _>>()
        .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
    batches.into_iter().next().ok_or_else(|| DistributedError::NetworkError("无数据".to_string()))
}

/// 网络传输服务
pub struct NetworkService {
    pub listen_addr: String,
}

impl NetworkService {
    pub fn new(listen_addr: &str) -> Self {
        Self { listen_addr: listen_addr.to_string() }
    }

    /// 启动监听服务
    pub async fn start(&self) -> Result<()> {
        let listener = TcpListener::bind(&self.listen_addr).await
            .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
        info!("网络服务监听于 {}", self.listen_addr);
        loop {
            let (mut socket, addr) = listener.accept().await
                .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
            info!("收到连接: {}", addr);
            tokio::spawn(async move {
                let mut buf = vec![0u8; 65536];
                match socket.read(&mut buf).await {
                    Ok(n) if n > 0 => {
                        // 反序列化消息
                        match serde_json::from_slice::<NetworkMessage>(&buf[..n]) {
                            Ok(NetworkMessage::Data(data)) => {
                                info!("收到数据包，大小{}字节", data.len());
                                // 这里可进一步处理数据
                            }
                            Ok(NetworkMessage::Heartbeat) => {
                                info!("收到心跳");
                            }
                            Ok(NetworkMessage::Ack) => {
                                info!("收到确认");
                            }
                            Ok(NetworkMessage::Error(msg)) => {
                                error!("收到错误: {}", msg);
                            }
                            Err(e) => {
                                error!("消息反序列化失败: {}", e);
                            }
                        }
                    }
                    Ok(_) => {}
                    Err(e) => {
                        error!("读取失败: {}", e);
                    }
                }
            });
        }
    }

    /// 发送数据
    pub async fn send(&self, addr: &str, batch: &RecordBatch) -> Result<()> {
        let mut stream = TcpStream::connect(addr).await
            .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
        let data = serialize_batch(batch)?;
        let msg = NetworkMessage::Data(data);
        let encoded = serde_json::to_vec(&msg)
            .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
        stream.write_all(&encoded).await
            .map_err(|e| DistributedError::NetworkError(e.to_string()))?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use arrow::array::{Int32Array, ArrayRef};
    use arrow::datatypes::{Field, DataType, Schema};
    use std::sync::Arc;

    #[tokio::test]
    async fn test_serialize_deserialize() {
        let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
        let array: ArrayRef = Arc::new(Int32Array::from(vec![1, 2, 3]));
        let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
        let data = serialize_batch(&batch).unwrap();
        let batch2 = deserialize_batch(&data).unwrap();
        assert_eq!(batch2.num_rows(), 3);
    }
}
