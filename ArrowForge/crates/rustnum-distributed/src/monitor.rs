use crate::ResourceStatus;
use crate::scheduler::TaskState;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use serde::{Serialize, Deserialize};
use serde_json;
use tracing::{info, warn};

/// 监控指标类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum MetricValue {
    Counter(u64),
    Gauge(f64),
    Histogram(Vec<f64>),
}

/// 监控指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Metric {
    pub name: String,
    pub value: MetricValue,
    pub labels: Vec<(String, String)>,
    pub timestamp: u64,
}

/// 监控事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringEvent {
    pub event_type: String,
    pub message: String,
    pub severity: EventSeverity,
    pub timestamp: u64,
    pub context: serde_json::Value,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum EventSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

/// 性能分析记录
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProfilingRecord {
    pub operation: String,
    pub duration_ms: u64,
    pub resource_usage: ResourceUsage,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceUsage {
    pub cpu_usage: f32,
    pub memory_usage: u64,
    pub gpu_usage: Option<f32>,
    pub network_usage: u64,
}

/// 分布式监控系统
pub struct DistributedMonitor {
    // 指标存储
    metrics: RwLock<Vec<Metric>>,
    // 事件队列
    events: RwLock<VecDeque<MonitoringEvent>>,
    // 性能分析数据
    profiling_data: RwLock<Vec<ProfilingRecord>>,
    // 警报配置
    alert_config: RwLock<AlertConfiguration>,
    // 指标保留时间
    metric_retention: Duration,
    // 事件保留时间
    event_retention: Duration,
}

/// 警报配置
#[derive(Debug, Clone, Serialize, Deserialize)]
struct AlertConfiguration {
    cpu_threshold: f32,
    memory_threshold: f32,
    gpu_threshold: Option<f32>,
    latency_threshold_ms: u64,
    error_rate_threshold: f32,
}

impl DistributedMonitor {
    pub fn new(metric_retention: Duration, event_retention: Duration) -> Self {
        Self {
            metrics: RwLock::new(Vec::new()),
            events: RwLock::new(VecDeque::new()),
            profiling_data: RwLock::new(Vec::new()),
            alert_config: RwLock::new(AlertConfiguration {
                cpu_threshold: 0.8,
                memory_threshold: 0.8,
                gpu_threshold: Some(0.8),
                latency_threshold_ms: 1000,
                error_rate_threshold: 0.1,
            }),
            metric_retention,
            event_retention,
        }
    }

    /// 记录指标
    pub async fn record_metric(&self, metric: Metric) {
        let mut metrics = self.metrics.write().await;
        metrics.push(metric);
        
        // 清理过期数据
        let cutoff = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() - self.metric_retention.as_secs();
            
        metrics.retain(|m| m.timestamp >= cutoff);
    }

    /// 记录事件
    pub async fn record_event(&self, event: MonitoringEvent) {
        let mut events = self.events.write().await;
        events.push_back(event);
        
        // 清理过期事件
        let cutoff = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs() - self.event_retention.as_secs();
            
        while events.front()
            .map_or(false, |e| e.timestamp < cutoff) {
            events.pop_front();
        }
        
        // 检查是否需要触发警报
        self.check_alerts(&events).await;
    }

    /// 记录性能数据
    pub async fn record_profiling(&self, record: ProfilingRecord) {
        let mut data = self.profiling_data.write().await;
        data.push(record);
        
        // 仅保留最近1000条记录
        if data.len() > 1000 {
            data.remove(0);
        }
    }

    /// 检查警报条件
    async fn check_alerts(&self, events: &VecDeque<MonitoringEvent>) {
        let config = self.alert_config.read().await;
        let recent_events: Vec<_> = events.iter()
            .rev()
            .take(100)
            .collect();
        
        // 检查错误率
        let error_count = recent_events.iter()
            .filter(|e| matches!(e.severity, EventSeverity::Error | EventSeverity::Critical))
            .count();
        let error_rate = error_count as f32 / recent_events.len() as f32;
        
        if error_rate > config.error_rate_threshold {
            warn!("错误率过高: {:.2}% > {:.2}%", 
                  error_rate * 100.0, 
                  config.error_rate_threshold * 100.0);
        }
    }

    /// 生成监控报告
    pub async fn generate_report(&self) -> MonitoringReport {
        let metrics = self.metrics.read().await;
        let events = self.events.read().await;
        let profiling = self.profiling_data.read().await;
        
        MonitoringReport {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            metrics: metrics.clone(),
            recent_events: events.iter().cloned().collect(),
            performance_summary: self.summarize_performance(&*profiling).await,
        }
    }

    /// 汇总性能数据
    async fn summarize_performance(&self, data: &[ProfilingRecord]) -> PerformanceSummary {
        let mut summary = PerformanceSummary {
            avg_cpu_usage: 0.0,
            avg_memory_usage: 0,
            avg_latency_ms: 0,
            operation_counts: std::collections::HashMap::new(),
        };
        
        if data.is_empty() {
            return summary;
        }
        
        let mut total_cpu = 0.0;
        let mut total_memory = 0;
        let mut total_latency = 0;
        
        for record in data {
            total_cpu += record.resource_usage.cpu_usage;
            total_memory += record.resource_usage.memory_usage;
            total_latency += record.duration_ms;
            
            *summary.operation_counts
                .entry(record.operation.clone())
                .or_insert(0) += 1;
        }
        
        let count = data.len() as f32;
        summary.avg_cpu_usage = total_cpu / count;
        summary.avg_memory_usage = total_memory / data.len() as u64;
        summary.avg_latency_ms = total_latency / data.len() as u64;
        
        summary
    }
}

/// 监控报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringReport {
    pub timestamp: u64,
    pub metrics: Vec<Metric>,
    pub recent_events: Vec<MonitoringEvent>,
    pub performance_summary: PerformanceSummary,
}

/// 性能汇总
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSummary {
    pub avg_cpu_usage: f32,
    pub avg_memory_usage: u64,
    pub avg_latency_ms: u64,
    pub operation_counts: std::collections::HashMap<String, u32>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_monitor_basics() {
        let monitor = DistributedMonitor::new(
            Duration::from_secs(3600),
            Duration::from_secs(3600),
        );

        // 记录指标
        monitor.record_metric(Metric {
            name: "cpu_usage".to_string(),
            value: MetricValue::Gauge(0.75),
            labels: vec![("node".to_string(), "worker1".to_string())],
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }).await;

        // 记录事件
        monitor.record_event(MonitoringEvent {
            event_type: "task_complete".to_string(),
            message: "Task completed successfully".to_string(),
            severity: EventSeverity::Info,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            context: serde_json::json!({"task_id": "123"}),
        }).await;

        // 记录性能数据
        monitor.record_profiling(ProfilingRecord {
            operation: "matrix_multiply".to_string(),
            duration_ms: 100,
            resource_usage: ResourceUsage {
                cpu_usage: 0.5,
                memory_usage: 1024 * 1024,
                gpu_usage: Some(0.3),
                network_usage: 1000,
            },
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }).await;

        // 生成报告
        let report = monitor.generate_report().await;
        assert_eq!(report.metrics.len(), 1);
        assert_eq!(report.recent_events.len(), 1);
        assert!(report.performance_summary.avg_cpu_usage > 0.0);
    }
}
