[package]
name = "rustnum-distributed"
version = "0.1.0"
edition = "2021"
description = "Distributed computing support for RustNum"

[dependencies]
arrow = "55.1"
arrow-flight = "55.1"
futures = "0.3"
tokio = { version = "1.36", features = ["full"] }
tracing = "0.1"
async-trait = "0.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
thiserror = "1.0"
dashmap = "5.5"
rand = "0.8"
crossbeam = "0.8"
rustnum-core = { path = "../rustnum-core" }

[dev-dependencies]
criterion = "0.5"
tokio-test = "0.4"
