use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;

#[tokio::test(flavor = "multi_thread", worker_threads = 4)]
async fn test_heterogeneous_hardware_support() {
    // 模拟CPU+GPU异构节点
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 10_000;
    let array: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
    let resources_cpu = ResourceStatus {
        total_cpu_cores: 4.0,
        available_cpu_cores: 4.0,
        total_memory_mb: 4096,
        available_memory_mb: 4096,
        total_gpu_memory_mb: None,
        available_gpu_memory_mb: None,
        network_bandwidth_mbps: 1000,
    };
    let resources_gpu = ResourceStatus {
        total_cpu_cores: 2.0,
        available_cpu_cores: 2.0,
        total_memory_mb: 2048,
        available_memory_mb: 2048,
        total_gpu_memory_mb: Some(4096),
        available_gpu_memory_mb: Some(4096),
        network_bandwidth_mbps: 1000,
    };
    let cpu_manager = Arc::new(resource::DynamicResourceManager::new(resources_cpu));
    let gpu_manager = Arc::new(resource::DynamicResourceManager::new(resources_gpu));
    let partition_strategy = Arc::new(partition::SmartPartitionStrategy::new(2_000, 4));
    // CPU节点执行
    let dist_array_cpu = DistributedArray::new(batch.clone(), partition_strategy.clone(), cpu_manager.clone()).unwrap();
    // GPU节点执行（模拟）
    let dist_array_gpu = DistributedArray::new(batch.clone(), partition_strategy.clone(), gpu_manager.clone()).unwrap();
    // 简单加法任务
    let add_fn = |a: &RecordBatch| -> RecordBatch {
        let arr = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let sum: Vec<i32> = arr.iter().map(|x| x.unwrap() + 1).collect();
        let array: ArrayRef = Arc::new(Int32Array::from(sum));
        RecordBatch::try_new(a.schema(), vec![array]).unwrap()
    };
    let cpu_results: Vec<_> = dist_array_cpu.partitions().iter().map(|p| add_fn(p)).collect();
    let gpu_results: Vec<_> = dist_array_gpu.partitions().iter().map(|p| add_fn(p)).collect();
    let merged_cpu = partition_strategy.merge_partitions(cpu_results).await.unwrap();
    let merged_gpu = partition_strategy.merge_partitions(gpu_results).await.unwrap();
    assert_eq!(merged_cpu.num_rows(), n as usize);
    assert_eq!(merged_gpu.num_rows(), n as usize);
    println!("异构硬件支持测试通过，CPU+GPU节点均可正确执行分布式任务");
}
