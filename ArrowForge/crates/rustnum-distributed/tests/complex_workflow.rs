use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;
use std::time::Instant;
use rustnum_distributed::*;

#[tokio::test(flavor = "multi_thread", worker_threads = 4)]
async fn test_complex_distributed_workflow() {
    // 构造大规模数据
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 50_000;
    let array: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();

    let resources = ResourceStatus {
        total_cpu_cores: 4.0,
        available_cpu_cores: 4.0,
        total_memory_mb: 4096,
        available_memory_mb: 4096,
        total_gpu_memory_mb: None,
        available_gpu_memory_mb: None,
        network_bandwidth_mbps: 1000,
    };
    let resource_manager = Arc::new(resource::DynamicResourceManager::new(resources));
    let partition_strategy = Arc::new(partition::SmartPartitionStrategy::new(5_000, 8));
    let dist_array = DistributedArray::new(batch, partition_strategy.clone(), resource_manager.clone()).unwrap();

    // 1. 分布式加法
    let add_fn = |a: &RecordBatch| -> RecordBatch {
        let arr = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let sum: Vec<i32> = arr.iter().map(|x| x.unwrap() + 1).collect();
        let array: ArrayRef = Arc::new(Int32Array::from(sum));
        RecordBatch::try_new(a.schema(), vec![array]).unwrap()
    };
    let mut add_results = Vec::new();
    for p in dist_array.partitions().iter() {
        add_results.push(add_fn(p));
    }
    let added = partition_strategy.merge_partitions(add_results).await.unwrap();

    // 2. 分布式过滤
    let filter_fn = |a: &RecordBatch| -> RecordBatch {
        let arr = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let filtered: Vec<i32> = arr.iter().filter_map(|x| x.and_then(|v| if v % 2 == 0 { Some(v) } else { None })).collect();
        let array: ArrayRef = Arc::new(Int32Array::from(filtered));
        RecordBatch::try_new(a.schema(), vec![array]).unwrap()
    };
    let mut filter_results = Vec::new();
    let arr = added.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
    let chunk_size = 5_000;
    for i in (0..arr.len()).step_by(chunk_size) {
        let end = std::cmp::min(i + chunk_size, arr.len());
        let chunk_data: Vec<i32> = (i..end).map(|idx| arr.value(idx)).collect();
        let array: ArrayRef = Arc::new(Int32Array::from(chunk_data));
        let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
        filter_results.push(filter_fn(&batch));
    }
    // 手动合并过滤结果
    let mut all_filtered_values = Vec::new();
    for batch in filter_results {
        let arr = batch.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        for v in arr.iter() {
            all_filtered_values.push(v.unwrap());
        }
    }

    // 3. 分布式聚合（求和）
    let sum: i64 = all_filtered_values.iter().map(|x| *x as i64).sum();
    println!("复杂分布式工作流最终聚合结果: {}", sum);
    assert!(sum > 0);
}
