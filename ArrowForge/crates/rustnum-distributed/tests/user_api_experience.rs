use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;

#[tokio::test]
async fn test_user_api_experience() {
    // 用户视角：一行式分布式加法
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let array: ArrayRef = Arc::new(Int32Array::from(vec![1, 2, 3, 4, 5]));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
    let resources = ResourceStatus {
        total_cpu_cores: 2.0,
        available_cpu_cores: 2.0,
        total_memory_mb: 1024,
        available_memory_mb: 1024,
        total_gpu_memory_mb: None,
        available_gpu_memory_mb: None,
        network_bandwidth_mbps: 1000,
    };
    let resource_manager = Arc::new(resource::DynamicResourceManager::new(resources));
    let partition_strategy = Arc::new(partition::SmartPartitionStrategy::new(2, 2));
    let dist_array = DistributedArray::new(batch, partition_strategy.clone(), resource_manager.clone()).unwrap();
    // 用户自定义分布式操作
    let add_one = |b: &RecordBatch| {
        let arr = b.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let array: ArrayRef = Arc::new(Int32Array::from(arr.iter().map(|x| x.unwrap() + 1).collect::<Vec<_>>()));
        RecordBatch::try_new(b.schema(), vec![array]).unwrap()
    };
    let results: Vec<_> = dist_array.partitions().iter().map(|p| add_one(p)).collect();
    let merged = partition_strategy.merge_partitions(results).await.unwrap();
    let arr = merged.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
    assert_eq!(arr.values(), &[2, 3, 4, 5, 6]);
    println!("用户API体验测试通过，结果：{:?}", arr.values());
}
