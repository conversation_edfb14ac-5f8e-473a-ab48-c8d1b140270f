use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;
use std::time::Instant;
use tokio::runtime::Runtime;

#[tokio::test(flavor = "multi_thread", worker_threads = 4)]
async fn benchmark_distributed_matrix_addition() {
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 100_000;
    let array1: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let array2: ArrayRef = Arc::new(Int32Array::from((0..n).rev().collect::<Vec<_>>()));
    let batch1 = RecordBatch::try_new(schema.clone(), vec![array1]).unwrap();
    let batch2 = RecordBatch::try_new(schema.clone(), vec![array2]).unwrap();

    let resources = ResourceStatus {
        total_cpu_cores: 4.0,
        available_cpu_cores: 4.0,
        total_memory_mb: 4096,
        available_memory_mb: 4096,
        total_gpu_memory_mb: None,
        available_gpu_memory_mb: None,
        network_bandwidth_mbps: 1000,
    };
    let resource_manager = Arc::new(resource::DynamicResourceManager::new(resources));
    let partition_strategy = Arc::new(partition::SmartPartitionStrategy::new(10_000, 8));

    let dist_array1 = DistributedArray::new(batch1, partition_strategy.clone(), resource_manager.clone()).unwrap();
    let dist_array2 = DistributedArray::new(batch2, partition_strategy.clone(), resource_manager.clone()).unwrap();

    let add_fn = |a: &RecordBatch, b: &RecordBatch| -> RecordBatch {
        let arr1 = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let arr2 = b.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let sum: Vec<i32> = arr1.iter().zip(arr2.iter()).map(|(x, y)| x.unwrap() + y.unwrap()).collect();
        let array: ArrayRef = Arc::new(Int32Array::from(sum));
        RecordBatch::try_new(a.schema(), vec![array]).unwrap()
    };

    let partitions1 = dist_array1.partitions().to_vec();
    let partitions2 = dist_array2.partitions().to_vec();
    assert_eq!(partitions1.len(), partitions2.len());
    let mut results = Vec::new();
    let start = Instant::now();
    for (p1, p2) in partitions1.iter().zip(partitions2.iter()) {
        results.push(add_fn(p1, p2));
    }
    let merged = partition_strategy.merge_partitions(results).await.unwrap();
    let elapsed = start.elapsed();
    println!("分布式矩阵加法 {} 行耗时: {:?}", n, elapsed);
    let arr = merged.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
    assert_eq!(arr.len(), n as usize);
}
