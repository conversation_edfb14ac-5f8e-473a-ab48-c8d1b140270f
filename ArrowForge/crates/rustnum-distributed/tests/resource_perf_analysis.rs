use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;
use std::time::Instant;
use rustnum_distributed::*;

#[tokio::test(flavor = "multi_thread", worker_threads = 4)]
async fn test_resource_utilization_and_perf() {
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 20_000;
    let array: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();

    let resources = ResourceStatus {
        total_cpu_cores: 2.0,
        available_cpu_cores: 2.0,
        total_memory_mb: 1024,
        available_memory_mb: 1024,
        total_gpu_memory_mb: None,
        available_gpu_memory_mb: None,
        network_bandwidth_mbps: 1000,
    };
    let resource_manager = Arc::new(resource::DynamicResourceManager::new(resources));
    let partition_strategy = Arc::new(partition::SmartPartitionStrategy::new(2_000, 4));
    let dist_array = DistributedArray::new(batch, partition_strategy.clone(), resource_manager.clone()).unwrap();

    let mut results = Vec::new();
    let start = Instant::now();
    for p in dist_array.partitions().iter() {
        let arr = p.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        let sum: i32 = arr.iter().map(|x| x.unwrap()).sum();
        let array: ArrayRef = Arc::new(Int32Array::from(vec![sum]));
        let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
        results.push(batch);
    }
    let merged = partition_strategy.merge_partitions(results).await.unwrap();
    let elapsed = start.elapsed();
    let arr = merged.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
    let total: i32 = arr.iter().map(|x| x.unwrap()).sum();
    println!("资源利用率与性能分析: 总和={}，耗时={:?}", total, elapsed);
    assert_eq!(total, (0..n).sum::<i32>());
}
