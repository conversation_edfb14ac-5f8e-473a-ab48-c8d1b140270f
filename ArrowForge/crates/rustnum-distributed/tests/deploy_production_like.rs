use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use rustnum_distributed::*;

#[tokio::test(flavor = "multi_thread", worker_threads = 8)]
async fn test_production_like_deployment() {
    // 模拟生产环境：多节点异构资源、动态负载、节点故障
    let node_count = 4;
    let mut resource_managers = Vec::new();
    let mut partition_strategies = Vec::new();
    let cpu_cores = [4.0, 2.0, 8.0, 1.0];
    let mems = [8192, 2048, 16384, 1024];
    for i in 0..node_count {
        let resources = ResourceStatus {
            total_cpu_cores: cpu_cores[i as usize],
            available_cpu_cores: cpu_cores[i as usize],
            total_memory_mb: mems[i as usize],
            available_memory_mb: mems[i as usize],
            total_gpu_memory_mb: if i == 2 { Some(4096) } else { None },
            available_gpu_memory_mb: if i == 2 { Some(4096) } else { None },
            network_bandwidth_mbps: 1000,
        };
        resource_managers.push(Arc::new(resource::DynamicResourceManager::new(resources)));
        partition_strategies.push(Arc::new(partition::SmartPartitionStrategy::new(2_000, 8)));
    }
    // 构造数据
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 40_000;
    let array: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
    // 按节点分片
    let rows_per_node = n / node_count;
    let mut distributed_batches = Vec::new();
    for i in 0..node_count {
        let start = i * rows_per_node;
        let end = if i == node_count - 1 { n } else { (i + 1) * rows_per_node };
        let slice = batch.slice(start as usize, (end - start) as usize);
        distributed_batches.push(slice);
    }
    // 并行执行分布式加法，模拟部分节点延迟/故障
    let mut handles = Vec::new();
    for i in 0..node_count {
        let batch = distributed_batches[i as usize].clone();
        let partition_strategy = partition_strategies[i as usize].clone();
        let resource_manager = resource_managers[i as usize].clone();
        handles.push(tokio::spawn(async move {
            if i == 1 { sleep(Duration::from_millis(200)).await; } // 模拟延迟
            if i == 3 { return None; } // 模拟节点故障
            let dist_array = DistributedArray::new(batch, partition_strategy.clone(), resource_manager.clone()).unwrap();
            let add_fn = |a: &RecordBatch| -> RecordBatch {
                let arr = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
                let sum: Vec<i32> = arr.iter().map(|x| x.unwrap() + 1).collect();
                let array: ArrayRef = Arc::new(Int32Array::from(sum));
                RecordBatch::try_new(a.schema(), vec![array]).unwrap()
            };
            let mut results = Vec::new();
            for p in dist_array.partitions().iter() {
                results.push(add_fn(p));
            }
            Some(partition_strategy.merge_partitions(results).await.unwrap())
        }));
    }
    // 聚合所有可用节点结果
    let mut all_results = Vec::new();
    for h in handles {
        if let Some(batch) = h.await.unwrap() {
            all_results.push(batch);
        }
    }
    // 手动合并最终结果
    let mut all_values = Vec::new();
    for batch in all_results {
        let arr = batch.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        for v in arr.iter() {
            all_values.push(v.unwrap());
        }
    }
    // 校验结果正确性（排除故障节点数据）
    assert_eq!(all_values.len(), (n * 3 / 4) as usize);
    println!("生产环境部署仿真通过，节点数：{}，有效数据行数：{}", node_count, all_values.len());
}
