use rustnum_distributed::monitor::*;
use rustnum_distributed::monitor::{DistributedMonitor, Metric, MetricValue, MonitoringEvent, EventSeverity, ProfilingRecord, ResourceUsage};
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test_monitoring_api_and_alerts() {
    let monitor = DistributedMonitor::new(Duration::from_secs(60), Duration::from_secs(60));
    // 模拟指标与事件
    for i in 0..10 {
        monitor.record_metric(Metric {
            name: "cpu_usage".to_string(),
            value: MetricValue::Gauge(0.7 + 0.05 * (i as f64)),
            labels: vec![("node".to_string(), format!("worker{}", i))],
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
        }).await;
        
        // 添加性能记录数据
        monitor.record_profiling(ProfilingRecord {
            operation: "compute".to_string(),
            duration_ms: 100 + i * 10,
            resource_usage: ResourceUsage {
                cpu_usage: 0.7 + 0.05 * (i as f32),
                memory_usage: 1024 * 1024 * (100 + i * 10),
                gpu_usage: Some(0.5 + 0.02 * (i as f32)),
                network_usage: 1024 * (50 + i * 5),
            },
            timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
        }).await;
    }
    monitor.record_event(MonitoringEvent {
        event_type: "error".to_string(),
        message: "模拟错误事件".to_string(),
        severity: EventSeverity::Error,
        timestamp: std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
        context: serde_json::json!({"node": "worker1"}),
    }).await;
    sleep(Duration::from_millis(100)).await;
    let report = monitor.generate_report().await;
    assert!(report.metrics.len() >= 10);
    assert!(report.recent_events.iter().any(|e| matches!(e.severity, EventSeverity::Error)));
    assert!(report.performance_summary.avg_cpu_usage > 0.0);
    println!("监控API与告警测试通过，平均CPU使用率：{}", report.performance_summary.avg_cpu_usage);
}
