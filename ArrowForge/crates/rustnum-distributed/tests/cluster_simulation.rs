use rustnum_distributed::*;
use arrow::array::{Int32Array, ArrayRef};
use arrow::datatypes::{Field, DataType, Schema};
use arrow::record_batch::RecordBatch;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use rustnum_distributed::*;

#[tokio::test(flavor = "multi_thread", worker_threads = 8)]
async fn test_large_scale_cluster_simulation() {
    // 模拟8节点集群，每节点分配独立资源
    let node_count = 8;
    let mut resource_managers = Vec::new();
    let mut partition_strategies = Vec::new();
    for _ in 0..node_count {
        let resources = ResourceStatus {
            total_cpu_cores: 2.0,
            available_cpu_cores: 2.0,
            total_memory_mb: 2048,
            available_memory_mb: 2048,
            total_gpu_memory_mb: None,
            available_gpu_memory_mb: None,
            network_bandwidth_mbps: 1000,
        };
        resource_managers.push(Arc::new(resource::DynamicResourceManager::new(resources)));
        partition_strategies.push(Arc::new(partition::SmartPartitionStrategy::new(2_000, 4)));
    }
    // 构造大规模数据
    let schema = Arc::new(Schema::new(vec![Field::new("a", DataType::Int32, false)]));
    let n = 160_000;
    let array: ArrayRef = Arc::new(Int32Array::from((0..n).collect::<Vec<_>>()));
    let batch = RecordBatch::try_new(schema.clone(), vec![array]).unwrap();
    // 按节点分片
    let rows_per_node = n / node_count;
    let mut distributed_batches = Vec::new();
    for i in 0..node_count {
        let start = i * rows_per_node;
        let end = if i == node_count - 1 { n } else { (i + 1) * rows_per_node };
        let slice = batch.slice(start as usize, (end - start) as usize);
        distributed_batches.push(slice);
    }
    // 各节点并行执行加法任务
    let mut handles = Vec::new();
    for i in 0..node_count {
        let batch = distributed_batches[i as usize].clone();
        let partition_strategy = partition_strategies[i as usize].clone();
        let resource_manager = resource_managers[i as usize].clone();
        handles.push(tokio::spawn(async move {
            let dist_array = DistributedArray::new(batch, partition_strategy.clone(), resource_manager.clone()).unwrap();
            let add_fn = |a: &RecordBatch| -> RecordBatch {
                let arr = a.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
                let sum: Vec<i32> = arr.iter().map(|x| x.unwrap() + 1).collect();
                let array: ArrayRef = Arc::new(Int32Array::from(sum));
                RecordBatch::try_new(a.schema(), vec![array]).unwrap()
            };
            let mut results = Vec::new();
            for p in dist_array.partitions().iter() {
                results.push(add_fn(p));
            }
            partition_strategy.merge_partitions(results).await.unwrap()
        }));
    }
    // 聚合所有节点结果
    let mut all_results = Vec::new();
    for h in handles {
        let batch = h.await.unwrap();
        all_results.push(batch);
    }
    // 合并最终结果 - 手动连接所有节点的结果
    let mut all_values = Vec::new();
    for batch in all_results {
        let arr = batch.column(0).as_any().downcast_ref::<Int32Array>().unwrap();
        for v in arr.iter() {
            all_values.push(v.unwrap());
        }
    }
    assert_eq!(all_values.len(), n as usize);
    let arr = &all_values;
    // 校验结果正确性
    for (i, v) in arr.iter().enumerate() {
        assert_eq!(*v, (i as i32) + 1);
    }
    println!("大规模集群仿真通过，节点数：{}，总行数：{}", node_count, n);
    // 模拟部分节点延迟/故障
    sleep(Duration::from_millis(100)).await;
}
