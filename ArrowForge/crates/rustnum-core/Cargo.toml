[package]
name = "rustnum-core"
version = "0.1.0"
edition = "2021"
authors = ["RustNum Team"]
description = "High-performance scientific computing library in Rust"

[dependencies]
# 基础工具
num-traits = { version = "0.2.19", features = ["std", "libm"] }
rayon = { version = "1.7", optional = true }
crossbeam = { version = "0.8", optional = true }
parking_lot = "0.12"

# SIMD 支持
wide = { version = "0.7", optional = true }
cfg-if = "1.0"

# 内存管理
mimalloc = { version = "0.1", default-features = false }

# 错误处理
thiserror = "1.0"

# BLAS 支持
blas = { version = "0.22", optional = true }
blas-src = { version = "0.9", optional = true }
openblas-src = { version = "0.10", optional = true, features = ["cblas", "lapacke", "system"] }
intel-mkl-src = { version = "0.8", optional = true, features = ["mkl-static-lp64-iomp"] }

# LAPACK 支持
lapack = { version = "0.19", optional = true }
lapack-src = { version = "0.9", optional = true }

# GPU 支持 - 优先支持AMD ROCm生态
# CUDA支持（备选）
# cuda-runtime-sys = { version = "0.3", optional = true }
# cuda-driver-sys = { version = "0.3", optional = true }
# rustacuda = { version = "0.1", optional = true }

# GPU支持
# AMD ROCm支持（优先）- 暂时禁用，等待生态成熟
# rocblas-sys = { version = "0.1", optional = true }
# hip-sys = { version = "0.1", optional = true }

# OpenCL通用支持
opencl3 = { version = "0.9", optional = true }

# CPU检测
raw-cpuid = "11.0"

# 机器学习支持
rustlearn = { version = "0.5", optional = true }
ndarray = { version = "0.12.0", features = ["serde"], optional = true }
smartcore = { version = "0.2", features = ["serde"], optional = true }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_millis = "0.1.1"

# 优化支持
rustfft = { version = "6.1", optional = true }
ndarray-stats = { version = "0.5", optional = true }
rand_distr = "0.4"
approx = "0.5"

# 分布式支持
tokio = { version = "1.25", features = ["full"], optional = true }
futures = { version = "0.3", optional = true }
dashmap = { version = "5.4", optional = true }
uuid = { version = "1.3", features = ["v4", "serde"], optional = true }
async-trait = { version = "0.1", optional = true }

# 其他工具
lazy_static = "1.4"
num_cpus = "1.16"
cache-size = "0.7.0"
num-complex = { version = "0.4.6", features = ["std"] }
libc = "0.2"

# Arrow 生态集成
arrow = { version = "55.1.0", optional = true }
parquet = { version = "55.1.0", optional = true }

# 对比测试库
faer = { version = "0.19", optional = true }
chrono = { version = "0.4", features = ["serde"], optional = true }

[features]
default = ["simd", "parallel", "openblas", "arrow"]
core = []  # 基础功能，不包含任何可选依赖

# BLAS后端选择
openblas = ["blas", "lapack", "openblas-src", "blas-src", "lapack-src"]
intel-mkl = ["blas", "lapack", "intel-mkl-src", "blas-src", "lapack-src"]
system-blas = ["blas", "lapack"]

# GPU支持暂时禁用
# GPU支持
amd-gpu = ["opencl3"]  # 暂时使用OpenCL，等待ROCm生态成熟
cuda-gpu = ["opencl3"]  # CUDA依赖项暂时注释，使用OpenCL作为通用接口
opencl = ["opencl3"]

# 核心功能
simd = ["wide"]
parallel = ["rayon", "crossbeam"]
ml_scheduler = ["rustlearn", "ndarray", "smartcore", "tokio", "futures", "dashmap", "uuid", "async-trait"]
fft = ["rustfft"]
stats = ["ndarray-stats"]
optimization = ["fft", "stats"]

# Arrow 生态集成
arrow = ["dep:arrow"]
arrow-compute = ["arrow"]
arrow-parquet = ["arrow", "dep:parquet"]
arrow-full = ["dep:arrow", "dep:parquet"]

# 对比测试
faer-comparison = ["faer", "chrono"]
distributed = ["tokio", "futures", "dashmap", "uuid", "async-trait"]

# 完整功能集
full = [
    "simd", "parallel", "intel-mkl",
    "ml_scheduler", "fft", "stats", "optimization", "distributed"
]

# 模块特性
array = []
sparse = []
solvers = []
transform = []
backend = []
gpu = []
ml = []
cloud = []
bindings = []
