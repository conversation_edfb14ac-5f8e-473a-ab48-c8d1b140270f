//! ArrowSciCompute 基础功能验证
//!
//! 这个示例验证我们的核心数组功能是否正常工作

use rustnum_core::array::{RustArray, ArrayCreation};
use rustnum_core::traits::Numeric;

fn main() {
    println!("🚀 ArrowSciCompute 基础功能验证开始...");
    
    // 测试数值 trait
    test_numeric_traits();
    
    // 测试数组创建
    test_array_creation();
    
    // 测试数组操作
    test_array_operations();
    
    // 测试性能
    test_performance();
    
    println!("🎉 所有基础功能测试通过！");
}

fn test_numeric_traits() {
    println!("\n🧮 测试数值 trait...");
    
    // 测试 f64
    assert_eq!(f64::zero(), 0.0);
    assert_eq!(f64::one(), 1.0);
    
    let x = 3.0_f64;
    assert!((x.sqrt() - 1.732050807568877).abs() < 1e-10);
    assert_eq!(x.abs(), 3.0);
    assert_eq!(x.signum(), 1.0);
    
    // 测试 f32
    assert_eq!(f32::zero(), 0.0);
    assert_eq!(f32::one(), 1.0);
    
    // 测试整数
    assert_eq!(i32::zero(), 0);
    assert_eq!(i32::one(), 1);
    assert_eq!(i64::zero(), 0);
    assert_eq!(i64::one(), 1);
    
    println!("  ✅ 数值 trait 测试通过");
}

fn test_array_creation() {
    println!("\n🏗️ 测试数组创建...");
    
    // 测试 f64 数组
    let zeros = RustArray::<f64>::zeros(&[2, 3]).unwrap();
    assert_eq!(zeros.shape(), &[2, 3]);
    
    for i in 0..2 {
        for j in 0..3 {
            assert_eq!(zeros.get(&[i, j]).unwrap(), 0.0);
        }
    }
    
    let ones = RustArray::<f64>::ones(&[3, 2]).unwrap();
    assert_eq!(ones.shape(), &[3, 2]);
    
    for i in 0..3 {
        for j in 0..2 {
            assert_eq!(ones.get(&[i, j]).unwrap(), 1.0);
        }
    }
    
    let eye = RustArray::<f64>::eye(3).unwrap();
    assert_eq!(eye.shape(), &[3, 3]);
    
    for i in 0..3 {
        for j in 0..3 {
            let expected = if i == j { 1.0 } else { 0.0 };
            assert_eq!(eye.get(&[i, j]).unwrap(), expected);
        }
    }
    
    // 测试 f32 数组
    let zeros_f32 = RustArray::<f32>::zeros(&[2, 2]).unwrap();
    assert_eq!(zeros_f32.shape(), &[2, 2]);
    
    println!("  ✅ 数组创建测试通过");
}

fn test_array_operations() {
    println!("\n🔧 测试数组操作...");
    
    let mut matrix = RustArray::<f64>::zeros(&[2, 2]).unwrap();
    
    // 测试设置和获取
    matrix.set(&[0, 0], 1.0).unwrap();
    matrix.set(&[0, 1], 2.0).unwrap();
    matrix.set(&[1, 0], 3.0).unwrap();
    matrix.set(&[1, 1], 4.0).unwrap();
    
    assert_eq!(matrix.get(&[0, 0]).unwrap(), 1.0);
    assert_eq!(matrix.get(&[0, 1]).unwrap(), 2.0);
    assert_eq!(matrix.get(&[1, 0]).unwrap(), 3.0);
    assert_eq!(matrix.get(&[1, 1]).unwrap(), 4.0);
    
    // 测试克隆
    let cloned = matrix.clone();
    for i in 0..2 {
        for j in 0..2 {
            assert_eq!(
                matrix.get(&[i, j]).unwrap(),
                cloned.get(&[i, j]).unwrap()
            );
        }
    }
    
    // 测试错误处理
    assert!(matrix.get(&[5, 5]).is_err());
    assert!(matrix.set(&[5, 5], 1.0).is_err());
    
    println!("  ✅ 数组操作测试通过");
}

fn test_performance() {
    println!("\n⏱️ 基础性能测试...");
    
    use std::time::Instant;
    
    // 创建性能测试
    let start = Instant::now();
    for _ in 0..1000 {
        let _matrix = RustArray::<f64>::zeros(&[10, 10]).unwrap();
    }
    let creation_time = start.elapsed();
    println!("  创建 1000 个 10x10 矩阵: {:?}", creation_time);
    
    // 访问性能测试
    let matrix = RustArray::<f64>::ones(&[100, 100]).unwrap();
    let start = Instant::now();
    let mut sum = 0.0;
    for i in 0..100 {
        for j in 0..100 {
            sum += matrix.get(&[i, j]).unwrap_or(0.0);
        }
    }
    let access_time = start.elapsed();
    println!("  访问 100x100 矩阵: {:?}, sum: {}", access_time, sum);
    
    // 修改性能测试
    let mut matrix = RustArray::<f64>::zeros(&[50, 50]).unwrap();
    let start = Instant::now();
    for i in 0..50 {
        for j in 0..50 {
            matrix.set(&[i, j], (i * j) as f64).unwrap();
        }
    }
    let modify_time = start.elapsed();
    println!("  修改 50x50 矩阵: {:?}", modify_time);
    
    println!("  ✅ 性能测试完成");
}
