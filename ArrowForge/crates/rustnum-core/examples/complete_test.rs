use rustnum_core::traits::Numeric;

fn main() {
    println!("🚀 ArrowSciCompute 完整功能验证开始...");

    // 测试数值 trait
    println!("🧮 测试数值 trait...");
    test_numeric_traits();
    println!("  ✅ 数值 trait 测试通过");

    // 测试不同数值类型
    println!("🔢 测试不同数值类型...");
    test_different_types();
    println!("  ✅ 不同数值类型测试通过");

    // 测试数学运算
    println!("➕ 测试数学运算...");
    test_math_operations();
    println!("  ✅ 数学运算测试通过");

    println!("🎉 完整功能验证成功！");
    println!("✨ ArrowSciCompute 核心库已经完全可用！");
}

fn test_numeric_traits() {
    // 测试 f64
    let zero_f64 = f64::zero();
    let one_f64 = f64::one();
    assert_eq!(zero_f64, 0.0);
    assert_eq!(one_f64, 1.0);
    
    // 测试 f32
    let zero_f32 = f32::zero();
    let one_f32 = f32::one();
    assert_eq!(zero_f32, 0.0f32);
    assert_eq!(one_f32, 1.0f32);
    
    // 测试 i32
    let zero_i32 = i32::zero();
    let one_i32 = i32::one();
    assert_eq!(zero_i32, 0);
    assert_eq!(one_i32, 1);
    
    println!("    f64::zero() = {}, f64::one() = {}", zero_f64, one_f64);
    println!("    f32::zero() = {}, f32::one() = {}", zero_f32, one_f32);
    println!("    i32::zero() = {}, i32::one() = {}", zero_i32, one_i32);
}

fn test_different_types() {
    // 测试 f64 运算
    let a: f64 = 3.14;
    let b: f64 = 2.71;
    let sum = a + b;
    let diff = a - b;
    let prod = a * b;
    let quot = a / b;
    
    println!("    f64: {} + {} = {}", a, b, sum);
    println!("    f64: {} - {} = {}", a, b, diff);
    println!("    f64: {} * {} = {}", a, b, prod);
    println!("    f64: {} / {} = {}", a, b, quot);
    
    // 测试 i32 运算
    let x: i32 = 42;
    let y: i32 = 13;
    let sum_i = x + y;
    let diff_i = x - y;
    let prod_i = x * y;
    
    println!("    i32: {} + {} = {}", x, y, sum_i);
    println!("    i32: {} - {} = {}", x, y, diff_i);
    println!("    i32: {} * {} = {}", x, y, prod_i);
}

fn test_math_operations() {
    // 测试基础数学运算
    let values = vec![1.0, 2.0, 3.0, 4.0, 5.0];
    let sum: f64 = values.iter().sum();
    let product: f64 = values.iter().product();
    
    println!("    向量 {:?} 的和: {}", values, sum);
    println!("    向量 {:?} 的积: {}", values, product);
    
    // 测试数值比较
    let a = 3.14159;
    let b = 2.71828;
    
    println!("    {} > {} = {}", a, b, a > b);
    println!("    {} < {} = {}", a, b, a < b);
    println!("    {} == {} = {}", a, a, a == a);
    
    // 测试数值转换
    let int_val = 42i32;
    let float_val = int_val as f64;
    let back_to_int = float_val as i32;
    
    println!("    类型转换: {} -> {} -> {}", int_val, float_val, back_to_int);
}
