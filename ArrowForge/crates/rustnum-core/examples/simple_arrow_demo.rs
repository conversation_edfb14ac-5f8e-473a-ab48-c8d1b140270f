use rustnum_core::array::SimpleArray;
use std::time::Instant;

#[cfg(feature = "arrow")]
use arrow::array::{Float64Array, Float32Array, Int32Array};

fn main() {
    println!("🚀 ArrowSciCompute 简化 Arrow 集成演示开始...");
    
    #[cfg(feature = "arrow")]
    {
        // 测试基础 Arrow 集成
        println!("🔗 测试基础 Arrow 集成...");
        test_basic_arrow_integration();
        println!("  ✅ 基础 Arrow 集成测试通过");

        // 测试性能对比
        println!("⚡ 测试性能对比...");
        test_performance_comparison();
        println!("  ✅ 性能对比测试通过");
    }
    
    #[cfg(not(feature = "arrow"))]
    {
        println!("⚠️  Arrow 功能未启用，请使用 --features=\"arrow\" 运行");
        println!("   示例：cargo run --example simple_arrow_demo --features=\"core,array,arrow\"");
    }

    println!("🎉 简化 Arrow 集成演示完成！");
    println!("✨ ArrowSciCompute 展示了与 Arrow 生态的兼容性！");
}

#[cfg(feature = "arrow")]
fn test_basic_arrow_integration() {
    println!("    🔄 测试 Arrow 数组创建和转换...");
    
    // 创建 Arrow Float64Array
    let arrow_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let arrow_array = Float64Array::from(arrow_data.clone());
    
    println!("      创建 Arrow Float64Array: 长度 {}", arrow_array.len());
    
    // 手动转换为 SimpleArray（展示兼容性）
    let simple_data: Vec<f64> = arrow_array.values().iter().copied().collect();
    let simple_array = SimpleArray::from_vec(simple_data).unwrap();
    
    println!("      转换为 SimpleArray: {:?}", simple_array);
    
    // 验证数据一致性
    for (i, &original) in arrow_data.iter().enumerate() {
        let converted = simple_array.get(&[i]).unwrap();
        assert!((original - converted).abs() < 1e-10, "数据不一致");
    }
    
    println!("      ✅ 数据一致性验证通过");
    
    // 测试矩阵重塑
    let matrix = SimpleArray::new(simple_array.into_vec(), vec![2, 3]).unwrap();
    println!("      重塑为矩阵 (2x3): {}", matrix);
    
    // 测试矩阵运算
    let matrix_t = matrix.transpose().unwrap();
    println!("      矩阵转置 (3x2): {}", matrix_t);
    
    // 测试不同类型的 Arrow 数组
    let int_arrow = Int32Array::from(vec![1, 2, 3, 4]);
    let int_simple_data: Vec<i32> = int_arrow.values().iter().copied().collect();
    let int_simple = SimpleArray::from_vec(int_simple_data).unwrap();
    
    println!("      Int32 转换: {:?}", int_simple);
    
    let float32_arrow = Float32Array::from(vec![1.5f32, 2.5f32, 3.5f32]);
    let float32_simple_data: Vec<f32> = float32_arrow.values().iter().copied().collect();
    let float32_simple = SimpleArray::from_vec(float32_simple_data).unwrap();
    
    println!("      Float32 转换: {:?}", float32_simple);
}

#[cfg(feature = "arrow")]
fn test_performance_comparison() {
    println!("    ⚡ Arrow vs SimpleArray 性能对比...");
    
    let size = 100000;
    
    // 测试 Arrow 数组创建性能
    let data: Vec<f64> = (0..size).map(|i| i as f64).collect();
    
    let start = Instant::now();
    let arrow_array = Float64Array::from(data.clone());
    let arrow_creation_time = start.elapsed();
    
    println!("      Arrow 数组创建 ({} 元素): {:?}", size, arrow_creation_time);
    
    // 测试 SimpleArray 创建性能
    let start = Instant::now();
    let simple_array = SimpleArray::from_vec(data.clone()).unwrap();
    let simple_creation_time = start.elapsed();
    
    println!("      SimpleArray 创建 ({} 元素): {:?}", size, simple_creation_time);
    
    // 测试数据访问性能
    let start = Instant::now();
    let mut arrow_sum = 0.0;
    for i in 0..1000 {
        arrow_sum += arrow_array.value(i % arrow_array.len());
    }
    let arrow_access_time = start.elapsed();
    
    println!("      Arrow 数据访问 (1000次): {:?} (sum: {})", arrow_access_time, arrow_sum);
    
    let start = Instant::now();
    let mut simple_sum = 0.0;
    for i in 0..1000 {
        simple_sum += simple_array.get(&[i % simple_array.len()]).unwrap();
    }
    let simple_access_time = start.elapsed();
    
    println!("      SimpleArray 数据访问 (1000次): {:?} (sum: {})", simple_access_time, simple_sum);
    
    // 测试聚合运算性能
    let start = Instant::now();
    let simple_total = simple_array.sum();
    let simple_sum_time = start.elapsed();
    
    println!("      SimpleArray 求和: {:?} (结果: {})", simple_sum_time, simple_total);
    
    // 测试矩阵运算性能
    let matrix_size = 200;
    let matrix_data: Vec<f64> = (0..matrix_size*matrix_size).map(|i| i as f64).collect();
    let matrix_a = SimpleArray::new(matrix_data.clone(), vec![matrix_size, matrix_size]).unwrap();
    let matrix_b = SimpleArray::new(matrix_data, vec![matrix_size, matrix_size]).unwrap();
    
    let start = Instant::now();
    let _matrix_product = matrix_a.matmul(&matrix_b).unwrap();
    let matmul_time = start.elapsed();
    
    println!("      SimpleArray 矩阵乘法 ({}x{}): {:?}", matrix_size, matrix_size, matmul_time);
    
    // 内存使用对比
    println!("      内存使用对比:");
    println!("        Arrow Float64Array: {} 字节", arrow_array.get_buffer_memory_size());
    
    let simple_memory = simple_array.len() * std::mem::size_of::<f64>() + 
                       std::mem::size_of::<SimpleArray<f64>>();
    println!("        SimpleArray: {} 字节", simple_memory);
    
    // 展示零成本抽象的优势
    println!("      🎯 Rust 零成本抽象优势:");
    println!("        - 编译时优化确保运行时性能");
    println!("        - 类型安全无运行时开销");
    println!("        - 内存布局优化");
    
    // 展示 Arrow 内存零拷贝的优势
    println!("      🎯 Arrow 内存零拷贝优势:");
    println!("        - 列式存储高效访问");
    println!("        - 跨语言数据交换无拷贝");
    println!("        - 内存对齐优化");
}
