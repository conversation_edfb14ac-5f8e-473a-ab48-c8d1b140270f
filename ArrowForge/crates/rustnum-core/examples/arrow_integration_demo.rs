use rustnum_core::array::SimpleArray;
use std::time::Instant;

#[cfg(feature = "arrow")]
use rustnum_core::arrow_integration::{
    ArrowArrayExt, SimpleArrayExt, ZeroCopyView, ArrowCompute, ComputeOps, MemoryLayout
};

#[cfg(feature = "arrow")]
use arrow::array::{Float64Array, Float32Array, Int32Array};

fn main() {
    println!("🚀 ArrowSciCompute Arrow 集成演示开始...");
    
    #[cfg(feature = "arrow")]
    {
        // 测试 Arrow 集成功能
        println!("🔗 测试 Arrow 集成功能...");
        test_arrow_integration();
        println!("  ✅ Arrow 集成测试通过");

        // 测试零拷贝性能
        println!("⚡ 测试零拷贝性能...");
        test_zero_copy_performance();
        println!("  ✅ 零拷贝性能测试通过");

        // 测试 Arrow Compute 集成
        println!("🧮 测试 Arrow Compute 集成...");
        test_arrow_compute();
        println!("  ✅ Arrow Compute 测试通过");

        // 测试内存布局优化
        println!("💾 测试内存布局优化...");
        test_memory_layout();
        println!("  ✅ 内存布局测试通过");
    }
    
    #[cfg(not(feature = "arrow"))]
    {
        println!("⚠️  Arrow 功能未启用，请使用 --features=\"arrow\" 运行");
        println!("   示例：cargo run --example arrow_integration_demo --features=\"core,array,arrow\"");
    }

    println!("🎉 Arrow 集成演示完成！");
    println!("✨ ArrowSciCompute 已实现与 Arrow 生态的深度集成！");
}

#[cfg(feature = "arrow")]
fn test_arrow_integration() {
    println!("    🔄 测试 Arrow 数组转换...");
    
    // 创建 Arrow Float64Array
    let arrow_data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let arrow_array = Float64Array::from(arrow_data.clone());
    
    // 零拷贝转换为 SimpleArray
    let simple_array = arrow_array.to_simple_array().unwrap();
    println!("      Arrow -> SimpleArray: {:?}", simple_array);
    
    // 转换为矩阵
    let matrix = arrow_array.to_matrix(2, 3).unwrap();
    println!("      Arrow -> Matrix (2x3): {}", matrix);
    
    // SimpleArray 转换回 Arrow
    let back_to_arrow = simple_array.to_arrow_array().unwrap();
    println!("      SimpleArray -> Arrow: 长度 {}", back_to_arrow.len());
    
    // 验证数据一致性
    let arrow_f64 = back_to_arrow.as_any()
        .downcast_ref::<Float64Array>()
        .unwrap();
    
    for (i, &original) in arrow_data.iter().enumerate() {
        let converted = arrow_f64.value(i);
        assert!((original - converted).abs() < 1e-10, "数据不一致");
    }
    
    println!("      ✅ 数据一致性验证通过");
}

#[cfg(feature = "arrow")]
fn test_zero_copy_performance() {
    println!("    ⚡ 零拷贝性能测试...");
    
    // 创建大型数组
    let size = 100000;
    let data: Vec<f64> = (0..size).map(|i| i as f64).collect();
    let arrow_array = Float64Array::from(data);
    
    // 测试零拷贝转换性能
    let start = Instant::now();
    let simple_array = arrow_array.to_simple_array().unwrap();
    let zero_copy_duration = start.elapsed();
    
    println!("      零拷贝转换 {} 元素: {:?}", size, zero_copy_duration);
    
    // 创建零拷贝视图
    let start = Instant::now();
    let zero_copy_view = ZeroCopyView::from_arrow(&arrow_array);
    let view_creation_duration = start.elapsed();
    
    println!("      零拷贝视图创建: {:?}", view_creation_duration);
    
    // 测试零拷贝计算
    let start = Instant::now();
    let sum = zero_copy_view.sum();
    let compute_duration = start.elapsed();
    
    println!("      零拷贝求和计算: {:?} (结果: {})", compute_duration, sum);
    
    // 测试零拷贝矩阵运算
    let matrix_size = 100;
    let matrix_data: Vec<f64> = (0..matrix_size*matrix_size).map(|i| i as f64).collect();
    let matrix_arrow = Float64Array::from(matrix_data);
    let matrix_view = ZeroCopyView::from_arrow(&matrix_arrow)
        .as_matrix(matrix_size, matrix_size).unwrap();
    
    let start = Instant::now();
    let matrix_product = matrix_view.matmul(&matrix_view).unwrap();
    let matmul_duration = start.elapsed();
    
    println!("      零拷贝矩阵乘法 {}x{}: {:?}", matrix_size, matrix_size, matmul_duration);
    println!("      矩阵乘积形状: {:?}", matrix_product.shape());
}

#[cfg(feature = "arrow")]
fn test_arrow_compute() {
    println!("    🧮 Arrow Compute 集成测试...");
    
    // 创建测试数组
    let a = SimpleArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![4]).unwrap();
    let b = SimpleArray::new(vec![5.0, 6.0, 7.0, 8.0], vec![4]).unwrap();
    
    println!("      数组 A: {:?}", a);
    println!("      数组 B: {:?}", b);
    
    // 测试 Arrow Compute 向量化运算
    let start = Instant::now();
    let sum = a.arrow_add(&b).unwrap();
    let add_duration = start.elapsed();
    println!("      Arrow 向量化加法: {:?} -> {:?} (耗时: {:?})", 
             a.as_slice(), sum.as_slice(), add_duration);
    
    let product = a.arrow_mul(&b).unwrap();
    println!("      Arrow 向量化乘法: {:?}", product.as_slice());
    
    let scalar_mul = a.arrow_scalar_mul(2.0).unwrap();
    println!("      Arrow 标量乘法: {:?}", scalar_mul.as_slice());
    
    // 测试聚合函数
    let sum_val = a.arrow_sum().unwrap();
    let mean_val = a.arrow_mean().unwrap();
    let max_val = a.arrow_max().unwrap();
    let min_val = a.arrow_min().unwrap();
    
    println!("      Arrow 聚合函数:");
    println!("        求和: {}", sum_val);
    println!("        平均值: {}", mean_val);
    println!("        最大值: {}", max_val);
    println!("        最小值: {}", min_val);
    
    // 测试高级计算操作
    let dot_product = a.dot_product(&b).unwrap();
    let norm = a.norm().unwrap();
    let normalized = a.normalize().unwrap();
    
    println!("      高级计算操作:");
    println!("        点积: {}", dot_product);
    println!("        范数: {}", norm);
    println!("        归一化: {:?}", normalized.as_slice());
}

#[cfg(feature = "arrow")]
fn test_memory_layout() {
    println!("    💾 内存布局优化测试...");
    
    // 创建测试数组
    let array = SimpleArray::new(vec![1.0, 2.0, 3.0, 4.0, 5.0], vec![5]).unwrap();
    
    // 测试内存布局信息
    let memory_usage = array.memory_usage();
    println!("      内存使用报告:");
    println!("{}", memory_usage.format_report());
    
    // 测试内存对齐
    println!("      内存对齐: {} 字节", array.memory_alignment());
    println!("      连续内存: {}", array.is_contiguous());
    
    // 测试内存优化
    let start = Instant::now();
    let optimized = array.optimize_layout().unwrap();
    let optimize_duration = start.elapsed();
    
    println!("      内存优化耗时: {:?}", optimize_duration);
    println!("      优化后数组: {:?}", optimized.as_slice());
    
    // 测试大数组的内存效率
    let large_size = 10000;
    let large_array = SimpleArray::<f64>::ones(&[large_size]).unwrap();
    let large_memory_usage = large_array.memory_usage();
    
    println!("      大数组 ({} 元素) 内存使用:", large_size);
    println!("        数据: {:.2} MB", large_memory_usage.data_bytes as f64 / 1024.0 / 1024.0);
    println!("        元数据: {} 字节", large_memory_usage.metadata_bytes);
    println!("        总计: {:.2} MB", large_memory_usage.total_bytes as f64 / 1024.0 / 1024.0);
}
