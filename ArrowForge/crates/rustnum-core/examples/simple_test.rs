use rustnum_core::traits::Numeric;

fn main() {
    println!("🚀 ArrowSciCompute 最简单功能验证开始...");

    // 测试数值 trait
    println!("🧮 测试数值 trait...");
    let a: f64 = 1.0;
    let b: f64 = 2.0;
    let sum = a + b;
    println!("  f64: {} + {} = {}", a, b, sum);
    
    let zero = f64::zero();
    let one = f64::one();
    println!("  f64::zero() = {}, f64::one() = {}", zero, one);
    
    println!("  ✅ 数值 trait 测试通过");

    println!("🎉 最简单功能验证完成！");
}
