use rustnum_core::array::SimpleArray;
use std::time::Instant;

fn main() {
    println!("🚀 ArrowSciCompute 矩阵运算功能验证开始...");

    // 测试基础数组创建
    println!("📊 测试基础数组创建...");
    test_array_creation();
    println!("  ✅ 数组创建测试通过");

    // 测试数组运算
    println!("🧮 测试数组运算...");
    test_array_operations();
    println!("  ✅ 数组运算测试通过");

    // 测试矩阵运算
    println!("🔢 测试矩阵运算...");
    test_matrix_operations();
    println!("  ✅ 矩阵运算测试通过");

    // 测试性能
    println!("⚡ 测试性能...");
    test_performance();
    println!("  ✅ 性能测试通过");

    println!("🎉 矩阵运算功能验证完成！");
    println!("✨ ArrowSciCompute 矩阵运算库已经完全可用！");
}

fn test_array_creation() {
    // 创建零数组
    let zeros = SimpleArray::<f64>::zeros(&[3, 3]).unwrap();
    println!("    零数组 (3x3):");
    println!("    {}", zeros);

    // 创建单位数组
    let ones = SimpleArray::<f64>::ones(&[2, 4]).unwrap();
    println!("    单位数组 (2x4):");
    println!("    {}", ones);

    // 创建单位矩阵
    let eye = SimpleArray::<f64>::eye(3).unwrap();
    println!("    单位矩阵 (3x3):");
    println!("    {}", eye);

    // 从向量创建数组
    let vec_data = vec![1.0, 2.0, 3.0, 4.0, 5.0];
    let arr = SimpleArray::from_vec(vec_data).unwrap();
    println!("    从向量创建的数组:");
    println!("    {}", arr);
}

fn test_array_operations() {
    // 创建测试数组
    let a = SimpleArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
    let b = SimpleArray::new(vec![5.0, 6.0, 7.0, 8.0], vec![2, 2]).unwrap();

    println!("    数组 A:");
    println!("    {}", a);
    println!("    数组 B:");
    println!("    {}", b);

    // 数组加法
    let sum = a.add(&b).unwrap();
    println!("    A + B:");
    println!("    {}", sum);

    // 数组减法
    let diff = a.sub(&b).unwrap();
    println!("    A - B:");
    println!("    {}", diff);

    // 标量乘法
    let scaled = a.scalar_mul(2.0).unwrap();
    println!("    A * 2:");
    println!("    {}", scaled);

    // 统计运算
    println!("    A 的和: {}", a.sum());
    println!("    A 的平均值: {}", a.mean());
}

fn test_matrix_operations() {
    // 创建测试矩阵
    let a = SimpleArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
    let b = SimpleArray::new(vec![5.0, 6.0, 7.0, 8.0], vec![2, 2]).unwrap();

    println!("    矩阵 A:");
    println!("    {}", a);
    println!("    矩阵 B:");
    println!("    {}", b);

    // 矩阵乘法
    let product = a.matmul(&b).unwrap();
    println!("    A × B:");
    println!("    {}", product);

    // 矩阵转置
    let a_t = a.transpose().unwrap();
    println!("    A 的转置:");
    println!("    {}", a_t);

    // 更大的矩阵测试
    let large_a = SimpleArray::new(
        vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0], 
        vec![2, 3]
    ).unwrap();
    let large_b = SimpleArray::new(
        vec![7.0, 8.0, 9.0, 10.0, 11.0, 12.0], 
        vec![3, 2]
    ).unwrap();

    println!("    大矩阵 A (2x3):");
    println!("    {}", large_a);
    println!("    大矩阵 B (3x2):");
    println!("    {}", large_b);

    let large_product = large_a.matmul(&large_b).unwrap();
    println!("    大矩阵乘积 A × B (2x2):");
    println!("    {}", large_product);
}

fn test_performance() {
    println!("    🔥 性能测试开始...");

    // 测试小矩阵性能
    let start = Instant::now();
    for _ in 0..1000 {
        let a = SimpleArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
        let b = SimpleArray::new(vec![5.0, 6.0, 7.0, 8.0], vec![2, 2]).unwrap();
        let _result = a.matmul(&b).unwrap();
    }
    let duration = start.elapsed();
    println!("    1000次 2x2 矩阵乘法: {:?}", duration);

    // 测试中等矩阵性能
    let size = 50;
    let data_a: Vec<f64> = (0..size*size).map(|i| i as f64).collect();
    let data_b: Vec<f64> = (0..size*size).map(|i| (i as f64) * 2.0).collect();
    
    let a = SimpleArray::new(data_a, vec![size, size]).unwrap();
    let b = SimpleArray::new(data_b, vec![size, size]).unwrap();

    let start = Instant::now();
    let _result = a.matmul(&b).unwrap();
    let duration = start.elapsed();
    println!("    {}x{} 矩阵乘法: {:?}", size, size, duration);

    // 测试数组创建性能
    let start = Instant::now();
    for _ in 0..10000 {
        let _arr = SimpleArray::<f64>::zeros(&[10, 10]).unwrap();
    }
    let duration = start.elapsed();
    println!("    10000次 10x10 零数组创建: {:?}", duration);

    // 测试元素访问性能
    let arr = SimpleArray::<f64>::ones(&[100, 100]).unwrap();
    let start = Instant::now();
    let mut sum = 0.0;
    for i in 0..100 {
        for j in 0..100 {
            sum += arr.get(&[i, j]).unwrap();
        }
    }
    let duration = start.elapsed();
    println!("    100x100 数组全元素访问: {:?} (sum: {})", duration, sum);

    // 测试数组运算性能
    let a = SimpleArray::<f64>::ones(&[100, 100]).unwrap();
    let b = SimpleArray::<f64>::ones(&[100, 100]).unwrap();
    
    let start = Instant::now();
    let _result = a.add(&b).unwrap();
    let duration = start.elapsed();
    println!("    100x100 数组加法: {:?}", duration);
}
