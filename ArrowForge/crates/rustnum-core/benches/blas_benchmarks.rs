use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::{
    backend::{BlasProvider, BlasRuntime, BlasWrapper},
    array::RustArray,
};

fn bench_matrix_multiplication(c: &mut Criterion) {
    let sizes = [64, 128, 256, 512, 1024];
    
    for &size in &sizes {
        let mut group = c.benchmark_group(format!("matrix_mul_{}x{}", size, size));
        
        // 创建随机矩阵
        let a = RustArray::rand((size, size));
        let b = RustArray::rand((size, size));
        
        // OpenBLAS基准测试
        group.bench_function("openblas", |bench| {
            BlasRuntime::global().set_provider(BlasProvider::OpenBlas);
            bench.iter(|| {
                black_box(a.dot(&b));
            });
        });
        
        // Intel MKL基准测试
        if cfg!(feature = "mkl") {
            group.bench_function("mkl", |bench| {
                BlasRuntime::global().set_provider(BlasProvider::IntelMkl);
                bench.iter(|| {
                    black_box(a.dot(&b));
                });
            });
        }
        
        // CUDA BLAS基准测试
        if cfg!(feature = "cuda") {
            group.bench_function("cublas", |bench| {
                BlasRuntime::global().set_provider(BlasProvider::CudaBlas);
                bench.iter(|| {
                    black_box(a.dot(&b));
                });
            });
        }
        
        group.finish();
    }
}

fn bench_svd(c: &mut Criterion) {
    let sizes = [32, 64, 128, 256];
    
    for &size in &sizes {
        let mut group = c.benchmark_group(format!("svd_{}x{}", size, size));
        
        // 创建随机矩阵
        let a = RustArray::rand((size, size));
        
        // OpenBLAS基准测试
        group.bench_function("openblas", |bench| {
            BlasRuntime::global().set_provider(BlasProvider::OpenBlas);
            bench.iter(|| {
                black_box(a.svd());
            });
        });
        
        // Intel MKL基准测试
        if cfg!(feature = "mkl") {
            group.bench_function("mkl", |bench| {
                BlasRuntime::global().set_provider(BlasProvider::IntelMkl);
                bench.iter(|| {
                    black_box(a.svd());
                });
            });
        }
        
        group.finish();
    }
}

fn bench_eigendecomposition(c: &mut Criterion) {
    let sizes = [32, 64, 128, 256];
    
    for &size in &sizes {
        let mut group = c.benchmark_group(format!("eig_{}x{}", size, size));
        
        // 创建随机对称矩阵
        let mut a = RustArray::rand((size, size));
        a = &a + &a.t(); // 确保对称性
        
        // OpenBLAS基准测试
        group.bench_function("openblas", |bench| {
            BlasRuntime::global().set_provider(BlasProvider::OpenBlas);
            bench.iter(|| {
                black_box(a.eig());
            });
        });
        
        // Intel MKL基准测试
        if cfg!(feature = "mkl") {
            group.bench_function("mkl", |bench| {
                BlasRuntime::global().set_provider(BlasProvider::IntelMkl);
                bench.iter(|| {
                    black_box(a.eig());
                });
            });
        }
        
        group.finish();
    }
}

criterion_group!(
    benches,
    bench_matrix_multiplication,
    bench_svd,
    bench_eigendecomposition
);
criterion_main!(benches);
