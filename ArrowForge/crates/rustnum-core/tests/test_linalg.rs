//! 线性代数算法单元测试
//! 
//! 本模块包含对 ArrowSciCompute 线性代数功能的完整测试，包括：
//! - LU 分解测试
//! - QR 分解测试  
//! - 线性方程组求解测试
//! - 数值精度验证
//! - 边界条件测试

use rustnum_core::array::lapack::LapackOps;
use rustnum_core::array::creation::ArrayCreation;
use rustnum_core::array::RustArray;

/// 测试辅助函数：检查两个浮点数是否近似相等
fn approx_eq(a: f64, b: f64, epsilon: f64) -> bool {
    (a - b).abs() < epsilon
}

/// 测试辅助函数：检查两个矩阵是否近似相等
fn matrices_approx_eq(a: &RustArray<f64>, b: &RustArray<f64>, epsilon: f64) -> bool {
    if a.shape() != b.shape() {
        return false;
    }
    
    for i in 0..a.shape()[0] {
        for j in 0..a.shape()[1] {
            let val_a = a.get(&[i, j]).unwrap_or(0.0);
            let val_b = b.get(&[i, j]).unwrap_or(0.0);
            if !approx_eq(val_a, val_b, epsilon) {
                return false;
            }
        }
    }
    true
}

/// 测试辅助函数：创建单位矩阵
fn create_identity_matrix(n: usize) -> RustArray<f64> {
    let mut matrix = RustArray::zeros(&[n, n]).unwrap();
    for i in 0..n {
        matrix.set(&[i, i], 1.0).unwrap();
    }
    matrix
}

/// 测试辅助函数：矩阵乘法
fn matrix_multiply(a: &RustArray<f64>, b: &RustArray<f64>) -> RustArray<f64> {
    let (m, k) = (a.shape()[0], a.shape()[1]);
    let (k2, n) = (b.shape()[0], b.shape()[1]);
    assert_eq!(k, k2, "矩阵维度不匹配");
    
    let mut result = RustArray::zeros(&[m, n]).unwrap();
    for i in 0..m {
        for j in 0..n {
            let mut sum = 0.0;
            for l in 0..k {
                sum += a.get(&[i, l]).unwrap_or(0.0) * b.get(&[l, j]).unwrap_or(0.0);
            }
            result.set(&[i, j], sum).unwrap();
        }
    }
    result
}

#[cfg(test)]
mod lu_decomposition_tests {
    use super::*;

    #[test]
    fn test_lu_decomposition_2x2() {
        // 测试 2x2 矩阵的 LU 分解
        let mut matrix = RustArray::zeros(&[2, 2]).unwrap();
        matrix.set(&[0, 0], 2.0).unwrap();
        matrix.set(&[0, 1], 1.0).unwrap();
        matrix.set(&[1, 0], 1.0).unwrap();
        matrix.set(&[1, 1], 1.0).unwrap();
        
        let result = matrix.lu_decomposition();
        assert!(result.is_ok(), "LU分解应该成功");
        
        let (l, u, _p) = result.unwrap();
        
        // 验证 L 是下三角矩阵，对角线为1
        assert!(approx_eq(l.get(&[0, 0]).unwrap(), 1.0, 1e-10));
        assert!(approx_eq(l.get(&[1, 1]).unwrap(), 1.0, 1e-10));
        assert!(approx_eq(l.get(&[0, 1]).unwrap(), 0.0, 1e-10));
        
        // 验证 U 是上三角矩阵
        assert!(approx_eq(u.get(&[1, 0]).unwrap(), 0.0, 1e-10));
        
        // 验证 L * U 近似等于原矩阵（忽略置换）
        let lu_product = matrix_multiply(&l, &u);
        println!("L矩阵: {:?}", l);
        println!("U矩阵: {:?}", u);
        println!("L*U: {:?}", lu_product);
    }

    #[test]
    fn test_lu_decomposition_3x3() {
        // 测试 3x3 矩阵的 LU 分解
        let mut matrix = RustArray::zeros(&[3, 3]).unwrap();
        matrix.set(&[0, 0], 2.0).unwrap();
        matrix.set(&[0, 1], -1.0).unwrap();
        matrix.set(&[0, 2], 0.0).unwrap();
        matrix.set(&[1, 0], -1.0).unwrap();
        matrix.set(&[1, 1], 2.0).unwrap();
        matrix.set(&[1, 2], -1.0).unwrap();
        matrix.set(&[2, 0], 0.0).unwrap();
        matrix.set(&[2, 1], -1.0).unwrap();
        matrix.set(&[2, 2], 2.0).unwrap();
        
        let result = matrix.lu_decomposition();
        assert!(result.is_ok(), "3x3矩阵LU分解应该成功");
        
        let (l, u, _p) = result.unwrap();
        
        // 验证 L 矩阵对角线为1
        for i in 0..3 {
            assert!(approx_eq(l.get(&[i, i]).unwrap(), 1.0, 1e-10));
        }
        
        // 验证 L 是下三角矩阵
        for i in 0..3 {
            for j in (i+1)..3 {
                assert!(approx_eq(l.get(&[i, j]).unwrap(), 0.0, 1e-10));
            }
        }
        
        // 验证 U 是上三角矩阵
        for i in 1..3 {
            for j in 0..i {
                assert!(approx_eq(u.get(&[i, j]).unwrap(), 0.0, 1e-10));
            }
        }
    }

    #[test]
    fn test_lu_decomposition_singular_matrix() {
        // 测试奇异矩阵（不可逆矩阵）
        let mut matrix = RustArray::zeros(&[2, 2]).unwrap();
        matrix.set(&[0, 0], 1.0).unwrap();
        matrix.set(&[0, 1], 2.0).unwrap();
        matrix.set(&[1, 0], 2.0).unwrap();
        matrix.set(&[1, 1], 4.0).unwrap(); // 第二行是第一行的2倍
        
        let result = matrix.lu_decomposition();
        // 奇异矩阵应该返回错误
        assert!(result.is_err(), "奇异矩阵的LU分解应该失败");
    }

    #[test]
    fn test_lu_decomposition_non_square_matrix() {
        // 测试非方阵
        let matrix = RustArray::zeros(&[2, 3]).unwrap();
        let result = matrix.lu_decomposition();
        assert!(result.is_err(), "非方阵的LU分解应该失败");
    }
}

#[cfg(test)]
mod qr_decomposition_tests {
    use super::*;

    #[test]
    fn test_qr_decomposition_2x2() {
        // 测试 2x2 矩阵的 QR 分解
        let mut matrix = RustArray::zeros(&[2, 2]).unwrap();
        matrix.set(&[0, 0], 1.0).unwrap();
        matrix.set(&[0, 1], 1.0).unwrap();
        matrix.set(&[1, 0], 0.0).unwrap();
        matrix.set(&[1, 1], 1.0).unwrap();
        
        let result = matrix.qr_decomposition();
        assert!(result.is_ok(), "QR分解应该成功");
        
        let (q, r) = result.unwrap();
        
        // 验证 Q 是正交矩阵 (Q^T * Q = I)
        let qt = transpose(&q);
        let qtq = matrix_multiply(&qt, &q);
        let identity = create_identity_matrix(2);
        assert!(matrices_approx_eq(&qtq, &identity, 1e-10), "Q应该是正交矩阵");
        
        // 验证 R 是上三角矩阵
        assert!(approx_eq(r.get(&[1, 0]).unwrap(), 0.0, 1e-10), "R应该是上三角矩阵");
        
        // 验证 Q * R = A
        let qr_product = matrix_multiply(&q, &r);
        assert!(matrices_approx_eq(&matrix, &qr_product, 1e-10), "Q*R应该等于原矩阵");
    }

    #[test]
    fn test_qr_decomposition_3x3() {
        // 测试 3x3 矩阵的 QR 分解
        let mut matrix = RustArray::zeros(&[3, 3]).unwrap();
        matrix.set(&[0, 0], 12.0).unwrap();
        matrix.set(&[0, 1], -51.0).unwrap();
        matrix.set(&[0, 2], 4.0).unwrap();
        matrix.set(&[1, 0], 6.0).unwrap();
        matrix.set(&[1, 1], 167.0).unwrap();
        matrix.set(&[1, 2], -68.0).unwrap();
        matrix.set(&[2, 0], -4.0).unwrap();
        matrix.set(&[2, 1], 24.0).unwrap();
        matrix.set(&[2, 2], -41.0).unwrap();
        
        let result = matrix.qr_decomposition();
        assert!(result.is_ok(), "3x3矩阵QR分解应该成功");
        
        let (q, r) = result.unwrap();
        
        // 验证 R 是上三角矩阵
        for i in 1..3 {
            for j in 0..i {
                assert!(approx_eq(r.get(&[i, j]).unwrap(), 0.0, 1e-8), 
                       "R[{}, {}] = {} 应该为0", i, j, r.get(&[i, j]).unwrap());
            }
        }
    }

    #[test]
    fn test_qr_decomposition_non_square_matrix() {
        // 测试非方阵
        let matrix = RustArray::zeros(&[3, 2]).unwrap();
        let result = matrix.qr_decomposition();
        assert!(result.is_err(), "非方阵的QR分解应该失败");
    }

    /// 辅助函数：矩阵转置
    fn transpose(matrix: &RustArray<f64>) -> RustArray<f64> {
        let (m, n) = (matrix.shape()[0], matrix.shape()[1]);
        let mut result = RustArray::zeros(&[n, m]).unwrap();
        for i in 0..m {
            for j in 0..n {
                let val = matrix.get(&[i, j]).unwrap_or(0.0);
                result.set(&[j, i], val).unwrap();
            }
        }
        result
    }
}

#[cfg(test)]
mod linear_solver_tests {
    use super::*;

    #[test]
    fn test_solve_2x2_system() {
        // 测试 2x2 线性方程组求解
        // 2x + y = 3
        // x + y = 2
        // 解: x = 1, y = 1
        
        let mut a = RustArray::zeros(&[2, 2]).unwrap();
        a.set(&[0, 0], 2.0).unwrap();
        a.set(&[0, 1], 1.0).unwrap();
        a.set(&[1, 0], 1.0).unwrap();
        a.set(&[1, 1], 1.0).unwrap();
        
        let mut b = RustArray::zeros(&[2]).unwrap();
        b.set(&[0], 3.0).unwrap();
        b.set(&[1], 2.0).unwrap();
        
        let result = a.solve(&b);
        assert!(result.is_ok(), "线性方程组求解应该成功");
        
        let x = result.unwrap();
        assert!(approx_eq(x.get(&[0]).unwrap(), 1.0, 1e-10), "x应该等于1");
        assert!(approx_eq(x.get(&[1]).unwrap(), 1.0, 1e-10), "y应该等于1");
    }

    #[test]
    fn test_solve_3x3_system() {
        // 测试 3x3 线性方程组求解
        let mut a = RustArray::zeros(&[3, 3]).unwrap();
        a.set(&[0, 0], 2.0).unwrap();
        a.set(&[0, 1], -1.0).unwrap();
        a.set(&[0, 2], 0.0).unwrap();
        a.set(&[1, 0], -1.0).unwrap();
        a.set(&[1, 1], 2.0).unwrap();
        a.set(&[1, 2], -1.0).unwrap();
        a.set(&[2, 0], 0.0).unwrap();
        a.set(&[2, 1], -1.0).unwrap();
        a.set(&[2, 2], 2.0).unwrap();
        
        let mut b = RustArray::zeros(&[3]).unwrap();
        b.set(&[0], 1.0).unwrap();
        b.set(&[1], 0.0).unwrap();
        b.set(&[2], 1.0).unwrap();
        
        let result = a.solve(&b);
        assert!(result.is_ok(), "3x3线性方程组求解应该成功");
        
        let x = result.unwrap();
        
        // 验证解的正确性：A * x = b
        // 创建 x 的列向量形式进行矩阵乘法
        let mut x_col = RustArray::zeros(&[3, 1]).unwrap();
        for i in 0..3 {
            x_col.set(&[i, 0], x.get(&[i]).unwrap()).unwrap();
        }
        let ax = matrix_multiply(&a, &x_col);
        for i in 0..3 {
            let expected = b.get(&[i]).unwrap();
            let actual = ax.get(&[i, 0]).unwrap();
            assert!(approx_eq(actual, expected, 1e-8), 
                   "A*x[{}] = {} 应该等于 b[{}] = {}", i, actual, i, expected);
        }
    }

    #[test]
    fn test_solve_singular_system() {
        // 测试奇异矩阵的线性方程组
        let mut a = RustArray::zeros(&[2, 2]).unwrap();
        a.set(&[0, 0], 1.0).unwrap();
        a.set(&[0, 1], 2.0).unwrap();
        a.set(&[1, 0], 2.0).unwrap();
        a.set(&[1, 1], 4.0).unwrap(); // 奇异矩阵
        
        let b = RustArray::zeros(&[2]).unwrap();
        
        let result = a.solve(&b);
        assert!(result.is_err(), "奇异矩阵的线性方程组求解应该失败");
    }

    #[test]
    fn test_solve_dimension_mismatch() {
        // 测试维度不匹配的情况
        let a = RustArray::zeros(&[2, 2]).unwrap();
        let b = RustArray::zeros(&[3]).unwrap(); // 维度不匹配
        
        let result = a.solve(&b);
        assert!(result.is_err(), "维度不匹配的线性方程组求解应该失败");
    }
}
