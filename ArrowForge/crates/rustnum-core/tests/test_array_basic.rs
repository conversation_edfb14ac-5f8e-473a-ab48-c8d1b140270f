//! 数组基础功能模块隔离测试
//! 
//! 本测试只验证数组的基础创建和访问功能，避免复杂的依赖

use rustnum_core::array::RustArray;
use rustnum_core::array::creation::ArrayCreation;
use rustnum_core::traits::Numeric;

#[cfg(test)]
mod array_basic_tests {
    use super::*;

    #[test]
    fn test_numeric_traits_basic() {
        // 测试数值 trait 的基本功能
        println!("🧮 测试数值 trait 基础功能...");
        
        assert_eq!(f64::zero(), 0.0);
        assert_eq!(f64::one(), 1.0);
        
        let x = 3.0_f64;
        assert!((x.sqrt() - 1.732050807568877).abs() < 1e-10);
        assert_eq!(x.abs(), 3.0);
        assert_eq!(x.signum(), 1.0);
        
        let y = -2.0_f64;
        assert_eq!(y.abs(), 2.0);
        assert_eq!(y.signum(), -1.0);
        
        println!("  ✅ f64 数值 trait 测试通过");
        
        // 测试 f32
        assert_eq!(f32::zero(), 0.0);
        assert_eq!(f32::one(), 1.0);
        println!("  ✅ f32 数值 trait 测试通过");
        
        // 测试整数类型
        assert_eq!(i32::zero(), 0);
        assert_eq!(i32::one(), 1);
        assert_eq!(i64::zero(), 0);
        assert_eq!(i64::one(), 1);
        println!("  ✅ 整数数值 trait 测试通过");
    }

    #[test]
    fn test_array_creation_f64() {
        println!("🏗️ 测试 f64 数组创建...");
        
        // 测试 zeros 创建
        let zeros = RustArray::<f64>::zeros(&[2, 3]).unwrap();
        assert_eq!(zeros.shape(), &[2, 3]);
        
        for i in 0..2 {
            for j in 0..3 {
                assert_eq!(zeros.get(&[i, j]).unwrap(), 0.0);
            }
        }
        println!("  ✅ zeros 创建测试通过");
        
        // 测试 ones 创建
        let ones = RustArray::<f64>::ones(&[3, 2]).unwrap();
        assert_eq!(ones.shape(), &[3, 2]);
        
        for i in 0..3 {
            for j in 0..2 {
                assert_eq!(ones.get(&[i, j]).unwrap(), 1.0);
            }
        }
        println!("  ✅ ones 创建测试通过");
        
        // 测试单位矩阵创建
        let eye = RustArray::<f64>::eye(3).unwrap();
        assert_eq!(eye.shape(), &[3, 3]);
        
        for i in 0..3 {
            for j in 0..3 {
                let expected = if i == j { 1.0 } else { 0.0 };
                assert_eq!(eye.get(&[i, j]).unwrap(), expected);
            }
        }
        println!("  ✅ eye 创建测试通过");
    }

    #[test]
    fn test_array_creation_f32() {
        println!("🏗️ 测试 f32 数组创建...");
        
        let zeros = RustArray::<f32>::zeros(&[2, 2]).unwrap();
        assert_eq!(zeros.shape(), &[2, 2]);
        
        for i in 0..2 {
            for j in 0..2 {
                assert_eq!(zeros.get(&[i, j]).unwrap(), 0.0);
            }
        }
        
        let ones = RustArray::<f32>::ones(&[2, 2]).unwrap();
        for i in 0..2 {
            for j in 0..2 {
                assert_eq!(ones.get(&[i, j]).unwrap(), 1.0);
            }
        }
        
        println!("  ✅ f32 数组创建测试通过");
    }

    #[test]
    fn test_array_access_and_modification() {
        println!("🔧 测试数组访问和修改...");
        
        let mut matrix = RustArray::<f64>::zeros(&[2, 2]).unwrap();
        
        // 测试设置值
        matrix.set(&[0, 0], 1.0).unwrap();
        matrix.set(&[0, 1], 2.0).unwrap();
        matrix.set(&[1, 0], 3.0).unwrap();
        matrix.set(&[1, 1], 4.0).unwrap();
        
        // 测试获取值
        assert_eq!(matrix.get(&[0, 0]).unwrap(), 1.0);
        assert_eq!(matrix.get(&[0, 1]).unwrap(), 2.0);
        assert_eq!(matrix.get(&[1, 0]).unwrap(), 3.0);
        assert_eq!(matrix.get(&[1, 1]).unwrap(), 4.0);
        
        println!("  ✅ 数组访问和修改测试通过");
    }

    #[test]
    fn test_array_error_handling() {
        println!("⚠️ 测试错误处理...");
        
        // 测试无效形状
        let result = RustArray::<f64>::zeros(&[]);
        assert!(result.is_err(), "空形状应该返回错误");
        
        // 测试越界访问
        let matrix = RustArray::<f64>::zeros(&[2, 2]).unwrap();
        let result = matrix.get(&[5, 5]);
        assert!(result.is_none(), "越界访问应该返回 None");
        
        // 测试越界设置
        let mut matrix = RustArray::<f64>::zeros(&[2, 2]).unwrap();
        let result = matrix.set(&[5, 5], 1.0);
        assert!(result.is_err(), "越界设置应该返回错误");
        
        println!("  ✅ 错误处理测试通过");
    }

    #[test]
    fn test_array_shapes_and_dimensions() {
        println!("📐 测试数组形状和维度...");
        
        // 1D 数组
        let vec1d = RustArray::<f64>::zeros(&[5]).unwrap();
        assert_eq!(vec1d.shape(), &[5]);
        assert_eq!(vec1d.shape().len(), 1);
        
        // 2D 数组
        let mat2d = RustArray::<f64>::zeros(&[3, 4]).unwrap();
        assert_eq!(mat2d.shape(), &[3, 4]);
        assert_eq!(mat2d.shape().len(), 2);
        
        // 3D 数组
        let tensor3d = RustArray::<f64>::zeros(&[2, 3, 4]).unwrap();
        assert_eq!(tensor3d.shape(), &[2, 3, 4]);
        assert_eq!(tensor3d.shape().len(), 3);
        
        println!("  ✅ 形状和维度测试通过");
    }

    #[test]
    fn test_array_clone() {
        println!("📋 测试数组克隆...");
        
        let mut original = RustArray::<f64>::zeros(&[2, 2]).unwrap();
        original.set(&[0, 0], 42.0).unwrap();
        original.set(&[1, 1], 24.0).unwrap();
        
        let cloned = original.clone();
        
        // 验证克隆的数组有相同的值
        assert_eq!(cloned.get(&[0, 0]).unwrap(), 42.0);
        assert_eq!(cloned.get(&[1, 1]).unwrap(), 24.0);
        assert_eq!(cloned.shape(), original.shape());
        
        println!("  ✅ 数组克隆测试通过");
    }

    #[test]
    fn test_performance_basic() {
        println!("⏱️ 基础性能测试...");
        
        use std::time::Instant;
        
        // 测试创建性能
        let start = Instant::now();
        for _ in 0..1000 {
            let _matrix = RustArray::<f64>::zeros(&[10, 10]).unwrap();
        }
        let creation_time = start.elapsed();
        println!("  创建 1000 个 10x10 矩阵: {:?}", creation_time);
        
        // 测试访问性能
        let matrix = RustArray::<f64>::ones(&[100, 100]).unwrap();
        let start = Instant::now();
        let mut sum = 0.0;
        for i in 0..100 {
            for j in 0..100 {
                sum += matrix.get(&[i, j]).unwrap_or(0.0);
            }
        }
        let access_time = start.elapsed();
        println!("  访问 100x100 矩阵所有元素: {:?}, sum: {}", access_time, sum);
        
        // 测试修改性能
        let mut matrix = RustArray::<f64>::zeros(&[50, 50]).unwrap();
        let start = Instant::now();
        for i in 0..50 {
            for j in 0..50 {
                matrix.set(&[i, j], (i * j) as f64).unwrap();
            }
        }
        let modify_time = start.elapsed();
        println!("  修改 50x50 矩阵所有元素: {:?}", modify_time);
        
        println!("  ✅ 基础性能测试完成");
    }

    #[test]
    fn test_trait_system_integration() {
        println!("🔗 测试 trait 系统集成...");
        
        // 验证不同数值类型都实现了 Numeric trait
        fn test_numeric<T: Numeric>() -> (T, T) {
            (T::zero(), T::one())
        }
        
        let (zero_f64, one_f64) = test_numeric::<f64>();
        assert_eq!(zero_f64, 0.0);
        assert_eq!(one_f64, 1.0);
        
        let (zero_f32, one_f32) = test_numeric::<f32>();
        assert_eq!(zero_f32, 0.0);
        assert_eq!(one_f32, 1.0);
        
        let (zero_i32, one_i32) = test_numeric::<i32>();
        assert_eq!(zero_i32, 0);
        assert_eq!(one_i32, 1);
        
        let (zero_i64, one_i64) = test_numeric::<i64>();
        assert_eq!(zero_i64, 0);
        assert_eq!(one_i64, 1);
        
        println!("  ✅ Trait 系统集成测试通过");
    }

    #[test]
    fn test_memory_safety() {
        println!("🛡️ 测试内存安全...");
        
        // 创建大量数组测试内存管理
        let mut arrays = Vec::new();
        for i in 1..=10 {
            let array = RustArray::<f64>::zeros(&[i, i]).unwrap();
            arrays.push(array);
        }
        
        // 验证所有数组都可以正常访问
        for (idx, array) in arrays.iter().enumerate() {
            let size = idx + 1;
            assert_eq!(array.shape(), &[size, size]);
            assert_eq!(array.get(&[0, 0]).unwrap(), 0.0);
        }
        
        println!("  ✅ 内存安全测试通过");
    }
}

#[cfg(test)]
mod integration_tests {
    use super::*;

    #[test]
    fn test_full_workflow() {
        println!("🚀 完整工作流程测试...");
        
        // 1. 创建数组
        let mut matrix_a = RustArray::<f64>::zeros(&[3, 3]).unwrap();
        let mut matrix_b = RustArray::<f64>::ones(&[3, 3]).unwrap();
        
        // 2. 填充数据
        for i in 0..3 {
            for j in 0..3 {
                matrix_a.set(&[i, j], (i * 3 + j) as f64).unwrap();
                matrix_b.set(&[i, j], (i + j) as f64).unwrap();
            }
        }
        
        // 3. 验证数据
        assert_eq!(matrix_a.get(&[0, 0]).unwrap(), 0.0);
        assert_eq!(matrix_a.get(&[1, 1]).unwrap(), 4.0);
        assert_eq!(matrix_a.get(&[2, 2]).unwrap(), 8.0);
        
        assert_eq!(matrix_b.get(&[0, 0]).unwrap(), 0.0);
        assert_eq!(matrix_b.get(&[1, 1]).unwrap(), 2.0);
        assert_eq!(matrix_b.get(&[2, 2]).unwrap(), 4.0);
        
        // 4. 克隆和比较
        let matrix_a_clone = matrix_a.clone();
        for i in 0..3 {
            for j in 0..3 {
                assert_eq!(
                    matrix_a.get(&[i, j]).unwrap(),
                    matrix_a_clone.get(&[i, j]).unwrap()
                );
            }
        }
        
        println!("  ✅ 完整工作流程测试通过");
        println!("🎉 所有基础数组功能测试完成！");
    }
}
