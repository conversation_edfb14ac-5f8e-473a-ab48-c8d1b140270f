#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Default)]
pub struct AllocationStats {
    /// 总分配次数
    pub total_allocations: usize,
    /// 总释放次数
    pub total_deallocations: usize,
    /// 总分配字节数
    pub total_bytes_allocated: usize,
    /// 总释放字节数
    pub total_bytes_freed: usize,
    /// 当前活跃分配数
    pub active_allocations: usize,
    /// 当前使用的内存量
    pub current_bytes_used: usize,
    /// 峰值内存使用量
    pub peak_bytes_used: usize,
    /// 小对象分配次数
    pub small_allocations: usize,
    /// 中等对象分配次数
    pub medium_allocations: usize,
    /// 大对象分配次数
    pub large_allocations: usize,
}

impl AllocationStats {
    /// 记录新的分配
    pub fn record_allocation(&mut self, size: usize) {
        self.total_allocations += 1;
        self.total_bytes_allocated += size;
        self.active_allocations += 1;
        self.current_bytes_used += size;
        
        if self.current_bytes_used > self.peak_bytes_used {
            self.peak_bytes_used = self.current_bytes_used;
        }

        match size {
            s if s <= 4096 => self.small_allocations += 1,
            s if s <= 1024 * 1024 => self.medium_allocations += 1,
            _ => self.large_allocations += 1,
        }
    }

    /// 记录内存释放
    pub fn record_deallocation(&mut self, size: usize) {
        self.total_deallocations += 1;
        self.total_bytes_freed += size;
        self.active_allocations -= 1;
        self.current_bytes_used -= size;
    }

    /// 计算内存碎片率
    pub fn fragmentation_ratio(&self) -> f64 {
        if self.current_bytes_used == 0 {
            0.0
        } else {
            1.0 - (self.total_bytes_allocated - self.total_bytes_freed) as f64
                / self.current_bytes_used as f64
        }
    }

    /// 计算平均分配大小
    pub fn average_allocation_size(&self) -> usize {
        if self.total_allocations == 0 {
            0
        } else {
            self.total_bytes_allocated / self.total_allocations
        }
    }

    /// 生成统计报告
    pub fn generate_report(&self) -> String {
        format!(
            "Memory Allocation Statistics:
            - Total Allocations: {}
            - Total Deallocations: {}
            - Total Bytes Allocated: {} bytes
            - Total Bytes Freed: {} bytes
            - Active Allocations: {}
            - Current Memory Usage: {} bytes
            - Peak Memory Usage: {} bytes
            - Small Object Allocations: {}
            - Medium Object Allocations: {}
            - Large Object Allocations: {}
            - Average Allocation Size: {} bytes
            - Fragmentation Ratio: {:.2}%",
            self.total_allocations,
            self.total_deallocations,
            self.total_bytes_allocated,
            self.total_bytes_freed,
            self.active_allocations,
            self.current_bytes_used,
            self.peak_bytes_used,
            self.small_allocations,
            self.medium_allocations,
            self.large_allocations,
            self.average_allocation_size(),
            self.fragmentation_ratio() * 100.0
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_allocation_stats() {
        let mut stats = AllocationStats::default();
        
        // 测试小对象分配
        stats.record_allocation(1024);
        assert_eq!(stats.small_allocations, 1);
        assert_eq!(stats.total_allocations, 1);
        
        // 测试中等对象分配
        stats.record_allocation(512 * 1024);
        assert_eq!(stats.medium_allocations, 1);
        
        // 测试大对象分配
        stats.record_allocation(2 * 1024 * 1024);
        assert_eq!(stats.large_allocations, 1);
        
        // 测试释放
        stats.record_deallocation(1024);
        assert_eq!(stats.total_deallocations, 1);
        
        // 测试统计
        assert!(stats.fragmentation_ratio() >= 0.0);
        assert!(stats.average_allocation_size() > 0);
        
        // 测试报告生成
        let report = stats.generate_report();
        assert!(report.contains("Memory Allocation Statistics"));
    }
}
