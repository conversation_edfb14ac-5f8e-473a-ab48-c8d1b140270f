use std::ptr::NonNull;
use std::alloc::Layout;
use std::collections::HashMap;
use super::error::MemoryError;
use super::{PreallocStrategy, MemoryPool};

/// 智能内存分配器
pub struct SmartAllocator {
    /// 内存池
    pool: MemoryPool,
    /// 对齐要求
    alignment: usize,
    /// 预分配策略
    prealloc_strategy: PreallocStrategy,
}

/// Slab分配器 - 用于小对象的固定大小分配
pub struct SlabAllocator {
    /// 内存块大小
    block_size: usize,
    /// 总容量
    capacity: usize,
    /// 可用块列表
    free_blocks: Vec<NonNull<u8>>,
    /// 分配映射表
    allocations: HashMap<NonNull<u8>, usize>,
    /// 底层内存
    memory: NonNull<u8>,
}

impl SlabAllocator {
    /// 创建新的Slab分配器
    pub fn new(block_size: usize, capacity: usize) -> Self {
        // 确保容量是块大小的整数倍
        let capacity = (capacity / block_size) * block_size;

        // 使用合理的对齐值，不超过系统页面大小
        let align = block_size.min(4096).next_power_of_two();
        
        // 分配底层内存
        let layout = Layout::from_size_align(capacity, align)
            .expect("Invalid layout");
        let memory = unsafe {
            NonNull::new(std::alloc::alloc(layout))
                .expect("Memory allocation failed")
        };

        let mut free_blocks = Vec::new();
        let mut current = memory;
        
        // 初始化可用块列表
        while (current.as_ptr() as usize) < (memory.as_ptr() as usize + capacity) {
            free_blocks.push(current);
            unsafe {
                current = NonNull::new_unchecked(
                    current.as_ptr().add(block_size)
                );
            }
        }

        Self {
            block_size,
            capacity,
            free_blocks,
            allocations: HashMap::new(),
            memory,
        }
    }

    /// 分配内存
    pub fn allocate(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, MemoryError> {
        // 检查对齐要求是否合理（不超过4KB）
        if align > 4096 {
            return Err(MemoryError::AlignmentError);
        }

        // 计算需要的块数
        let blocks_needed = (size + self.block_size - 1) / self.block_size;
        
        // 检查是否有足够的连续块
        if self.free_blocks.len() < blocks_needed {
            return Err(MemoryError::OutOfMemory);
        }

        // 找到满足对齐要求的块
        let mut found_index = None;
        for (i, &ptr) in self.free_blocks.iter().enumerate() {
            if ptr.as_ptr() as usize % align == 0 {
                found_index = Some(i);
                break;
            }
        }
        
        let base_ptr = match found_index {
            Some(index) => {
                let ptr = self.free_blocks.remove(index);
                // 移除后续需要的块
                for _ in 1..blocks_needed {
                    if !self.free_blocks.is_empty() {
                        self.free_blocks.remove(0);
                    }
                }
                ptr
            }
            None => return Err(MemoryError::AlignmentError),
        };

        // 记录分配信息
        self.allocations.insert(base_ptr, blocks_needed);

        Ok(base_ptr)
    }

    /// 释放内存
    pub fn deallocate(&mut self, ptr: NonNull<u8>, _layout: Layout) {
        if let Some(blocks) = self.allocations.remove(&ptr) {
            // 将块添加回可用列表
            let mut current = ptr;
            for _ in 0..blocks {
                self.free_blocks.push(current);
                unsafe {
                    current = NonNull::new_unchecked(
                        current.as_ptr().add(self.block_size)
                    );
                }
            }
        }
    }

    /// 检查指针是否属于此分配器
    pub fn owns(&self, ptr: NonNull<u8>) -> bool {
        let addr = ptr.as_ptr() as usize;
        let start = self.memory.as_ptr() as usize;
        let end = start + self.capacity;
        addr >= start && addr < end
    }
}

/// 伙伴系统分配器 - 用于中等大小对象的动态分配
pub struct BuddyAllocator {
    /// 最小块大小（2的幂）
    min_block_size: usize,
    /// 总容量
    capacity: usize,
    /// 空闲块列表（每个级别一个列表）
    free_lists: Vec<Vec<NonNull<u8>>>,
    /// 分配映射表
    allocations: HashMap<NonNull<u8>, (usize, usize)>, // (size, order)
    /// 底层内存
    memory: NonNull<u8>,
}

impl BuddyAllocator {
    /// 创建新的伙伴系统分配器
    pub fn new(capacity: usize) -> Self {
        // 确保容量是2的幂
        let capacity = capacity.next_power_of_two();
        
        // 分配底层内存
        let layout = Layout::from_size_align(capacity, 8)
            .expect("Invalid layout");
        let memory = unsafe {
            NonNull::new(std::alloc::alloc(layout))
                .expect("Memory allocation failed")
        };

        // 确定最小块大小和级别数
        let min_block_size = 64; // 64字节
        let levels = (capacity / min_block_size).ilog2() as usize + 1;
        
        // 初始化空闲列表
        let mut free_lists = vec![Vec::new(); levels];
        free_lists[levels - 1].push(memory);

        Self {
            min_block_size,
            capacity,
            free_lists,
            allocations: HashMap::new(),
            memory,
        }
    }

    /// 分配内存
    pub fn allocate(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, MemoryError> {
        // 计算所需的块大小（向上取整到2的幂）
        let block_size = size.max(align).next_power_of_two();
        if block_size < self.min_block_size {
            return Err(MemoryError::InvalidSize);
        }

        // 计算对应的级别
        let requested_order = (block_size / self.min_block_size).ilog2() as usize;
        
        // 查找合适的块
        let mut order = requested_order;
        while order < self.free_lists.len() {
            if !self.free_lists[order].is_empty() {
                break;
            }
            order += 1;
        }

        if order >= self.free_lists.len() {
            return Err(MemoryError::OutOfMemory);
        }

        // 分裂大块直到得到合适大小
        let block = self.free_lists[order].pop().unwrap();
        while order > requested_order {
            order -= 1;
            let buddy = unsafe {
                NonNull::new_unchecked(
                    block.as_ptr().add(1 << (order + 6))
                )
            };
            self.free_lists[order].push(buddy);
        }

        // 记录分配信息
        self.allocations.insert(block, (block_size, order));

        Ok(block)
    }

    /// 释放内存
    pub fn deallocate(&mut self, ptr: NonNull<u8>, _layout: Layout) {
        if let Some((_size, mut order)) = self.allocations.remove(&ptr) {
            let mut block = ptr;

            // 尝试合并伙伴块
            while order < self.free_lists.len() - 1 {
                let buddy_offset = 1 << (order + 6);
                let buddy = unsafe {
                    NonNull::new_unchecked(
                        if (block.as_ptr() as usize) % (buddy_offset * 2) == 0 {
                            block.as_ptr().add(buddy_offset)
                        } else {
                            block.as_ptr().sub(buddy_offset)
                        }
                    )
                };

                // 如果找到空闲的伙伴，则合并
                if let Some(index) = self.free_lists[order].iter().position(|&b| b == buddy) {
                    self.free_lists[order].remove(index);
                    block = NonNull::new(block.as_ptr().min(buddy.as_ptr())).unwrap();
                    order += 1;
                } else {
                    break;
                }
            }

            // 将合并后的块添加到对应级别的空闲列表
            self.free_lists[order].push(block);
        }
    }

    /// 检查指针是否属于此分配器
    pub fn owns(&self, ptr: NonNull<u8>) -> bool {
        let addr = ptr.as_ptr() as usize;
        let start = self.memory.as_ptr() as usize;
        let end = start + self.capacity;
        addr >= start && addr < end
    }
}

impl Drop for SlabAllocator {
    fn drop(&mut self) {
        // 释放底层内存
        unsafe {
            let layout = Layout::from_size_align(self.capacity, 8)
                .expect("Invalid layout");
            std::alloc::dealloc(self.memory.as_ptr(), layout);
        }
    }
}

impl Drop for BuddyAllocator {
    fn drop(&mut self) {
        // 释放底层内存
        unsafe {
            let layout = Layout::from_size_align(self.capacity, 8)
                .expect("Invalid layout");
            std::alloc::dealloc(self.memory.as_ptr(), layout);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_slab_allocator() {
        let mut slab = SlabAllocator::new(64, 8192);
        
        // 测试基本分配和释放
        let layout = Layout::from_size_align(8, 8).unwrap();
        let ptr = slab.allocate(8, 8).unwrap();
        assert!(!ptr.as_ptr().is_null());
        slab.deallocate(ptr, layout);
        
        // 测试多个分配
        let ptrs: Vec<_> = (0..10)
            .map(|_| slab.allocate(8, 8).unwrap())
            .collect();
        for ptr in ptrs {
            slab.deallocate(ptr, layout);
        }
    }

    #[test]
    fn test_buddy_allocator() {
        let mut buddy = BuddyAllocator::new(4096);
        
        // 测试基本分配和释放
        let layout = Layout::from_size_align(64, 8).unwrap();
        let ptr = buddy.allocate(64, 8).unwrap();
        assert!(!ptr.as_ptr().is_null());
        buddy.deallocate(ptr, layout);
        
        // 测试不同大小的分配
        let layout_large = Layout::from_size_align(256, 8).unwrap();
        let ptr_large = buddy.allocate(256, 8).unwrap();
        buddy.deallocate(ptr_large, layout_large);
    }
}
