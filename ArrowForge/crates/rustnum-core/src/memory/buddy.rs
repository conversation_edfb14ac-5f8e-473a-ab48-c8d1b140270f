use std::ptr::NonNull;
use crate::error::RustNumError;

/// Buddy 分配器，用于中等大小的内存块
pub(crate) struct BuddyAllocator {
    order: usize,  // 2^order 字节
    free_lists: Vec<Vec<NonNull<u8>>>,
}

impl BuddyAllocator {
    pub fn new(order: usize) -> Self {
        Self {
            order,
            free_lists: vec![Vec::new(); order + 1],
        }
    }
    
    pub fn allocate(&self) -> Result<NonNull<u8>, RustNumError> {
        let size = 1 << (self.order + 12);  // 从4KB开始
        let layout = std::alloc::Layout::array::<u8>(size)
            .map_err(|_| RustNumError::AllocationError("Invalid buddy block size".into()))?;
            
        if let Some(ptr) = self.free_lists[self.order].pop() {
            Ok(ptr)
        } else {
            let ptr = unsafe {
                std::alloc::alloc(layout)
            };
            
            NonNull::new(ptr)
                .ok_or_else(|| RustNumError::AllocationError("Buddy allocation failed".into()))
        }
    }
    
    pub unsafe fn deallocate(&self, ptr: NonNull<u8>) {
        let mut current_order = self.order;
        let mut current_ptr = ptr;
        
        while current_order < self.free_lists.len() - 1 {
            // 计算伙伴块的地址
            let buddy_ptr = self.find_buddy(current_ptr, current_order);
            
            // 检查伙伴块是否在空闲列表中
            if let Some(idx) = self.free_lists[current_order]
                .iter()
                .position(|&p| p == buddy_ptr)
            {
                // 找到伙伴块，可以合并
                self.free_lists[current_order].remove(idx);
                
                // 合并后的块使用较低的地址
                current_ptr = std::cmp::min(current_ptr, buddy_ptr);
                current_order += 1;
            } else {
                // 没有找到伙伴块，将当前块添加到空闲列表
                self.free_lists[current_order].push(current_ptr);
                return;
            }
        }
        
        // 如果达到最大order，直接添加到对应的空闲列表
        self.free_lists[current_order].push(current_ptr);
    }
    
    /// 查找给定内存块的伙伴块
    fn find_buddy(&self, ptr: NonNull<u8>, order: usize) -> NonNull<u8> {
        let addr = ptr.as_ptr() as usize;
        let block_size = 1 << (order + 12);  // 从4KB开始
        let buddy_addr = addr ^ block_size;   // 异或操作得到伙伴块地址
        
        unsafe { NonNull::new_unchecked(buddy_addr as *mut u8) }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_buddy_allocator() {
        let buddy = BuddyAllocator::new(0);  // 4KB blocks
        
        // 分配一些内存块
        let ptrs: Vec<_> = (0..10)
            .map(|_| buddy.allocate().unwrap())
            .collect();
            
        // 释放内存块
        for ptr in ptrs {
            unsafe {
                buddy.deallocate(ptr);
            }
        }
        
        // 验证空闲列表
        assert_eq!(buddy.free_lists[0].len(), 10);
    }
}
