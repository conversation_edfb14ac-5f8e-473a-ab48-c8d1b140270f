use std::alloc::{Layout, handle_alloc_error};
use std::ptr::NonNull;
use std::sync::Arc;
use parking_lot::RwLock;
use crate::memory::allocator::{SlabAllocator, BuddyAllocator};
use crate::error::RustNumError;

/// 内存块大小分类
const SMALL_BLOCK_SIZE: usize = 4096;      // 4KB
const MEDIUM_BLOCK_SIZE: usize = 1048576;  // 1MB

/// 内存池实现
pub struct MemoryPool {
    // 小对象池（≤4KB）使用slab分配算法
    small_pools: Vec<SlabAllocator>,
    // 中等对象池（4KB~1MB）使用buddy系统
    medium_pools: Vec<BuddyAllocator>,
    // 统计信息
    stats: Arc<RwLock<PoolStats>>,
}

/// 内存池统计信息
#[derive(Default)]
struct PoolStats {
    total_allocated: usize,
    total_deallocated: usize,
    peak_memory: usize,
    allocation_count: usize,
}

impl MemoryPool {
    /// 创建新的内存池
    pub fn new() -> Self {
        let mut small_pools = Vec::new();
        let mut medium_pools = Vec::new();
        
        // 初始化小对象池
        for size in [16, 32, 64, 128, 256, 512, 1024, 2048, 4096] {
            small_pools.push(SlabAllocator::new(size, SMALL_BLOCK_SIZE * 4)); // 增加容量
        }
        
        // 初始化中等对象池
        for i in 0..10 {  // 4KB到1MB
            medium_pools.push(BuddyAllocator::new(SMALL_BLOCK_SIZE << i));
        }
        
        Self {
            small_pools,
            medium_pools,
            stats: Arc::new(RwLock::new(PoolStats::default())),
        }
    }
    
    /// 分配内存
    pub fn allocate(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, RustNumError> {
        let _layout = Layout::from_size_align(size, align)
            .map_err(|_| RustNumError::AllocationError("Invalid size or alignment".into()))?;
            
        let ptr = if size == 0 {
            return Err(RustNumError::AllocationError("Cannot allocate zero bytes".into()));
        } else if size <= SMALL_BLOCK_SIZE {
            self.allocate_small(size, align)?
        } else if size <= MEDIUM_BLOCK_SIZE {
            self.allocate_medium(size, align)?
        } else {
            self.allocate_large(size, align)?
        };
        
        // 更新统计信息
        let mut stats = self.stats.write();
        stats.total_allocated += size;
        stats.allocation_count += 1;
        stats.peak_memory = stats.peak_memory.max(stats.total_allocated - stats.total_deallocated);
        
        Ok(ptr)
    }
    
    /// 释放内存
    pub unsafe fn deallocate(&mut self, ptr: NonNull<u8>, size: usize, align: usize) {
        let layout = Layout::from_size_align_unchecked(size, align);
        
        if size <= SMALL_BLOCK_SIZE {
            self.deallocate_small(ptr, size, align);
        } else if size <= MEDIUM_BLOCK_SIZE {
            self.deallocate_medium(ptr, size, align);
        } else {
            self.deallocate_large(ptr, layout);
        }
        
        // 更新统计信息
        let mut stats = self.stats.write();
        stats.total_deallocated += size;
    }
    
    // 内部实现细节...
    fn allocate_small(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, RustNumError> {
        // 找到合适的小对象池
        let index = (size.next_power_of_two().trailing_zeros() as usize).saturating_sub(4);
        let index = index.min(self.small_pools.len() - 1); // 防止越界
        self.small_pools[index].allocate(size, align).map_err(|e| RustNumError::AllocationError(e.to_string()))
    }
    
    fn allocate_medium(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, RustNumError> {
        // 使用buddy系统分配
        let order = (size.next_power_of_two().trailing_zeros() as usize).saturating_sub(12);
        let order = order.min(self.medium_pools.len() - 1); // 防止越界
        self.medium_pools[order].allocate(size, align).map_err(|e| RustNumError::AllocationError(e.to_string()))
    }
    
    fn allocate_large(&mut self, size: usize, align: usize) -> Result<NonNull<u8>, RustNumError> {
        // 大对象直接使用系统分配器
        let layout = Layout::from_size_align(size, align)
            .map_err(|_| RustNumError::AllocationError("Invalid size or alignment".into()))?;
            
        let ptr = unsafe {
            std::alloc::alloc(layout)
        };
        
        NonNull::new(ptr)
            .ok_or_else(|| RustNumError::AllocationError("System allocation failed".into()))
    }
    
    unsafe fn deallocate_small(&mut self, ptr: NonNull<u8>, size: usize, align: usize) {
        let layout = Layout::from_size_align_unchecked(size, align);
        let index = (size.next_power_of_two().trailing_zeros() as usize).saturating_sub(4);
        let index = index.min(self.small_pools.len() - 1); // 防止越界
        self.small_pools[index].deallocate(ptr, layout);
    }
    
    unsafe fn deallocate_medium(&mut self, ptr: NonNull<u8>, size: usize, align: usize) {
        let layout = Layout::from_size_align_unchecked(size, align);
        let order = (size.next_power_of_two().trailing_zeros() as usize).saturating_sub(12);
        let order = order.min(self.medium_pools.len() - 1); // 防止越界
        self.medium_pools[order].deallocate(ptr, layout);
    }
    
    unsafe fn deallocate_large(&mut self, ptr: NonNull<u8>, layout: Layout) {
        std::alloc::dealloc(ptr.as_ptr(), layout);
    }
}

thread_local! {
    /// 线程局部内存池
    static THREAD_LOCAL_POOL: std::cell::RefCell<MemoryPool> = std::cell::RefCell::new(MemoryPool::new());
}

/// 全局内存分配器（注释掉，因为使用 mimalloc）
// #[global_allocator]
// static GLOBAL_ALLOCATOR: GlobalPool = GlobalPool::new();

pub struct GlobalPool;

impl GlobalPool {
    const fn new() -> Self {
        GlobalPool
    }
}

unsafe impl std::alloc::GlobalAlloc for GlobalPool {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        THREAD_LOCAL_POOL.with(|pool| {
            let mut pool = pool.borrow_mut();
            match pool.allocate(layout.size(), layout.align()) {
                Ok(ptr) => ptr.as_ptr(),
                Err(_) => handle_alloc_error(layout),
            }
        })
    }
    
    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        THREAD_LOCAL_POOL.with(|pool| {
            let mut pool = pool.borrow_mut();
            pool.deallocate(
                NonNull::new_unchecked(ptr),
                layout.size(),
                layout.align(),
            );
        });
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_memory_pool_allocation() {
        let mut pool = MemoryPool::new();
        
        // 测试小对象分配
        let ptr1 = pool.allocate(64, 8).unwrap();
        
        // 测试中等对象分配
        let ptr2 = pool.allocate(8192, 8).unwrap();
        
        // 测试大对象分配
        let ptr3 = pool.allocate(2_000_000, 8).unwrap();
        
        unsafe {
            pool.deallocate(ptr1, 64, 8);
            pool.deallocate(ptr2, 8192, 8);
            pool.deallocate(ptr3, 2_000_000, 8);
        }
    }
    
    #[test]
    fn test_memory_pool_stats() {
        let mut pool = MemoryPool::new();
        
        // 分配一些内存
        let ptrs: Vec<_> = (0..10)
            .map(|_| pool.allocate(1024, 8).unwrap())
            .collect();
            
        // 检查统计信息
        {
            let stats = pool.stats.read();
            assert!(stats.total_allocated >= 1024 * 10);
            assert_eq!(stats.allocation_count, 10);
        }
        
        // 释放内存
        for ptr in ptrs {
            unsafe {
                pool.deallocate(ptr, 1024, 8);
            }
        }
    }
}
