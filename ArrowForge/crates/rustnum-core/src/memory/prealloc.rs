use std::ptr::NonNull;
use std::alloc::Layout;
use crate::error::RustNumError;

/// 缓存行大小（字节）
const CACHE_LINE_SIZE: usize = 64;

/// 内存块预分配器
pub(crate) struct BlockPreallocator {
    // 预分配的内存块
    blocks: Vec<NonNull<u8>>,
    // 每个块的大小
    block_size: usize,
    // 当前已分配的块数
    allocated: usize,
}

impl BlockPreallocator {
    /// 创建新的预分配器
    pub fn new(block_size: usize, initial_blocks: usize) -> Result<Self, RustNumError> {
        let mut blocks = Vec::with_capacity(initial_blocks);
        
        // 预分配内存块
        for _ in 0..initial_blocks {
            let layout = Layout::from_size_align(block_size, CACHE_LINE_SIZE)
                .map_err(|_| RustNumError::AllocationError("Invalid block size or alignment".into()))?;
                
            let ptr = unsafe {
                std::alloc::alloc(layout)
            };
            
            let ptr = NonNull::new(ptr)
                .ok_or_else(|| RustNumError::AllocationError("Preallocation failed".into()))?;
                
            blocks.push(ptr);
        }
        
        Ok(Self {
            blocks,
            block_size,
            allocated: 0,
        })
    }
    
    /// 获取一个预分配的内存块
    pub fn get_block(&mut self) -> Result<NonNull<u8>, RustNumError> {
        if self.allocated >= self.blocks.len() {
            // 需要分配更多块
            let layout = Layout::from_size_align(self.block_size, CACHE_LINE_SIZE)
                .map_err(|_| RustNumError::AllocationError("Invalid block size or alignment".into()))?;
                
            let ptr = unsafe {
                std::alloc::alloc(layout)
            };
            
            let ptr = NonNull::new(ptr)
                .ok_or_else(|| RustNumError::AllocationError("Block allocation failed".into()))?;
                
            self.blocks.push(ptr);
        }
        
        let ptr = self.blocks[self.allocated];
        self.allocated += 1;
        Ok(ptr)
    }
    
    /// 释放所有预分配的内存块
    pub unsafe fn free_all(&mut self) {
        for ptr in self.blocks.drain(..) {
            let layout = Layout::from_size_align_unchecked(self.block_size, CACHE_LINE_SIZE);
            std::alloc::dealloc(ptr.as_ptr(), layout);
        }
        self.allocated = 0;
    }
}

impl Drop for BlockPreallocator {
    fn drop(&mut self) {
        unsafe {
            self.free_all();
        }
    }
}

/// 缓存对齐的内存分配工具
pub fn allocate_aligned(size: usize) -> Result<NonNull<u8>, RustNumError> {
    let layout = Layout::from_size_align(size, CACHE_LINE_SIZE)
        .map_err(|_| RustNumError::AllocationError("Invalid size for aligned allocation".into()))?;
        
    let ptr = unsafe {
        std::alloc::alloc(layout)
    };
    
    NonNull::new(ptr)
        .ok_or_else(|| RustNumError::AllocationError("Aligned allocation failed".into()))
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_block_preallocator() {
        let mut prealloc = BlockPreallocator::new(1024, 10).unwrap();
        
        // 获取所有预分配的块
        let mut blocks = Vec::new();
        for _ in 0..10 {
            blocks.push(prealloc.get_block().unwrap());
        }
        
        // 验证所有块都是缓存行对齐的
        for block in &blocks {
            assert_eq!(block.as_ptr() as usize % CACHE_LINE_SIZE, 0);
        }
        
        // 测试自动扩展
        let extra_block = prealloc.get_block().unwrap();
        assert_eq!(extra_block.as_ptr() as usize % CACHE_LINE_SIZE, 0);
    }
    
    #[test]
    fn test_aligned_allocation() {
        let ptr = allocate_aligned(1000).unwrap();
        assert_eq!(ptr.as_ptr() as usize % CACHE_LINE_SIZE, 0);
        
        unsafe {
            let layout = Layout::from_size_align_unchecked(1000, CACHE_LINE_SIZE);
            std::alloc::dealloc(ptr.as_ptr(), layout);
        }
    }
}
