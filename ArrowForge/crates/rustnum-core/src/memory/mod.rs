mod error;
mod pool;
mod stats;
pub mod allocator;

pub use pool::MemoryPool;
pub use allocator::SmartAllocator;
// pub use self::error::MemoryError; // 暂时未使用

/// 预分配策略
#[derive(Debug, <PERSON><PERSON>, Copy)]
pub enum PreallocStrategy {
    /// 不预分配
    None,
    /// 固定大小预分配
    Fixed(usize),
    /// 动态增长预分配
    Dynamic(usize, f32),
}

impl PartialEq for PreallocStrategy {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (Self::None, Self::None) => true,
            (Self::Fixed(a), Self::Fixed(b)) => a == b,
            (Self::Dynamic(a1, b1), Self::Dynamic(a2, b2)) => a1 == a2 && b1 == b2,
            _ => false,
        }
    }
}

impl Eq for PreallocStrategy {}

pub fn create_default_pool() -> MemoryPool {
    MemoryPool::new()
}

use std::ptr::NonNull;

/// 数据类型枚举
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum DType {
    F32,
    F64,
    I32,
    I64,
    U8,
}

/// 内存布局
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Layout {
    C,
    Fortran,
}

/// 零拷贝缓冲区
pub struct Buffer<T> {
    /// 原始指针
    ptr: NonNull<T>,
    /// 容量
    capacity: usize,
    /// 分配器
    allocator: SmartAllocator,
}

impl<T> Drop for Buffer<T> {
    fn drop(&mut self) {
        // Deallocate memory using the allocator
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::alloc::Layout;

    #[test]
    fn test_default_pool() {
        let mut pool = create_default_pool();
        let layout = Layout::from_size_align(1024, 8).unwrap();
        
        let ptr = pool.allocate(layout.size(), layout.align()).unwrap();
        assert!(!ptr.as_ptr().is_null());
        
        unsafe { pool.deallocate(ptr, layout.size(), layout.align()); }
    }

    #[test]
    fn test_custom_pool() {
        // let config = MemoryPoolConfig {
        //     small_threshold: 8 * 1024,  // 8KB
        //     medium_threshold: 2 * 1024 * 1024,  // 2MB
        //     small_pool_initial_size: 128 * 1024,  // 128KB
        //     medium_pool_initial_size: 8 * 1024 * 1024,  // 8MB
        //     enable_stats: true,
        // };
        
        // let pool = create_custom_pool(config);
        let _layout = Layout::from_size_align(16 * 1024, 8).unwrap();
        
        // let ptr = pool.allocate(layout).unwrap();
        // assert!(!ptr.as_ptr().is_null());
        
        // pool.deallocate(ptr, layout);
    }
}
