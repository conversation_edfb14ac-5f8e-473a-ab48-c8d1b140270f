use std::ptr::NonNull;
use crate::error::RustNumError;

/// Slab 分配器，用于固定大小的小内存块
pub(crate) struct SlabAllocator {
    block_size: usize,
    free_list: Vec<NonNull<u8>>,
}

impl SlabAllocator {
    pub fn new(block_size: usize) -> Self {
        Self {
            block_size,
            free_list: Vec::new(),
        }
    }
    
    pub fn allocate(&self) -> Result<NonNull<u8>, RustNumError> {
        if let Some(ptr) = self.free_list.pop() {
            Ok(ptr)
        } else {
            // 分配新的内存块
            let layout = std::alloc::Layout::array::<u8>(self.block_size)
                .map_err(|_| RustNumError::AllocationError("Invalid block size".into()))?;
                
            let ptr = unsafe {
                std::alloc::alloc(layout)
            };
            
            NonNull::new(ptr)
                .ok_or_else(|| RustNumError::AllocationError("Slab allocation failed".into()))
        }
    }
    
    pub unsafe fn deallocate(&self, ptr: NonNull<u8>) {
        // 将释放的内存块加入到空闲列表
        self.free_list.push(ptr);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_slab_allocator() {
        let slab = SlabAllocator::new(64);
        
        // 分配一些内存块
        let ptrs: Vec<_> = (0..100)
            .map(|_| slab.allocate().unwrap())
            .collect();
            
        // 释放内存块
        for ptr in ptrs {
            unsafe {
                slab.deallocate(ptr);
            }
        }
        
        // 验证空闲列表
        assert_eq!(slab.free_list.len(), 100);
    }
}
