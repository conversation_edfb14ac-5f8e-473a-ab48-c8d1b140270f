use std::fmt;
use std::error::Error;

/// 内存管理相关错误
#[derive(Debug)]
pub enum MemoryError {
    /// 内存分配失败
    AllocationFailed,
    /// 对齐要求无法满足
    AlignmentError,
    /// 内存不足
    OutOfMemory,
    /// 无效的大小
    InvalidSize,
    /// 无效的指针
    InvalidPointer,
    /// 重复释放
    DoubleFree,
}

impl fmt::Display for MemoryError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            MemoryError::AllocationFailed => 
                write!(f, "Memory allocation failed"),
            MemoryError::AlignmentError => 
                write!(f, "Cannot satisfy alignment requirements"),
            MemoryError::OutOfMemory => 
                write!(f, "Out of memory"),
            MemoryError::InvalidSize => 
                write!(f, "Invalid allocation size requested"),
            MemoryError::InvalidPointer => 
                write!(f, "Invalid pointer provided for deallocation"),
            MemoryError::DoubleFree => 
                write!(f, "Attempting to free already freed memory"),
        }
    }
}

impl Error for MemoryError {}
