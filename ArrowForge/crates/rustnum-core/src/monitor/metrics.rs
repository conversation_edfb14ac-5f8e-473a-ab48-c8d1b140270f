use std::sync::Arc;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use parking_lot::RwLock;
use serde::{Serialize, Deserialize};
use metrics::{Counter, Gauge, Histogram};

/// 性能指标类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetricType {
    /// 计数器（如操作次数）
    Counter(u64),
    /// 仪表（如内存使用）
    Gauge(f64),
    /// 直方图（如延迟分布）
    Histogram(Vec<f64>),
}

/// 性能指标标签
#[derive(Debug, Clone, Hash, Eq, PartialEq, Serialize, Deserialize)]
pub struct MetricLabels {
    /// 操作类型
    pub operation: String,
    /// 数据维度
    pub dimensions: Option<Vec<usize>>,
    /// 数据类型
    pub dtype: Option<String>,
    /// 执行设备
    pub device: Option<String>,
}

/// 性能监控中心
#[derive(Clone)]
pub struct PerformanceMonitor {
    /// 指标存储
    metrics: Arc<RwLock<HashMap<String, HashMap<MetricLabels, MetricType>>>>,
    /// 计时器存储
    timers: Arc<RwLock<HashMap<String, Instant>>>,
}

impl PerformanceMonitor {
    /// 创建新的性能监控实例
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(HashMap::new())),
            timers: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 记录计数器类型指标
    pub fn increment_counter(&self, name: &str, labels: MetricLabels, value: u64) {
        let mut metrics = self.metrics.write();
        let counters = metrics.entry(name.to_string())
                            .or_insert_with(HashMap::new);
        
        match counters.get_mut(&labels) {
            Some(MetricType::Counter(ref mut count)) => {
                *count += value;
            }
            _ => {
                counters.insert(labels, MetricType::Counter(value));
            }
        }
    }

    /// 记录仪表类型指标
    pub fn record_gauge(&self, name: &str, labels: MetricLabels, value: f64) {
        let mut metrics = self.metrics.write();
        let gauges = metrics.entry(name.to_string())
                          .or_insert_with(HashMap::new);
        gauges.insert(labels, MetricType::Gauge(value));
    }

    /// 记录直方图类型指标
    pub fn record_histogram(&self, name: &str, labels: MetricLabels, value: f64) {
        let mut metrics = self.metrics.write();
        let histograms = metrics.entry(name.to_string())
                              .or_insert_with(HashMap::new);
        
        match histograms.get_mut(&labels) {
            Some(MetricType::Histogram(ref mut values)) => {
                values.push(value);
            }
            _ => {
                histograms.insert(labels, MetricType::Histogram(vec![value]));
            }
        }
    }

    /// 开始计时
    pub fn start_timer(&self, operation: &str) {
        let mut timers = self.timers.write();
        timers.insert(operation.to_string(), Instant::now());
    }

    /// 停止计时并记录
    pub fn stop_timer(&self, operation: &str, labels: MetricLabels) {
        let mut timers = self.timers.write();
        if let Some(start_time) = timers.remove(operation) {
            let duration = start_time.elapsed().as_secs_f64();
            self.record_histogram("operation_duration", labels, duration);
        }
    }

    /// 生成性能报告
    pub fn generate_report(&self) -> PerformanceReport {
        let metrics = self.metrics.read();
        
        let mut report = PerformanceReport {
            timestamp: chrono::Utc::now(),
            metrics: HashMap::new(),
        };

        for (name, measurements) in metrics.iter() {
            let mut metric_summary = MetricSummary {
                counter_total: 0,
                gauge_average: 0.0,
                histogram_stats: HistogramStats::default(),
                by_label: HashMap::new(),
            };

            for (labels, metric) in measurements {
                match metric {
                    MetricType::Counter(value) => {
                        metric_summary.counter_total += value;
                    }
                    MetricType::Gauge(value) => {
                        metric_summary.gauge_average += value;
                    }
                    MetricType::Histogram(values) => {
                        metric_summary.histogram_stats.update(values);
                    }
                }

                metric_summary.by_label.insert(labels.clone(), metric.clone());
            }

            if !measurements.is_empty() {
                metric_summary.gauge_average /= measurements.len() as f64;
            }

            report.metrics.insert(name.clone(), metric_summary);
        }

        report
    }

    /// 导出Prometheus格式指标
    pub fn export_prometheus(&self) -> String {
        let metrics = self.metrics.read();
        let mut output = String::new();

        for (name, measurements) in metrics.iter() {
            for (labels, metric) in measurements {
                let label_str = format!(
                    "{{operation=\"{}\",device=\"{}\"}}",
                    labels.operation,
                    labels.device.as_deref().unwrap_or("cpu")
                );

                match metric {
                    MetricType::Counter(value) => {
                        output.push_str(&format!(
                            "{}_total{} {}\n",
                            name, label_str, value
                        ));
                    }
                    MetricType::Gauge(value) => {
                        output.push_str(&format!(
                            "{}{} {}\n",
                            name, label_str, value
                        ));
                    }
                    MetricType::Histogram(values) => {
                        let stats = HistogramStats::from_values(values);
                        output.push_str(&format!(
                            "{}_sum{} {}\n",
                            name, label_str, stats.sum
                        ));
                        output.push_str(&format!(
                            "{}_count{} {}\n",
                            name, label_str, stats.count
                        ));
                    }
                }
            }
        }

        output
    }
}

/// 性能报告
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceReport {
    /// 报告生成时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 指标汇总
    pub metrics: HashMap<String, MetricSummary>,
}

/// 指标汇总
#[derive(Debug, Serialize, Deserialize)]
pub struct MetricSummary {
    /// 计数器总和
    pub counter_total: u64,
    /// 仪表平均值
    pub gauge_average: f64,
    /// 直方图统计
    pub histogram_stats: HistogramStats,
    /// 按标签分组的原始数据
    pub by_label: HashMap<MetricLabels, MetricType>,
}

/// 直方图统计
#[derive(Debug, Default, Serialize, Deserialize)]
pub struct HistogramStats {
    /// 样本数量
    pub count: usize,
    /// 总和
    pub sum: f64,
    /// 最小值
    pub min: f64,
    /// 最大值
    pub max: f64,
    /// 平均值
    pub mean: f64,
    /// 中位数
    pub median: f64,
    /// 95分位数
    pub p95: f64,
    /// 99分位数
    pub p99: f64,
}

impl HistogramStats {
    /// 根据数值列表计算统计信息
    pub fn from_values(values: &[f64]) -> Self {
        if values.is_empty() {
            return Self::default();
        }

        let mut sorted = values.to_vec();
        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let count = values.len();
        let sum: f64 = values.iter().sum();
        let mean = sum / count as f64;
        let min = sorted[0];
        let max = sorted[count - 1];
        
        Self {
            count,
            sum,
            min,
            max,
            mean,
            median: Self::percentile(&sorted, 0.5),
            p95: Self::percentile(&sorted, 0.95),
            p99: Self::percentile(&sorted, 0.99),
        }
    }

    /// 更新统计信息
    pub fn update(&mut self, values: &[f64]) {
        *self = Self::from_values(values);
    }

    /// 计算百分位数
    fn percentile(sorted: &[f64], p: f64) -> f64 {
        let rank = p * (sorted.len() - 1) as f64;
        let i = rank.floor() as usize;
        let frac = rank - i as f64;

        if i + 1 < sorted.len() {
            sorted[i] * (1.0 - frac) + sorted[i + 1] * frac
        } else {
            sorted[i]
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_counter_metrics() {
        let monitor = PerformanceMonitor::new();
        
        let labels = MetricLabels {
            operation: "matrix_mul".to_string(),
            dimensions: Some(vec![100, 100]),
            dtype: Some("f64".to_string()),
            device: Some("cpu".to_string()),
        };

        monitor.increment_counter("operations", labels.clone(), 1);
        monitor.increment_counter("operations", labels.clone(), 2);

        let report = monitor.generate_report();
        assert_eq!(
            report.metrics["operations"].counter_total,
            3
        );
    }

    #[test]
    fn test_histogram_metrics() {
        let monitor = PerformanceMonitor::new();
        
        let labels = MetricLabels {
            operation: "matrix_mul".to_string(),
            dimensions: Some(vec![100, 100]),
            dtype: Some("f64".to_string()),
            device: Some("cpu".to_string()),
        };

        monitor.record_histogram("duration", labels.clone(), 1.0);
        monitor.record_histogram("duration", labels.clone(), 2.0);
        monitor.record_histogram("duration", labels.clone(), 3.0);

        let report = monitor.generate_report();
        let stats = &report.metrics["duration"].histogram_stats;
        
        assert_eq!(stats.count, 3);
        assert_eq!(stats.mean, 2.0);
        assert_eq!(stats.median, 2.0);
    }

    #[test]
    fn test_timing_operations() {
        let monitor = PerformanceMonitor::new();
        
        let labels = MetricLabels {
            operation: "test_op".to_string(),
            dimensions: None,
            dtype: None,
            device: Some("cpu".to_string()),
        };

        monitor.start_timer("test_op");
        std::thread::sleep(Duration::from_millis(100));
        monitor.stop_timer("test_op", labels);

        let report = monitor.generate_report();
        let stats = &report.metrics["operation_duration"].histogram_stats;
        
        assert!(stats.mean >= 0.1);
    }

    #[test]
    fn test_prometheus_export() {
        let monitor = PerformanceMonitor::new();
        
        let labels = MetricLabels {
            operation: "test_op".to_string(),
            dimensions: None,
            dtype: None,
            device: Some("cpu".to_string()),
        };

        monitor.increment_counter("operations", labels.clone(), 10);
        monitor.record_gauge("memory_usage", labels.clone(), 1024.0);

        let prometheus_output = monitor.export_prometheus();
        assert!(prometheus_output.contains("operations_total"));
        assert!(prometheus_output.contains("memory_usage"));
    }
}
