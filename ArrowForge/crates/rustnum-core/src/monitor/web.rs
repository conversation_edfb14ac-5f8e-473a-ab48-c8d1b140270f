use super::*;
use warp::{Filter, ws::WebSocket};
use tokio::sync::broadcast;
use serde_json::json;
use std::convert::Infallible;

/// 实时监控Web服务
pub struct RealTimeWebServer {
    /// 事件广播器
    event_sender: broadcast::Sender<RealTimeEvent>,
    /// 监控服务
    monitor: RealTimeMonitor,
    /// 服务器配置
    config: WebServerConfig,
}

/// Web服务器配置
#[derive(Debug, Clone)]
pub struct WebServerConfig {
    /// 监听地址
    pub host: String,
    /// 监听端口
    pub port: u16,
    /// 静态文件目录
    pub static_dir: String,
}

impl Default for WebServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 3000,
            static_dir: "./static".to_string(),
        }
    }
}

impl RealTimeWebServer {
    /// 创建新的Web服务器
    pub fn new(monitor: RealTimeMonitor) -> Self {
        let (event_sender, _) = broadcast::channel(100);
        
        Self {
            event_sender,
            monitor,
            config: WebServerConfig::default(),
        }
    }

    /// 启动Web服务器
    pub async fn start(&self) {
        // 配置WebSocket路由
        let ws_route = warp::path("ws")
            .and(warp::ws())
            .and(self.with_event_sender())
            .map(|ws: warp::ws::Ws, sender: broadcast::Sender<RealTimeEvent>| {
                ws.on_upgrade(move |socket| Self::handle_websocket(socket, sender))
            });

        // 配置静态文件路由
        let static_route = warp::path("static")
            .and(warp::fs::dir(&self.config.static_dir));

        // 配置主页路由
        let index_route = warp::path::end()
            .and(warp::fs::file(format!("{}/index.html", self.config.static_dir)));

        // 合并所有路由
        let routes = ws_route
            .or(static_route)
            .or(index_route)
            .with(warp::cors().allow_any_origin());

        // 启动服务器
        println!("Starting web server at http://{}:{}", self.config.host, self.config.port);
        warp::serve(routes).run((
            self.config.host.parse().unwrap(),
            self.config.port
        )).await;
    }

    /// 处理WebSocket连接
    async fn handle_websocket(ws: WebSocket, sender: broadcast::Sender<RealTimeEvent>) {
        let (mut ws_sender, _) = ws.split();
        let mut event_receiver = sender.subscribe();

        while let Ok(event) = event_receiver.recv().await {
            let message = match serde_json::to_string(&Self::event_to_json(&event)) {
                Ok(msg) => msg,
                Err(e) => {
                    eprintln!("Error serializing event: {}", e);
                    continue;
                }
            };

            if let Err(e) = ws_sender.send(message.into()).await {
                eprintln!("WebSocket send error: {}", e);
                break;
            }
        }
    }

    /// 将事件转换为JSON格式
    fn event_to_json(event: &RealTimeEvent) -> serde_json::Value {
        match event {
            RealTimeEvent::NewMetric { name, labels, value, timestamp } => {
                json!({
                    "type": "metric",
                    "name": name,
                    "labels": labels,
                    "value": value,
                    "timestamp": timestamp
                })
            }
            RealTimeEvent::Alert { level, message, timestamp } => {
                json!({
                    "type": "alert",
                    "level": format!("{:?}", level),
                    "message": message,
                    "timestamp": timestamp
                })
            }
            RealTimeEvent::SystemStatus { cpu_usage, memory_usage, gpu_usage, timestamp } => {
                json!({
                    "type": "status",
                    "cpu_usage": cpu_usage,
                    "memory_usage": memory_usage,
                    "gpu_usage": gpu_usage,
                    "timestamp": timestamp
                })
            }
        }
    }

    /// 辅助函数：提供事件发送器给路由
    fn with_event_sender(
        &self,
    ) -> impl Filter<Extract = (broadcast::Sender<RealTimeEvent>,), Error = Infallible> + Clone {
        let sender = self.event_sender.clone();
        warp::any().map(move || sender.clone())
    }
}

/// 生成前端静态文件
pub fn generate_frontend_files(static_dir: &str) -> std::io::Result<()> {
    std::fs::create_dir_all(static_dir)?;

    // 创建index.html
    let index_html = r#"<!DOCTYPE html>
<html>
<head>
    <title>RustNum实时监控</title>
    <link rel="stylesheet" href="static/style.css">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>RustNum实时性能监控</h1>
        
        <div class="grid">
            <div class="card">
                <h2>系统资源使用率</h2>
                <div id="system-usage"></div>
            </div>
            
            <div class="card">
                <h2>操作延迟</h2>
                <div id="operation-latency"></div>
            </div>
            
            <div class="card">
                <h2>内存使用情况</h2>
                <div id="memory-usage"></div>
            </div>
            
            <div class="card">
                <h2>警报日志</h2>
                <div id="alerts" class="alert-log"></div>
            </div>
        </div>
    </div>
    <script src="static/app.js"></script>
</body>
</html>"#;

    // 创建style.css
    let style_css = r#"
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card h2 {
    margin-top: 0;
    color: #333;
}

.alert-log {
    height: 300px;
    overflow-y: auto;
    font-family: monospace;
    background: #f8f8f8;
    padding: 10px;
    border-radius: 4px;
}

.alert {
    padding: 8px;
    margin: 4px 0;
    border-radius: 4px;
}

.alert.warning { background: #fff3cd; }
.alert.error { background: #f8d7da; }
.alert.info { background: #cce5ff; }
.alert.critical { background: #ffebee; }
"#;

    // 创建app.js
    let app_js = r#"
// WebSocket连接
const ws = new WebSocket(`ws://${window.location.host}/ws`);
const maxDataPoints = 50;

// 图表数据
let cpuData = [];
let memoryData = [];
let gpuData = [];
let timestamps = [];

// 初始化图表
const systemUsageChart = Plotly.newPlot('system-usage', [
    {
        name: 'CPU',
        y: cpuData,
        x: timestamps,
        type: 'scatter',
        mode: 'lines',
    },
    {
        name: 'Memory',
        y: memoryData,
        x: timestamps,
        type: 'scatter',
        mode: 'lines',
    },
    {
        name: 'GPU',
        y: gpuData,
        x: timestamps,
        type: 'scatter',
        mode: 'lines',
        visible: 'legendonly',
    }
], {
    title: '系统资源使用率',
    yaxis: { range: [0, 100], title: '使用率 (%)' },
    xaxis: { title: '时间' },
});

// 处理WebSocket消息
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    const timestamp = new Date(data.timestamp).toLocaleTimeString();

    switch(data.type) {
        case 'status':
            updateSystemUsage(data, timestamp);
            break;
        case 'alert':
            addAlert(data);
            break;
        case 'metric':
            updateMetrics(data);
            break;
    }
};

// 更新系统使用率图表
function updateSystemUsage(data, timestamp) {
    cpuData.push(data.cpu_usage);
    memoryData.push(data.memory_usage);
    if (data.gpu_usage !== null) {
        gpuData.push(data.gpu_usage);
    } else {
        gpuData.push(null);
    }
    timestamps.push(timestamp);

    // 限制数据点数量
    if (cpuData.length > maxDataPoints) {
        cpuData.shift();
        memoryData.shift();
        gpuData.shift();
        timestamps.shift();
    }

    Plotly.update('system-usage', {
        y: [cpuData, memoryData, gpuData],
        x: [timestamps, timestamps, timestamps]
    });
}

// 添加警报
function addAlert(data) {
    const alertsDiv = document.getElementById('alerts');
    const alertElement = document.createElement('div');
    alertElement.className = `alert ${data.level.toLowerCase()}`;
    alertElement.textContent = `[${new Date(data.timestamp).toLocaleTimeString()}] ${data.message}`;
    alertsDiv.insertBefore(alertElement, alertsDiv.firstChild);
}

// 更新指标
function updateMetrics(data) {
    // TODO: 实现指标更新逻辑
}
"#;

    // 写入文件
    std::fs::write(format!("{}/index.html", static_dir), index_html)?;
    std::fs::write(format!("{}/style.css", static_dir), style_css)?;
    std::fs::write(format!("{}/app.js", static_dir), app_js)?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_generate_frontend_files() {
        let temp_dir = tempdir().unwrap();
        let static_dir = temp_dir.path().to_str().unwrap();
        
        assert!(generate_frontend_files(static_dir).is_ok());
        
        // 验证文件生成
        assert!(std::path::Path::new(&format!("{}/index.html", static_dir)).exists());
        assert!(std::path::Path::new(&format!("{}/style.css", static_dir)).exists());
        assert!(std::path::Path::new(&format!("{}/app.js", static_dir)).exists());
    }

    #[tokio::test]
    async fn test_websocket_connection() {
        let monitor = RealTimeMonitor::new(PerformanceMonitor::new());
        let server = RealTimeWebServer::new(monitor);
        
        // 启动服务器
        tokio::spawn(async move {
            server.start().await;
        });
        
        // 等待服务器启动
        tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
        
        // 测试WebSocket连接
        let url = format!("ws://{}:{}/ws", "127.0.0.1", 3000);
        let (_, response) = tokio_tungstenite::connect_async(&url).await.unwrap();
        
        assert_eq!(response.status(), 101); // 101 Switching Protocols
    }
}
