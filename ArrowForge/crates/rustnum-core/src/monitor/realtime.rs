use super::*;
use std::sync::mpsc::{channel, Sender, Receiver};
use std::thread;
use std::time::Duration;
use tokio::time::interval;

/// 实时性能事件
#[derive(Debug, Clone)]
pub enum RealTimeEvent {
    /// 新的性能指标
    NewMetric {
        name: String,
        labels: MetricLabels,
        value: MetricType,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    /// 性能警报
    Alert {
        level: AlertLevel,
        message: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    /// 系统状态更新
    SystemStatus {
        cpu_usage: f64,
        memory_usage: f64,
        gpu_usage: Option<f64>,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}

/// 警报级别
#[derive(Debug, Clone, PartialEq)]
pub enum AlertLevel {
    Info,
    Warning,
    Error,
    Critical,
}

/// 实时监控服务
pub struct RealTimeMonitor {
    /// 事件发送器
    event_sender: Sender<RealTimeEvent>,
    /// 事件接收器
    event_receiver: Receiver<RealTimeEvent>,
    /// 性能监控器
    performance_monitor: PerformanceMonitor,
    /// 警报阈值配置
    alert_thresholds: AlertThresholds,
}

/// 警报阈值配置
#[derive(Debug, Clone)]
pub struct AlertThresholds {
    /// CPU使用率阈值
    pub cpu_usage_threshold: f64,
    /// 内存使用率阈值
    pub memory_usage_threshold: f64,
    /// 操作延迟阈值（毫秒）
    pub operation_latency_threshold: f64,
    /// GPU使用率阈值（如果可用）
    pub gpu_usage_threshold: Option<f64>,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            cpu_usage_threshold: 80.0,
            memory_usage_threshold: 85.0,
            operation_latency_threshold: 1000.0,
            gpu_usage_threshold: Some(90.0),
        }
    }
}

impl RealTimeMonitor {
    /// 创建新的实时监控服务
    pub fn new(performance_monitor: PerformanceMonitor) -> Self {
        let (event_sender, event_receiver) = channel();
        
        Self {
            event_sender,
            event_receiver,
            performance_monitor,
            alert_thresholds: AlertThresholds::default(),
        }
    }

    /// 启动监控服务
    pub async fn start(&self) {
        let sender = self.event_sender.clone();
        let thresholds = self.alert_thresholds.clone();
        
        // 创建系统监控任务
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(1));
            
            loop {
                interval.tick().await;
                
                // 收集系统状态
                let status = Self::collect_system_status();
                sender.send(RealTimeEvent::SystemStatus {
                    cpu_usage: status.cpu_usage,
                    memory_usage: status.memory_usage,
                    gpu_usage: status.gpu_usage,
                    timestamp: chrono::Utc::now(),
                }).unwrap_or_default();
                
                // 检查警报条件
                Self::check_alerts(&status, &thresholds, &sender);
            }
        });
    }

    /// 收集系统状态信息
    fn collect_system_status() -> SystemStatus {
        let cpu_usage = Self::get_cpu_usage();
        let memory_usage = Self::get_memory_usage();
        let gpu_usage = Self::get_gpu_usage();
        
        SystemStatus {
            cpu_usage,
            memory_usage,
            gpu_usage,
        }
    }

    /// 获取CPU使用率
    fn get_cpu_usage() -> f64 {
        // 使用sysinfo获取CPU信息
        use sysinfo::{System, SystemExt, CpuExt};
        let mut sys = System::new_all();
        sys.refresh_cpu();
        
        let cpu_usage = sys.cpus().iter()
            .map(|cpu| cpu.cpu_usage())
            .sum::<f32>() / sys.cpus().len() as f32;
            
        cpu_usage as f64
    }

    /// 获取内存使用率
    fn get_memory_usage() -> f64 {
        use sysinfo::{System, SystemExt};
        let mut sys = System::new_all();
        sys.refresh_memory();
        
        let total = sys.total_memory() as f64;
        let used = sys.used_memory() as f64;
        
        (used / total) * 100.0
    }

    /// 获取GPU使用率（如果可用）
    fn get_gpu_usage() -> Option<f64> {
        #[cfg(feature = "gpu")]
        {
            // 使用nvidia-smi或rocm-smi获取GPU信息
            // 这里需要根据实际使用的GPU类型实现
            None
        }
        #[cfg(not(feature = "gpu"))]
        None
    }

    /// 检查和发送警报
    fn check_alerts(
        status: &SystemStatus,
        thresholds: &AlertThresholds,
        sender: &Sender<RealTimeEvent>
    ) {
        // 检查CPU使用率
        if status.cpu_usage > thresholds.cpu_usage_threshold {
            sender.send(RealTimeEvent::Alert {
                level: AlertLevel::Warning,
                message: format!(
                    "CPU使用率过高: {:.1}% > {:.1}%",
                    status.cpu_usage,
                    thresholds.cpu_usage_threshold
                ),
                timestamp: chrono::Utc::now(),
            }).unwrap_or_default();
        }
        
        // 检查内存使用率
        if status.memory_usage > thresholds.memory_usage_threshold {
            sender.send(RealTimeEvent::Alert {
                level: AlertLevel::Warning,
                message: format!(
                    "内存使用率过高: {:.1}% > {:.1}%",
                    status.memory_usage,
                    thresholds.memory_usage_threshold
                ),
                timestamp: chrono::Utc::now(),
            }).unwrap_or_default();
        }
        
        // 检查GPU使用率
        if let (Some(gpu_usage), Some(threshold)) = (status.gpu_usage, thresholds.gpu_usage_threshold) {
            if gpu_usage > threshold {
                sender.send(RealTimeEvent::Alert {
                    level: AlertLevel::Warning,
                    message: format!(
                        "GPU使用率过高: {:.1}% > {:.1}%",
                        gpu_usage,
                        threshold
                    ),
                    timestamp: chrono::Utc::now(),
                }).unwrap_or_default();
            }
        }
    }

    /// 订阅事件流
    pub fn subscribe(&self) -> Receiver<RealTimeEvent> {
        let (sender, receiver) = channel();
        let event_receiver = self.event_receiver.clone();
        
        thread::spawn(move || {
            for event in event_receiver.iter() {
                if sender.send(event).is_err() {
                    break;
                }
            }
        });
        
        receiver
    }

    /// 设置警报阈值
    pub fn set_alert_thresholds(&mut self, thresholds: AlertThresholds) {
        self.alert_thresholds = thresholds;
    }
}

/// 系统状态信息
#[derive(Debug, Clone)]
struct SystemStatus {
    cpu_usage: f64,
    memory_usage: f64,
    gpu_usage: Option<f64>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_realtime_monitoring() {
        let performance_monitor = PerformanceMonitor::new();
        let monitor = RealTimeMonitor::new(performance_monitor);
        
        // 启动监控
        monitor.start().await;
        
        // 订阅事件
        let receiver = monitor.subscribe();
        
        // 等待并检查事件
        let event = tokio::time::timeout(
            Duration::from_secs(2),
            async {
                receiver.recv().unwrap()
            }
        ).await.unwrap();
        
        match event {
            RealTimeEvent::SystemStatus { cpu_usage, memory_usage, .. } => {
                assert!(cpu_usage >= 0.0 && cpu_usage <= 100.0);
                assert!(memory_usage >= 0.0 && memory_usage <= 100.0);
            }
            _ => panic!("Expected SystemStatus event"),
        }
    }

    #[test]
    fn test_alert_thresholds() {
        let performance_monitor = PerformanceMonitor::new();
        let mut monitor = RealTimeMonitor::new(performance_monitor);
        
        // 设置自定义阈值
        let thresholds = AlertThresholds {
            cpu_usage_threshold: 70.0,
            memory_usage_threshold: 75.0,
            operation_latency_threshold: 500.0,
            gpu_usage_threshold: Some(80.0),
        };
        
        monitor.set_alert_thresholds(thresholds.clone());
        assert_eq!(monitor.alert_thresholds.cpu_usage_threshold, 70.0);
    }

    #[test]
    fn test_system_metrics_collection() {
        // 测试CPU使用率收集
        let cpu_usage = RealTimeMonitor::get_cpu_usage();
        assert!(cpu_usage >= 0.0 && cpu_usage <= 100.0);
        
        // 测试内存使用率收集
        let memory_usage = RealTimeMonitor::get_memory_usage();
        assert!(memory_usage >= 0.0 && memory_usage <= 100.0);
    }
}
