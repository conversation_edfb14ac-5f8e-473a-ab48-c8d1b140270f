use super::*;
use std::path::Path;
use plotters::prelude::*;
use chrono::{DateTime, Utc};

/// 仪表板生成器
pub struct DashboardGenerator {
    /// 性能监控器
    monitor: PerformanceMonitor,
    /// 输出目录
    output_dir: String,
}

impl DashboardGenerator {
    /// 创建新的仪表板生成器
    pub fn new(monitor: PerformanceMonitor, output_dir: &str) -> Self {
        Self {
            monitor,
            output_dir: output_dir.to_string(),
        }
    }

    /// 生成完整的性能仪表板
    pub fn generate_dashboard(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 创建输出目录
        std::fs::create_dir_all(&self.output_dir)?;
        
        // 获取性能报告
        let report = self.monitor.generate_report();
        
        // 生成各种图表
        self.generate_operation_latency_chart(&report)?;
        self.generate_memory_usage_chart(&report)?;
        self.generate_operation_counts_chart(&report)?;
        self.generate_device_utilization_chart(&report)?;
        
        // 生成HTML报告
        self.generate_html_report(&report)?;

        Ok(())
    }

    /// 生成操作延迟图表
    fn generate_operation_latency_chart(&self, report: &PerformanceReport) -> Result<(), Box<dyn std::error::Error>> {
        let path = format!("{}/operation_latency.png", self.output_dir);
        let root = BitMapBackend::new(&path, (1024, 768)).into_drawing_area();
        root.fill(&WHITE)?;

        let mut chart = ChartBuilder::on(&root)
            .caption("Operation Latencies", ("sans-serif", 50).into_font())
            .margin(5)
            .x_label_area_size(40)
            .y_label_area_size(40)
            .build_cartesian_2d(
                0f64..100f64,
                0f64..report.metrics
                    .get("operation_duration")
                    .map(|m| m.histogram_stats.max)
                    .unwrap_or(1.0)
            )?;

        chart.configure_mesh().draw()?;

        // 为每个操作绘制延迟分布
        for (name, summary) in &report.metrics {
            if name.contains("duration") {
                let stats = &summary.histogram_stats;
                chart.draw_series(vec![
                    Circle::new((50.0, stats.median), 3, &RED),
                    Circle::new((95.0, stats.p95), 3, &BLUE),
                    Circle::new((99.0, stats.p99), 3, &GREEN),
                ])?;
            }
        }

        root.present()?;
        Ok(())
    }

    /// 生成内存使用图表
    fn generate_memory_usage_chart(&self, report: &PerformanceReport) -> Result<(), Box<dyn std::error::Error>> {
        let path = format!("{}/memory_usage.png", self.output_dir);
        let root = BitMapBackend::new(&path, (1024, 768)).into_drawing_area();
        root.fill(&WHITE)?;

        let memory_metrics: Vec<_> = report.metrics.iter()
            .filter(|(name, _)| name.contains("memory"))
            .collect();

        if !memory_metrics.is_empty() {
            let max_memory = memory_metrics.iter()
                .map(|(_, summary)| summary.gauge_average)
                .max_by(|a, b| a.partial_cmp(b).unwrap())
                .unwrap_or(1.0);

            let mut chart = ChartBuilder::on(&root)
                .caption("Memory Usage", ("sans-serif", 50).into_font())
                .margin(5)
                .x_label_area_size(40)
                .y_label_area_size(40)
                .build_cartesian_2d(
                    0..memory_metrics.len(),
                    0f64..max_memory
                )?;

            chart.configure_mesh().draw()?;

            chart.draw_series(
                memory_metrics.iter().enumerate().map(|(i, (name, summary))| {
                    Rectangle::new(
                        [(i, 0.0), (i + 1, summary.gauge_average)],
                        &BLUE.mix(0.3)
                    )
                })
            )?;
        }

        root.present()?;
        Ok(())
    }

    /// 生成操作计数图表
    fn generate_operation_counts_chart(&self, report: &PerformanceReport) -> Result<(), Box<dyn std::error::Error>> {
        let path = format!("{}/operation_counts.png", self.output_dir);
        let root = BitMapBackend::new(&path, (1024, 768)).into_drawing_area();
        root.fill(&WHITE)?;

        let operation_metrics: Vec<_> = report.metrics.iter()
            .filter(|(_, summary)| summary.counter_total > 0)
            .collect();

        if !operation_metrics.is_empty() {
            let max_count = operation_metrics.iter()
                .map(|(_, summary)| summary.counter_total)
                .max()
                .unwrap_or(1);

            let mut chart = ChartBuilder::on(&root)
                .caption("Operation Counts", ("sans-serif", 50).into_font())
                .margin(5)
                .x_label_area_size(40)
                .y_label_area_size(40)
                .build_cartesian_2d(
                    0..operation_metrics.len(),
                    0..max_count
                )?;

            chart.configure_mesh().draw()?;

            chart.draw_series(
                operation_metrics.iter().enumerate().map(|(i, (name, summary))| {
                    Rectangle::new(
                        [(i, 0), (i + 1, summary.counter_total as i32)],
                        &GREEN.mix(0.3)
                    )
                })
            )?;
        }

        root.present()?;
        Ok(())
    }

    /// 生成设备利用率图表
    fn generate_device_utilization_chart(&self, report: &PerformanceReport) -> Result<(), Box<dyn std::error::Error>> {
        let path = format!("{}/device_utilization.png", self.output_dir);
        let root = BitMapBackend::new(&path, (1024, 768)).into_drawing_area();
        root.fill(&WHITE)?;

        // 按设备分组统计操作数
        let mut device_stats: HashMap<String, u64> = HashMap::new();
        for (_, summary) in &report.metrics {
            for (labels, _) in &summary.by_label {
                if let Some(device) = &labels.device {
                    *device_stats.entry(device.clone()).or_default() += 1;
                }
            }
        }

        if !device_stats.is_empty() {
            let max_ops = device_stats.values().max().unwrap_or(&1);

            let mut chart = ChartBuilder::on(&root)
                .caption("Device Utilization", ("sans-serif", 50).into_font())
                .margin(5)
                .x_label_area_size(40)
                .y_label_area_size(40)
                .build_cartesian_2d(
                    0..device_stats.len(),
                    0..*max_ops
                )?;

            chart.configure_mesh().draw()?;

            chart.draw_series(
                device_stats.iter().enumerate().map(|(i, (device, count))| {
                    Rectangle::new(
                        [(i, 0), (i + 1, *count as i32)],
                        &BLUE.mix(0.3)
                    )
                })
            )?;
        }

        root.present()?;
        Ok(())
    }

    /// 生成HTML报告
    fn generate_html_report(&self, report: &PerformanceReport) -> Result<(), Box<dyn std::error::Error>> {
        let path = format!("{}/report.html", self.output_dir);
        let mut html = String::from(r#"
<!DOCTYPE html>
<html>
<head>
    <title>RustNum Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart { margin: 20px 0; }
        .stats { margin: 20px 0; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
    </style>
</head>
<body>
    <h1>RustNum Performance Report</h1>
    <p>Generated at: "#);
        
        html.push_str(&report.timestamp.to_rfc3339());
        html.push_str("</p>");

        // 添加图表
        html.push_str(r#"
    <div class="charts">
        <div class="chart">
            <h2>Operation Latencies</h2>
            <img src="operation_latency.png" alt="Operation Latencies">
        </div>
        <div class="chart">
            <h2>Memory Usage</h2>
            <img src="memory_usage.png" alt="Memory Usage">
        </div>
        <div class="chart">
            <h2>Operation Counts</h2>
            <img src="operation_counts.png" alt="Operation Counts">
        </div>
        <div class="chart">
            <h2>Device Utilization</h2>
            <img src="device_utilization.png" alt="Device Utilization">
        </div>
    </div>
    "#);

        // 添加详细统计
        html.push_str(r#"<div class="stats"><h2>Detailed Statistics</h2><table>"#);
        html.push_str("<tr><th>Metric</th><th>Count</th><th>Mean</th><th>P95</th><th>P99</th></tr>");

        for (name, summary) in &report.metrics {
            html.push_str(&format!(
                "<tr><td>{}</td><td>{}</td><td>{:.2}</td><td>{:.2}</td><td>{:.2}</td></tr>",
                name,
                summary.histogram_stats.count,
                summary.histogram_stats.mean,
                summary.histogram_stats.p95,
                summary.histogram_stats.p99
            ));
        }

        html.push_str("</table></div></body></html>");

        std::fs::write(path, html)?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_dashboard_generation() {
        let monitor = PerformanceMonitor::new();
        
        // 添加一些测试数据
        let labels = MetricLabels {
            operation: "test_op".to_string(),
            dimensions: Some(vec![100, 100]),
            dtype: Some("f64".to_string()),
            device: Some("cpu".to_string()),
        };

        monitor.record_histogram("operation_duration", labels.clone(), 1.0);
        monitor.record_histogram("operation_duration", labels.clone(), 2.0);
        monitor.increment_counter("operations", labels.clone(), 10);
        monitor.record_gauge("memory_usage", labels, 1024.0);

        // 创建临时目录
        let temp_dir = tempdir().unwrap();
        let dashboard = DashboardGenerator::new(
            monitor,
            temp_dir.path().to_str().unwrap()
        );

        // 生成仪表板
        assert!(dashboard.generate_dashboard().is_ok());

        // 验证文件生成
        assert!(temp_dir.path().join("report.html").exists());
        assert!(temp_dir.path().join("operation_latency.png").exists());
        assert!(temp_dir.path().join("memory_usage.png").exists());
        assert!(temp_dir.path().join("operation_counts.png").exists());
        assert!(temp_dir.path().join("device_utilization.png").exists());
    }
}
