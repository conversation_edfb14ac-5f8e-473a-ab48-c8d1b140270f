use super::*;
use chrono::{DateTime, Utc, Duration};
use serde::{Serialize, Deserialize};
use sqlx::{Pool, Sqlite, sqlite::SqlitePoolOptions};
use std::path::Path;
use futures::StreamExt;

/// 历史数据管理器
pub struct HistoricalDataManager {
    /// 数据库连接池
    pool: Pool<Sqlite>,
    /// 数据保留策略
    retention_policy: RetentionPolicy,
}

/// 数据保留策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetentionPolicy {
    /// 原始数据保留时间（天）
    raw_data_retention_days: i64,
    /// 聚合数据保留时间（天）
    aggregated_data_retention_days: i64,
    /// 数据采样间隔（秒）
    sampling_interval_seconds: i64,
}

impl Default for RetentionPolicy {
    fn default() -> Self {
        Self {
            raw_data_retention_days: 7,
            aggregated_data_retention_days: 90,
            sampling_interval_seconds: 60,
        }
    }
}

/// 趋势分析结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrendAnalysis {
    /// 指标名称
    metric_name: String,
    /// 分析开始时间
    start_time: DateTime<Utc>,
    /// 分析结束时间
    end_time: DateTime<Utc>,
    /// 趋势类型
    trend_type: TrendType,
    /// 变化率
    change_rate: f64,
    /// 预测值
    prediction: Vec<DataPoint>,
    /// 异常点
    anomalies: Vec<DataPoint>,
}

/// 趋势类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendType {
    Increasing,
    Decreasing,
    Stable,
    Fluctuating,
}

/// 数据点
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataPoint {
    timestamp: DateTime<Utc>,
    value: f64,
}

impl HistoricalDataManager {
    /// 创建新的历史数据管理器
    pub async fn new(db_path: &Path) -> Result<Self, sqlx::Error> {
        // 创建数据库连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(5)
            .connect_with(
                sqlx::sqlite::SqliteConnectOptions::new()
                    .filename(db_path)
                    .create_if_missing(true)
            )
            .await?;

        // 初始化数据库表
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                value REAL NOT NULL,
                labels TEXT
            );
            CREATE INDEX IF NOT EXISTS idx_metrics_time 
            ON metrics(metric_name, timestamp);
            
            CREATE TABLE IF NOT EXISTS aggregated_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                avg_value REAL NOT NULL,
                min_value REAL NOT NULL,
                max_value REAL NOT NULL,
                count INTEGER NOT NULL,
                interval_minutes INTEGER NOT NULL
            );
            CREATE INDEX IF NOT EXISTS idx_agg_metrics_time 
            ON aggregated_metrics(metric_name, timestamp, interval_minutes);
            "#,
        )
        .execute(&pool)
        .await?;

        Ok(Self {
            pool,
            retention_policy: RetentionPolicy::default(),
        })
    }

    /// 存储指标数据
    pub async fn store_metric(
        &self,
        metric_name: &str,
        value: f64,
        timestamp: DateTime<Utc>,
        labels: Option<&str>,
    ) -> Result<(), sqlx::Error> {
        sqlx::query(
            r#"
            INSERT INTO metrics (metric_name, timestamp, value, labels)
            VALUES (?, ?, ?, ?)
            "#,
        )
        .bind(metric_name)
        .bind(timestamp)
        .bind(value)
        .bind(labels)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// 聚合历史数据
    pub async fn aggregate_data(&self) -> Result<(), sqlx::Error> {
        // 聚合间隔（分钟）
        let intervals = [5, 15, 60, 1440]; // 5分钟, 15分钟, 1小时, 1天

        for interval in intervals {
            sqlx::query(
                r#"
                INSERT INTO aggregated_metrics 
                (metric_name, timestamp, avg_value, min_value, max_value, count, interval_minutes)
                SELECT 
                    metric_name,
                    datetime((strftime('%s', timestamp) / (? * 60)) * (? * 60), 'unixepoch') as interval_start,
                    avg(value) as avg_value,
                    min(value) as min_value,
                    max(value) as max_value,
                    count(*) as count,
                    ? as interval_minutes
                FROM metrics
                WHERE timestamp >= datetime('now', '-1 day')
                GROUP BY metric_name, interval_start
                "#,
            )
            .bind(interval)
            .bind(interval)
            .bind(interval)
            .execute(&self.pool)
            .await?;
        }

        Ok(())
    }

    /// 执行趋势分析
    pub async fn analyze_trend(
        &self,
        metric_name: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<TrendAnalysis, sqlx::Error> {
        // 获取数据点
        let data_points = self.get_metric_data(metric_name, start_time, end_time).await?;
        
        // 计算趋势
        let (trend_type, change_rate) = self.calculate_trend(&data_points);
        
        // 生成预测
        let prediction = self.predict_future_values(&data_points);
        
        // 检测异常点
        let anomalies = self.detect_anomalies(&data_points);

        Ok(TrendAnalysis {
            metric_name: metric_name.to_string(),
            start_time,
            end_time,
            trend_type,
            change_rate,
            prediction,
            anomalies,
        })
    }

    /// 获取指标数据
    async fn get_metric_data(
        &self,
        metric_name: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<Vec<DataPoint>, sqlx::Error> {
        let rows = sqlx::query!(
            r#"
            SELECT timestamp, value
            FROM metrics
            WHERE metric_name = ? AND timestamp BETWEEN ? AND ?
            ORDER BY timestamp ASC
            "#,
            metric_name,
            start_time,
            end_time
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(rows
            .into_iter()
            .map(|row| DataPoint {
                timestamp: row.timestamp.parse().unwrap(),
                value: row.value,
            })
            .collect())
    }

    /// 计算趋势
    fn calculate_trend(&self, data_points: &[DataPoint]) -> (TrendType, f64) {
        if data_points.is_empty() {
            return (TrendType::Stable, 0.0);
        }

        // 计算线性回归
        let n = data_points.len() as f64;
        let sum_x: f64 = data_points.iter()
            .map(|p| p.timestamp.timestamp() as f64)
            .sum();
        let sum_y: f64 = data_points.iter()
            .map(|p| p.value)
            .sum();
        let sum_xy: f64 = data_points.iter()
            .map(|p| (p.timestamp.timestamp() as f64) * p.value)
            .sum();
        let sum_xx: f64 = data_points.iter()
            .map(|p| (p.timestamp.timestamp() as f64).powi(2))
            .sum();

        let slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x);
        let change_rate = slope * 100.0; // 转换为百分比变化率

        // 判断趋势类型
        match change_rate.abs() {
            r if r < 1.0 => (TrendType::Stable, change_rate),
            r if r > 10.0 => {
                if change_rate > 0.0 {
                    (TrendType::Increasing, change_rate)
                } else {
                    (TrendType::Decreasing, change_rate)
                }
            }
            _ => (TrendType::Fluctuating, change_rate),
        }
    }

    /// 预测未来值
    fn predict_future_values(&self, data_points: &[DataPoint]) -> Vec<DataPoint> {
        if data_points.len() < 2 {
            return Vec::new();
        }

        // 使用简单移动平均预测
        let window_size = 5;
        let last_timestamp = data_points.last().unwrap().timestamp;
        let mut predictions = Vec::new();

        for i in 1..=10 {
            let prediction_time = last_timestamp + Duration::hours(i);
            let recent_values: Vec<f64> = data_points
                .iter()
                .rev()
                .take(window_size)
                .map(|p| p.value)
                .collect();

            let predicted_value = recent_values.iter().sum::<f64>() / recent_values.len() as f64;
            
            predictions.push(DataPoint {
                timestamp: prediction_time,
                value: predicted_value,
            });
        }

        predictions
    }

    /// 检测异常点
    fn detect_anomalies(&self, data_points: &[DataPoint]) -> Vec<DataPoint> {
        if data_points.len() < 4 {
            return Vec::new();
        }

        let mut anomalies = Vec::new();
        let values: Vec<f64> = data_points.iter().map(|p| p.value).collect();

        // 计算均值和标准差
        let mean = values.iter().sum::<f64>() / values.len() as f64;
        let std_dev = (values.iter()
            .map(|x| (x - mean).powi(2))
            .sum::<f64>() / values.len() as f64)
            .sqrt();

        // 使用3倍标准差规则检测异常
        for point in data_points {
            if (point.value - mean).abs() > 3.0 * std_dev {
                anomalies.push(point.clone());
            }
        }

        anomalies
    }

    /// 清理过期数据
    pub async fn cleanup_old_data(&self) -> Result<(), sqlx::Error> {
        // 清理原始数据
        sqlx::query(
            r#"
            DELETE FROM metrics
            WHERE timestamp < datetime('now', ?)
            "#,
        )
        .bind(format!("-{} days", self.retention_policy.raw_data_retention_days))
        .execute(&self.pool)
        .await?;

        // 清理聚合数据
        sqlx::query(
            r#"
            DELETE FROM aggregated_metrics
            WHERE timestamp < datetime('now', ?)
            "#,
        )
        .bind(format!("-{} days", self.retention_policy.aggregated_data_retention_days))
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_historical_data_operations() {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let manager = HistoricalDataManager::new(&db_path).await.unwrap();
        
        // 存储测试数据
        let now = Utc::now();
        manager.store_metric(
            "test_metric",
            42.0,
            now,
            Some("test_label"),
        ).await.unwrap();
        
        // 分析趋势
        let trend = manager.analyze_trend(
            "test_metric",
            now - Duration::hours(1),
            now,
        ).await.unwrap();
        
        assert!(matches!(trend.trend_type, TrendType::Stable));
    }

    #[tokio::test]
    async fn test_data_aggregation() {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let manager = HistoricalDataManager::new(&db_path).await.unwrap();
        
        // 存储多个数据点
        let now = Utc::now();
        for i in 0..10 {
            manager.store_metric(
                "test_metric",
                i as f64,
                now + Duration::minutes(i),
                None,
            ).await.unwrap();
        }
        
        // 执行聚合
        manager.aggregate_data().await.unwrap();
        
        // 清理旧数据
        manager.cleanup_old_data().await.unwrap();
    }
}
