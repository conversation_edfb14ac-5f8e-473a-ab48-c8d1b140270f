use std::fmt;

#[derive(Debug)]
pub enum RustNumError {
    AllocationError(String),
    ShapeMismatch {
        expected: Vec<usize>,
        got: Vec<usize>,
    },
    DimensionMismatch(String),
    BlasError(String),
    LapackError(String),
    DecompositionError(String),
    SingularMatrix,
    ConvergenceError {
        iterations: usize,
    },
    IoError(String),
    ParseError(String),
    OverflowError(String),
    UnderflowError(String),
    DivisionByZero,
    InvalidParameter(String),
    ConversionError(String),
    FeatureNotEnabled(String),
    IndexError(String),
    TypeMismatch {
        expected: String,
        got: String,
    },
    // 新增错误类型以支持我们的实施
    ShapeError(String),
    IndexOutOfBounds {
        index: usize,
        len: usize,
    },
    ValueError(String),
    ComputationError(String),
    DimensionError(String),
    MemoryError(String),
    InvalidOperation(String),
    NotImplemented(String),
    ComputeError(String),
}

impl fmt::Display for RustNumError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            RustNumError::AllocationError(msg) => write!(f, "Memory allocation error: {}", msg),
            RustNumError::ShapeMismatch { expected, got } => {
                write!(f, "Shape mismatch: expected {:?}, got {:?}", expected, got)
            }
            RustNumError::DimensionMismatch(msg) => write!(f, "Dimension mismatch: {}", msg),
            RustNumError::BlasError(msg) => write!(f, "BLAS operation failed: {}", msg),
            RustNumError::LapackError(msg) => write!(f, "LAPACK operation failed: {}", msg),
            RustNumError::DecompositionError(msg) => write!(f, "Decomposition failed: {}", msg),
            RustNumError::SingularMatrix => write!(f, "Singular matrix detected"),
            RustNumError::ConvergenceError { iterations } => {
                write!(f, "Convergence failed after {} iterations", iterations)
            }
            RustNumError::IoError(msg) => write!(f, "IO error: {}", msg),
            RustNumError::ParseError(msg) => write!(f, "Parse error: {}", msg),
            RustNumError::OverflowError(msg) => write!(f, "Overflow error: {}", msg),
            RustNumError::UnderflowError(msg) => write!(f, "Underflow error: {}", msg),
            RustNumError::DivisionByZero => write!(f, "Division by zero"),
            RustNumError::InvalidParameter(msg) => write!(f, "Invalid parameter: {}", msg),
            RustNumError::ConversionError(msg) => write!(f, "Conversion error: {}", msg),
            RustNumError::FeatureNotEnabled(msg) => write!(f, "Feature not enabled: {}", msg),
            RustNumError::IndexError(msg) => write!(f, "Index error: {}", msg),
            RustNumError::TypeMismatch { expected, got } => {
                write!(f, "Type mismatch: expected {}, got {}", expected, got)
            }
            RustNumError::NotImplemented(msg) => write!(f, "Not implemented: {}", msg),
            RustNumError::ShapeError(msg) => write!(f, "Shape error: {}", msg),
            RustNumError::IndexOutOfBounds { index, len } => write!(f, "Index {} out of bounds for length {}", index, len),
            RustNumError::ValueError(msg) => write!(f, "Value error: {}", msg),
            RustNumError::ComputationError(msg) => write!(f, "Computation error: {}", msg),
            RustNumError::DimensionError(msg) => write!(f, "Dimension error: {}", msg),
            RustNumError::MemoryError(msg) => write!(f, "Memory error: {}", msg),
            RustNumError::InvalidOperation(msg) => write!(f, "Invalid operation: {}", msg),
            RustNumError::ComputeError(msg) => write!(f, "Compute error: {}", msg),
        }
    }
}

impl std::error::Error for RustNumError {}
