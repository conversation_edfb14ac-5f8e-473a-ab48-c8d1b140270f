//! 多语言绑定模块
//! 
//! 提供 Python、JavaScript、R 等语言的绑定支持

pub mod python;
pub mod javascript;
pub mod r_lang;
pub mod c_api;

pub use python::{PyRustArray, PyTensor, PyMLContext};
pub use javascript::{JsRustArray, WasmBindings};
pub use r_lang::{RRustArray, RBindings};
pub use c_api::{CRustArray, CBindings};

use crate::error::RustNumError;
use crate::array::RustArray;
use std::collections::HashMap;
use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_double, c_int, c_void};

/// 绑定配置
#[derive(Debug, Clone)]
pub struct BindingConfig {
    /// 启用的语言绑定
    pub enabled_languages: Vec<Language>,
    /// 内存管理策略
    pub memory_strategy: MemoryStrategy,
    /// 错误处理模式
    pub error_handling: ErrorHandling,
    /// 性能优化选项
    pub optimization: OptimizationLevel,
}

/// 支持的语言
#[derive(Debug, Clone, PartialEq)]
pub enum Language {
    Python,
    JavaScript,
    R,
    C,
    Java,
    CSharp,
    Go,
}

/// 内存管理策略
#[derive(Debug, Clone)]
pub enum MemoryStrategy {
    /// 零拷贝（推荐）
    ZeroCopy,
    /// 深拷贝
    DeepCopy,
    /// 引用计数
    RefCounted,
    /// 共享内存
    SharedMemory,
}

/// 错误处理模式
#[derive(Debug, Clone)]
pub enum ErrorHandling {
    /// 异常抛出
    Exception,
    /// 返回错误码
    ErrorCode,
    /// 可选值返回
    Optional,
    /// 回调函数
    Callback,
}

/// 优化级别
#[derive(Debug, Clone)]
pub enum OptimizationLevel {
    Debug,
    Release,
    Aggressive,
}

/// 绑定管理器
pub struct BindingManager {
    config: BindingConfig,
    registered_types: HashMap<String, TypeInfo>,
    active_contexts: HashMap<String, BindingContext>,
}

/// 类型信息
#[derive(Debug, Clone)]
pub struct TypeInfo {
    pub name: String,
    pub size: usize,
    pub alignment: usize,
    pub is_pod: bool,
    pub destructor: Option<fn(*mut c_void)>,
}

/// 绑定上下文
#[derive(Debug)]
pub struct BindingContext {
    pub language: Language,
    pub session_id: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub objects: HashMap<String, ObjectHandle>,
}

/// 对象句柄
#[derive(Debug)]
pub struct ObjectHandle {
    pub ptr: *mut c_void,
    pub type_name: String,
    pub ref_count: usize,
}

impl BindingManager {
    /// 创建新的绑定管理器
    pub fn new(config: BindingConfig) -> Self {
        let mut manager = Self {
            config,
            registered_types: HashMap::new(),
            active_contexts: HashMap::new(),
        };
        
        // 注册核心类型
        manager.register_core_types();
        
        manager
    }
    
    /// 注册核心类型
    fn register_core_types(&mut self) {
        // 注册 RustArray<f32>
        self.registered_types.insert(
            "RustArray<f32>".to_string(),
            TypeInfo {
                name: "RustArray<f32>".to_string(),
                size: std::mem::size_of::<RustArray<f32>>(),
                alignment: std::mem::align_of::<RustArray<f32>>(),
                is_pod: false,
                destructor: Some(Self::destroy_rust_array_f32),
            }
        );
        
        // 注册 RustArray<f64>
        self.registered_types.insert(
            "RustArray<f64>".to_string(),
            TypeInfo {
                name: "RustArray<f64>".to_string(),
                size: std::mem::size_of::<RustArray<f64>>(),
                alignment: std::mem::align_of::<RustArray<f64>>(),
                is_pod: false,
                destructor: Some(Self::destroy_rust_array_f64),
            }
        );
    }
    
    /// 销毁 RustArray<f32>
    extern "C" fn destroy_rust_array_f32(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f32>);
            }
        }
    }
    
    /// 销毁 RustArray<f64>
    extern "C" fn destroy_rust_array_f64(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f64>);
            }
        }
    }
    
    /// 创建绑定上下文
    pub fn create_context(&mut self, language: Language) -> Result<String, RustNumError> {
        let session_id = uuid::Uuid::new_v4().to_string();
        
        let context = BindingContext {
            language,
            session_id: session_id.clone(),
            created_at: chrono::Utc::now(),
            objects: HashMap::new(),
        };
        
        self.active_contexts.insert(session_id.clone(), context);
        
        Ok(session_id)
    }
    
    /// 销毁绑定上下文
    pub fn destroy_context(&mut self, session_id: &str) -> Result<(), RustNumError> {
        if let Some(mut context) = self.active_contexts.remove(session_id) {
            // 清理所有对象
            for (_, handle) in context.objects.drain() {
                self.destroy_object(handle)?;
            }
            Ok(())
        } else {
            Err(RustNumError::InvalidInput(format!("Context not found: {}", session_id)))
        }
    }
    
    /// 注册对象
    pub fn register_object(&mut self, session_id: &str, object_id: String, ptr: *mut c_void, type_name: String) -> Result<(), RustNumError> {
        if let Some(context) = self.active_contexts.get_mut(session_id) {
            let handle = ObjectHandle {
                ptr,
                type_name,
                ref_count: 1,
            };
            context.objects.insert(object_id, handle);
            Ok(())
        } else {
            Err(RustNumError::InvalidInput(format!("Context not found: {}", session_id)))
        }
    }
    
    /// 获取对象句柄
    pub fn get_object(&self, session_id: &str, object_id: &str) -> Result<&ObjectHandle, RustNumError> {
        if let Some(context) = self.active_contexts.get(session_id) {
            context.objects.get(object_id)
                .ok_or_else(|| RustNumError::InvalidInput(format!("Object not found: {}", object_id)))
        } else {
            Err(RustNumError::InvalidInput(format!("Context not found: {}", session_id)))
        }
    }
    
    /// 销毁对象
    fn destroy_object(&self, handle: ObjectHandle) -> Result<(), RustNumError> {
        if let Some(type_info) = self.registered_types.get(&handle.type_name) {
            if let Some(destructor) = type_info.destructor {
                destructor(handle.ptr);
            }
        }
        Ok(())
    }
    
    /// 获取统计信息
    pub fn get_stats(&self) -> BindingStats {
        let mut language_counts = HashMap::new();
        let mut total_objects = 0;
        
        for context in self.active_contexts.values() {
            *language_counts.entry(context.language.clone()).or_insert(0) += 1;
            total_objects += context.objects.len();
        }
        
        BindingStats {
            active_contexts: self.active_contexts.len(),
            total_objects,
            language_counts,
            registered_types: self.registered_types.len(),
        }
    }
}

/// 绑定统计信息
#[derive(Debug)]
pub struct BindingStats {
    pub active_contexts: usize,
    pub total_objects: usize,
    pub language_counts: HashMap<Language, usize>,
    pub registered_types: usize,
}

/// 通用绑定特征
pub trait LanguageBinding {
    type NativeType;
    type ForeignType;
    
    /// 从 Rust 类型转换为外部类型
    fn to_foreign(&self, native: Self::NativeType) -> Result<Self::ForeignType, RustNumError>;
    
    /// 从外部类型转换为 Rust 类型
    fn from_foreign(&self, foreign: Self::ForeignType) -> Result<Self::NativeType, RustNumError>;
    
    /// 获取类型信息
    fn type_info(&self) -> TypeInfo;
    
    /// 清理资源
    fn cleanup(&self, foreign: Self::ForeignType) -> Result<(), RustNumError>;
}

/// 数组绑定特征
pub trait ArrayBinding<T> {
    /// 创建数组
    fn create_array(&self, data: Vec<T>, shape: Vec<usize>) -> Result<*mut c_void, RustNumError>;
    
    /// 获取数组数据
    fn get_array_data(&self, ptr: *mut c_void) -> Result<(*const T, usize), RustNumError>;
    
    /// 获取数组形状
    fn get_array_shape(&self, ptr: *mut c_void) -> Result<Vec<usize>, RustNumError>;
    
    /// 销毁数组
    fn destroy_array(&self, ptr: *mut c_void) -> Result<(), RustNumError>;
}

/// 绑定工厂
pub struct BindingFactory;

impl BindingFactory {
    /// 创建 Python 绑定
    pub fn create_python_binding() -> Result<Box<dyn LanguageBinding<NativeType = RustArray<f64>, ForeignType = *mut c_void>>, RustNumError> {
        Ok(Box::new(python::PyArrayBinding::new()))
    }
    
    /// 创建 JavaScript 绑定
    pub fn create_javascript_binding() -> Result<Box<dyn LanguageBinding<NativeType = RustArray<f64>, ForeignType = *mut c_void>>, RustNumError> {
        Ok(Box::new(javascript::JsArrayBinding::new()))
    }
    
    /// 创建 R 绑定
    pub fn create_r_binding() -> Result<Box<dyn LanguageBinding<NativeType = RustArray<f64>, ForeignType = *mut c_void>>, RustNumError> {
        Ok(Box::new(r_lang::RArrayBinding::new()))
    }
    
    /// 创建 C API 绑定
    pub fn create_c_binding() -> Result<Box<dyn LanguageBinding<NativeType = RustArray<f64>, ForeignType = *mut c_void>>, RustNumError> {
        Ok(Box::new(c_api::CArrayBinding::new()))
    }
}

impl Default for BindingConfig {
    fn default() -> Self {
        Self {
            enabled_languages: vec![Language::Python, Language::JavaScript, Language::C],
            memory_strategy: MemoryStrategy::ZeroCopy,
            error_handling: ErrorHandling::Exception,
            optimization: OptimizationLevel::Release,
        }
    }
}

/// 绑定工具函数
pub mod utils {
    use super::*;
    
    /// 安全地将 C 字符串转换为 Rust 字符串
    pub unsafe fn c_str_to_string(c_str: *const c_char) -> Result<String, RustNumError> {
        if c_str.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        CStr::from_ptr(c_str)
            .to_str()
            .map(|s| s.to_string())
            .map_err(|e| RustNumError::ConversionError(format!("UTF-8 conversion error: {}", e)))
    }
    
    /// 将 Rust 字符串转换为 C 字符串
    pub fn string_to_c_str(s: &str) -> Result<CString, RustNumError> {
        CString::new(s)
            .map_err(|e| RustNumError::ConversionError(format!("C string conversion error: {}", e)))
    }
    
    /// 检查指针有效性
    pub fn check_ptr_validity<T>(ptr: *const T) -> Result<(), RustNumError> {
        if ptr.is_null() {
            Err(RustNumError::InvalidInput("Null pointer".into()))
        } else {
            Ok(())
        }
    }
    
    /// 安全地解引用指针
    pub unsafe fn safe_deref<T>(ptr: *const T) -> Result<&'static T, RustNumError> {
        check_ptr_validity(ptr)?;
        Ok(&*ptr)
    }
    
    /// 安全地解引用可变指针
    pub unsafe fn safe_deref_mut<T>(ptr: *mut T) -> Result<&'static mut T, RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        Ok(&mut *ptr)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_binding_config_creation() {
        let config = BindingConfig::default();
        assert!(config.enabled_languages.contains(&Language::Python));
        assert!(config.enabled_languages.contains(&Language::JavaScript));
        assert!(config.enabled_languages.contains(&Language::C));
    }
    
    #[test]
    fn test_binding_manager_creation() {
        let config = BindingConfig::default();
        let manager = BindingManager::new(config);
        
        assert!(manager.registered_types.contains_key("RustArray<f32>"));
        assert!(manager.registered_types.contains_key("RustArray<f64>"));
    }
    
    #[test]
    fn test_context_creation() {
        let config = BindingConfig::default();
        let mut manager = BindingManager::new(config);
        
        let session_id = manager.create_context(Language::Python).unwrap();
        assert!(!session_id.is_empty());
        assert!(manager.active_contexts.contains_key(&session_id));
    }
    
    #[test]
    fn test_context_destruction() {
        let config = BindingConfig::default();
        let mut manager = BindingManager::new(config);
        
        let session_id = manager.create_context(Language::Python).unwrap();
        assert!(manager.active_contexts.contains_key(&session_id));
        
        manager.destroy_context(&session_id).unwrap();
        assert!(!manager.active_contexts.contains_key(&session_id));
    }
    
    #[test]
    fn test_binding_stats() {
        let config = BindingConfig::default();
        let mut manager = BindingManager::new(config);
        
        let _session1 = manager.create_context(Language::Python).unwrap();
        let _session2 = manager.create_context(Language::JavaScript).unwrap();
        
        let stats = manager.get_stats();
        assert_eq!(stats.active_contexts, 2);
        assert_eq!(stats.registered_types, 2);
    }
    
    #[test]
    fn test_utils_string_conversion() {
        let test_str = "Hello, World!";
        let c_string = utils::string_to_c_str(test_str).unwrap();
        
        unsafe {
            let converted_back = utils::c_str_to_string(c_string.as_ptr()).unwrap();
            assert_eq!(converted_back, test_str);
        }
    }
    
    #[test]
    fn test_utils_ptr_validity() {
        let valid_ptr = &42i32 as *const i32;
        let null_ptr = std::ptr::null::<i32>();
        
        assert!(utils::check_ptr_validity(valid_ptr).is_ok());
        assert!(utils::check_ptr_validity(null_ptr).is_err());
    }
}
