//! R 语言绑定实现
//! 
//! 提供 R 语言的 RustNum 绑定

use crate::error::RustNumError;
use crate::array::RustArray;
use super::{LanguageBinding, ArrayBinding, TypeInfo};
use std::os::raw::c_void;

/// R 数组绑定
pub struct RArrayBinding {
    type_info: TypeInfo,
}

impl RArrayBinding {
    pub fn new() -> Self {
        Self {
            type_info: TypeInfo {
                name: "RRustArray".to_string(),
                size: std::mem::size_of::<RustArray<f64>>(),
                alignment: std::mem::align_of::<RustArray<f64>>(),
                is_pod: false,
                destructor: Some(Self::destroy_r_array),
            },
        }
    }
    
    extern "C" fn destroy_r_array(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f64>);
            }
        }
    }
}

impl LanguageBinding for RArrayBinding {
    type NativeType = RustArray<f64>;
    type ForeignType = *mut c_void;
    
    fn to_foreign(&self, native: Self::NativeType) -> Result<Self::ForeignType, RustNumError> {
        let boxed = Box::new(native);
        Ok(Box::into_raw(boxed) as *mut c_void)
    }
    
    fn from_foreign(&self, foreign: Self::ForeignType) -> Result<Self::NativeType, RustNumError> {
        if foreign.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let boxed = Box::from_raw(foreign as *mut RustArray<f64>);
            Ok(*boxed)
        }
    }
    
    fn type_info(&self) -> TypeInfo {
        self.type_info.clone()
    }
    
    fn cleanup(&self, foreign: Self::ForeignType) -> Result<(), RustNumError> {
        if !foreign.is_null() {
            unsafe {
                let _ = Box::from_raw(foreign as *mut RustArray<f64>);
            }
        }
        Ok(())
    }
}

impl ArrayBinding<f64> for RArrayBinding {
    fn create_array(&self, data: Vec<f64>, shape: Vec<usize>) -> Result<*mut c_void, RustNumError> {
        let array = RustArray::from_vec(data, shape)?;
        self.to_foreign(array)
    }
    
    fn get_array_data(&self, ptr: *mut c_void) -> Result<(*const f64, usize), RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok((array.data().as_ptr(), array.data().len()))
        }
    }
    
    fn get_array_shape(&self, ptr: *mut c_void) -> Result<Vec<usize>, RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok(array.shape().to_vec())
        }
    }
    
    fn destroy_array(&self, ptr: *mut c_void) -> Result<(), RustNumError> {
        self.cleanup(ptr)
    }
}

/// R RustArray 包装器
#[repr(C)]
pub struct RRustArray {
    ptr: *mut c_void,
    binding: RArrayBinding,
}

impl RRustArray {
    /// 创建新的 R 数组
    pub fn new(data: Vec<f64>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        let binding = RArrayBinding::new();
        let ptr = binding.create_array(data, shape)?;
        
        Ok(Self { ptr, binding })
    }
}

/// R 绑定
pub struct RBindings;

impl RBindings {
    /// 创建数组
    pub fn create_array(data: &[f64], shape: &[usize]) -> Result<RRustArray, RustNumError> {
        RRustArray::new(data.to_vec(), shape.to_vec())
    }
}
