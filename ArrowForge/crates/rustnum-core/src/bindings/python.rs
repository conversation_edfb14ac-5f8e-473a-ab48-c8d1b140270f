//! Python 绑定实现
//! 
//! 提供 Python 语言的 RustNum 绑定

use crate::error::RustNumError;
use crate::array::RustArray;
use super::{LanguageBinding, ArrayBinding, TypeInfo};
use std::os::raw::{c_char, c_double, c_int, c_void};
use std::ffi::{CStr, CString};

/// Python 数组绑定
pub struct PyArrayBinding {
    type_info: TypeInfo,
}

impl PyArrayBinding {
    pub fn new() -> Self {
        Self {
            type_info: TypeInfo {
                name: "PyRustArray".to_string(),
                size: std::mem::size_of::<RustArray<f64>>(),
                alignment: std::mem::align_of::<RustArray<f64>>(),
                is_pod: false,
                destructor: Some(Self::destroy_py_array),
            },
        }
    }
    
    extern "C" fn destroy_py_array(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f64>);
            }
        }
    }
}

impl LanguageBinding for PyArrayBinding {
    type NativeType = RustArray<f64>;
    type ForeignType = *mut c_void;
    
    fn to_foreign(&self, native: Self::NativeType) -> Result<Self::ForeignType, RustNumError> {
        let boxed = Box::new(native);
        Ok(Box::into_raw(boxed) as *mut c_void)
    }
    
    fn from_foreign(&self, foreign: Self::ForeignType) -> Result<Self::NativeType, RustNumError> {
        if foreign.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let boxed = Box::from_raw(foreign as *mut RustArray<f64>);
            Ok(*boxed)
        }
    }
    
    fn type_info(&self) -> TypeInfo {
        self.type_info.clone()
    }
    
    fn cleanup(&self, foreign: Self::ForeignType) -> Result<(), RustNumError> {
        if !foreign.is_null() {
            unsafe {
                let _ = Box::from_raw(foreign as *mut RustArray<f64>);
            }
        }
        Ok(())
    }
}

impl ArrayBinding<f64> for PyArrayBinding {
    fn create_array(&self, data: Vec<f64>, shape: Vec<usize>) -> Result<*mut c_void, RustNumError> {
        let array = RustArray::from_vec(data, shape)?;
        self.to_foreign(array)
    }
    
    fn get_array_data(&self, ptr: *mut c_void) -> Result<(*const f64, usize), RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok((array.data().as_ptr(), array.data().len()))
        }
    }
    
    fn get_array_shape(&self, ptr: *mut c_void) -> Result<Vec<usize>, RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok(array.shape().to_vec())
        }
    }
    
    fn destroy_array(&self, ptr: *mut c_void) -> Result<(), RustNumError> {
        self.cleanup(ptr)
    }
}

/// Python RustArray 包装器
#[repr(C)]
pub struct PyRustArray {
    ptr: *mut c_void,
    binding: PyArrayBinding,
}

impl PyRustArray {
    /// 创建新的 Python 数组
    pub fn new(data: Vec<f64>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        let binding = PyArrayBinding::new();
        let ptr = binding.create_array(data, shape)?;
        
        Ok(Self { ptr, binding })
    }
    
    /// 获取数组形状
    pub fn shape(&self) -> Result<Vec<usize>, RustNumError> {
        self.binding.get_array_shape(self.ptr)
    }
    
    /// 获取数组数据
    pub fn data(&self) -> Result<Vec<f64>, RustNumError> {
        let (data_ptr, len) = self.binding.get_array_data(self.ptr)?;
        
        unsafe {
            let slice = std::slice::from_raw_parts(data_ptr, len);
            Ok(slice.to_vec())
        }
    }
    
    /// 获取数组长度
    pub fn len(&self) -> Result<usize, RustNumError> {
        let (_, len) = self.binding.get_array_data(self.ptr)?;
        Ok(len)
    }
    
    /// 数组加法
    pub fn add(&self, other: &PyRustArray) -> Result<PyRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.add(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(PyRustArray {
            ptr: result_ptr,
            binding: PyArrayBinding::new(),
        })
    }
    
    /// 数组乘法
    pub fn mul(&self, other: &PyRustArray) -> Result<PyRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.mul(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(PyRustArray {
            ptr: result_ptr,
            binding: PyArrayBinding::new(),
        })
    }
    
    /// 矩阵乘法
    pub fn matmul(&self, other: &PyRustArray) -> Result<PyRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.matmul(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(PyRustArray {
            ptr: result_ptr,
            binding: PyArrayBinding::new(),
        })
    }
    
    /// 求和
    pub fn sum(&self) -> Result<f64, RustNumError> {
        let array = self.binding.from_foreign(self.ptr)?;
        let sum_array = array.sum()?;
        
        if sum_array.data().is_empty() {
            Ok(0.0)
        } else {
            Ok(sum_array.data()[0])
        }
    }
    
    /// 平均值
    pub fn mean(&self) -> Result<f64, RustNumError> {
        let array = self.binding.from_foreign(self.ptr)?;
        let mean_array = array.mean()?;
        
        if mean_array.data().is_empty() {
            Ok(0.0)
        } else {
            Ok(mean_array.data()[0])
        }
    }
}

impl Drop for PyRustArray {
    fn drop(&mut self) {
        let _ = self.binding.cleanup(self.ptr);
    }
}

/// Python 张量包装器
#[repr(C)]
pub struct PyTensor {
    ptr: *mut c_void,
}

impl PyTensor {
    /// 创建新张量
    pub fn new(data: Vec<f32>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        // 这里应该创建实际的张量
        // 简化实现，使用空指针
        Ok(Self {
            ptr: std::ptr::null_mut(),
        })
    }
    
    /// 张量加法
    pub fn add(&self, _other: &PyTensor) -> Result<PyTensor, RustNumError> {
        // 简化实现
        Ok(PyTensor {
            ptr: std::ptr::null_mut(),
        })
    }
    
    /// 前向传播
    pub fn forward(&self) -> Result<PyTensor, RustNumError> {
        // 简化实现
        Ok(PyTensor {
            ptr: std::ptr::null_mut(),
        })
    }
    
    /// 反向传播
    pub fn backward(&self) -> Result<(), RustNumError> {
        // 简化实现
        Ok(())
    }
}

/// Python ML 上下文包装器
#[repr(C)]
pub struct PyMLContext {
    ptr: *mut c_void,
}

impl PyMLContext {
    /// 创建新的 ML 上下文
    pub fn new() -> Result<Self, RustNumError> {
        // 简化实现
        Ok(Self {
            ptr: std::ptr::null_mut(),
        })
    }
    
    /// 创建张量
    pub fn tensor(&self, data: Vec<f32>, shape: Vec<usize>) -> Result<PyTensor, RustNumError> {
        PyTensor::new(data, shape)
    }
    
    /// 训练模型
    pub fn train(&self, _model: &PyTensor, _data: &PyTensor) -> Result<(), RustNumError> {
        // 简化实现
        Ok(())
    }
    
    /// 预测
    pub fn predict(&self, _model: &PyTensor, _data: &PyTensor) -> Result<PyTensor, RustNumError> {
        // 简化实现
        PyTensor::new(vec![0.0], vec![1])
    }
}

// C API 导出函数
#[no_mangle]
pub extern "C" fn py_rustarray_new(
    data: *const c_double,
    data_len: c_int,
    shape: *const c_int,
    shape_len: c_int,
) -> *mut PyRustArray {
    if data.is_null() || shape.is_null() || data_len <= 0 || shape_len <= 0 {
        return std::ptr::null_mut();
    }
    
    unsafe {
        let data_slice = std::slice::from_raw_parts(data, data_len as usize);
        let shape_slice = std::slice::from_raw_parts(shape, shape_len as usize);
        
        let data_vec = data_slice.to_vec();
        let shape_vec = shape_slice.iter().map(|&x| x as usize).collect();
        
        match PyRustArray::new(data_vec, shape_vec) {
            Ok(array) => Box::into_raw(Box::new(array)),
            Err(_) => std::ptr::null_mut(),
        }
    }
}

#[no_mangle]
pub extern "C" fn py_rustarray_destroy(ptr: *mut PyRustArray) {
    if !ptr.is_null() {
        unsafe {
            let _ = Box::from_raw(ptr);
        }
    }
}

#[no_mangle]
pub extern "C" fn py_rustarray_shape(
    ptr: *const PyRustArray,
    shape_out: *mut c_int,
    shape_len: *mut c_int,
) -> c_int {
    if ptr.is_null() || shape_out.is_null() || shape_len.is_null() {
        return -1;
    }
    
    unsafe {
        let array = &*ptr;
        match array.shape() {
            Ok(shape) => {
                let len = shape.len().min(10); // 限制最大长度
                for (i, &dim) in shape.iter().take(len).enumerate() {
                    *shape_out.add(i) = dim as c_int;
                }
                *shape_len = len as c_int;
                0
            }
            Err(_) => -1,
        }
    }
}

#[no_mangle]
pub extern "C" fn py_rustarray_data(
    ptr: *const PyRustArray,
    data_out: *mut c_double,
    data_len: *mut c_int,
) -> c_int {
    if ptr.is_null() || data_out.is_null() || data_len.is_null() {
        return -1;
    }
    
    unsafe {
        let array = &*ptr;
        match array.data() {
            Ok(data) => {
                let len = data.len();
                for (i, &val) in data.iter().enumerate() {
                    *data_out.add(i) = val;
                }
                *data_len = len as c_int;
                0
            }
            Err(_) => -1,
        }
    }
}

#[no_mangle]
pub extern "C" fn py_rustarray_add(
    left: *const PyRustArray,
    right: *const PyRustArray,
) -> *mut PyRustArray {
    if left.is_null() || right.is_null() {
        return std::ptr::null_mut();
    }
    
    unsafe {
        let left_array = &*left;
        let right_array = &*right;
        
        match left_array.add(right_array) {
            Ok(result) => Box::into_raw(Box::new(result)),
            Err(_) => std::ptr::null_mut(),
        }
    }
}

#[no_mangle]
pub extern "C" fn py_rustarray_sum(ptr: *const PyRustArray) -> c_double {
    if ptr.is_null() {
        return 0.0;
    }
    
    unsafe {
        let array = &*ptr;
        array.sum().unwrap_or(0.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_py_array_binding() {
        let binding = PyArrayBinding::new();
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let ptr = binding.create_array(data.clone(), shape.clone()).unwrap();
        
        let retrieved_shape = binding.get_array_shape(ptr).unwrap();
        assert_eq!(retrieved_shape, shape);
        
        let (data_ptr, len) = binding.get_array_data(ptr).unwrap();
        assert_eq!(len, 4);
        
        unsafe {
            let retrieved_data = std::slice::from_raw_parts(data_ptr, len);
            assert_eq!(retrieved_data, &data);
        }
        
        binding.destroy_array(ptr).unwrap();
    }
    
    #[test]
    fn test_py_rust_array() {
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let array = PyRustArray::new(data.clone(), shape.clone()).unwrap();
        
        assert_eq!(array.shape().unwrap(), shape);
        assert_eq!(array.data().unwrap(), data);
        assert_eq!(array.len().unwrap(), 4);
        
        let sum = array.sum().unwrap();
        assert_eq!(sum, 10.0); // 1 + 2 + 3 + 4 = 10
        
        let mean = array.mean().unwrap();
        assert_eq!(mean, 2.5); // 10 / 4 = 2.5
    }
    
    #[test]
    fn test_py_rust_array_operations() {
        let a = PyRustArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
        let b = PyRustArray::new(vec![2.0, 3.0, 4.0, 5.0], vec![2, 2]).unwrap();
        
        let add_result = a.add(&b).unwrap();
        let add_data = add_result.data().unwrap();
        assert_eq!(add_data, vec![3.0, 5.0, 7.0, 9.0]);
        
        let mul_result = a.mul(&b).unwrap();
        let mul_data = mul_result.data().unwrap();
        assert_eq!(mul_data, vec![2.0, 6.0, 12.0, 20.0]);
    }
    
    #[test]
    fn test_c_api_functions() {
        let data = [1.0, 2.0, 3.0, 4.0];
        let shape = [2, 2];
        
        let ptr = py_rustarray_new(
            data.as_ptr(),
            data.len() as c_int,
            shape.as_ptr(),
            shape.len() as c_int,
        );
        
        assert!(!ptr.is_null());
        
        let sum = py_rustarray_sum(ptr);
        assert_eq!(sum, 10.0);
        
        py_rustarray_destroy(ptr);
    }
}
