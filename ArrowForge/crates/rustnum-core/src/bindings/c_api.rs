//! C API 绑定实现
//! 
//! 提供 C 语言的 RustNum 绑定

use crate::error::RustNumError;
use crate::array::RustArray;
use super::{LanguageBinding, ArrayBinding, TypeInfo};
use std::os::raw::c_void;

/// C 数组绑定
pub struct CArrayBinding {
    type_info: TypeInfo,
}

impl CArrayBinding {
    pub fn new() -> Self {
        Self {
            type_info: TypeInfo {
                name: "CRustArray".to_string(),
                size: std::mem::size_of::<RustArray<f64>>(),
                alignment: std::mem::align_of::<RustArray<f64>>(),
                is_pod: false,
                destructor: Some(Self::destroy_c_array),
            },
        }
    }
    
    extern "C" fn destroy_c_array(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f64>);
            }
        }
    }
}

impl LanguageBinding for CArrayBinding {
    type NativeType = RustArray<f64>;
    type ForeignType = *mut c_void;
    
    fn to_foreign(&self, native: Self::NativeType) -> Result<Self::ForeignType, RustNumError> {
        let boxed = Box::new(native);
        Ok(Box::into_raw(boxed) as *mut c_void)
    }
    
    fn from_foreign(&self, foreign: Self::ForeignType) -> Result<Self::NativeType, RustNumError> {
        if foreign.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let boxed = Box::from_raw(foreign as *mut RustArray<f64>);
            Ok(*boxed)
        }
    }
    
    fn type_info(&self) -> TypeInfo {
        self.type_info.clone()
    }
    
    fn cleanup(&self, foreign: Self::ForeignType) -> Result<(), RustNumError> {
        if !foreign.is_null() {
            unsafe {
                let _ = Box::from_raw(foreign as *mut RustArray<f64>);
            }
        }
        Ok(())
    }
}

impl ArrayBinding<f64> for CArrayBinding {
    fn create_array(&self, data: Vec<f64>, shape: Vec<usize>) -> Result<*mut c_void, RustNumError> {
        let array = RustArray::from_vec(data, shape)?;
        self.to_foreign(array)
    }
    
    fn get_array_data(&self, ptr: *mut c_void) -> Result<(*const f64, usize), RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok((array.data().as_ptr(), array.data().len()))
        }
    }
    
    fn get_array_shape(&self, ptr: *mut c_void) -> Result<Vec<usize>, RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok(array.shape().to_vec())
        }
    }
    
    fn destroy_array(&self, ptr: *mut c_void) -> Result<(), RustNumError> {
        self.cleanup(ptr)
    }
}

/// C RustArray 包装器
#[repr(C)]
pub struct CRustArray {
    ptr: *mut c_void,
    binding: CArrayBinding,
}

impl CRustArray {
    /// 创建新的 C 数组
    pub fn new(data: Vec<f64>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        let binding = CArrayBinding::new();
        let ptr = binding.create_array(data, shape)?;
        
        Ok(Self { ptr, binding })
    }
}

/// C 绑定
pub struct CBindings;

impl CBindings {
    /// 创建数组
    pub fn create_array(data: &[f64], shape: &[usize]) -> Result<CRustArray, RustNumError> {
        CRustArray::new(data.to_vec(), shape.to_vec())
    }
}
