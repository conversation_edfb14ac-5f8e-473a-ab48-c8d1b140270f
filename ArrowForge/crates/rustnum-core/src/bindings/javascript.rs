//! JavaScript/WebAssembly 绑定实现
//! 
//! 提供 JavaScript 和 WebAssembly 的 RustNum 绑定

use crate::error::RustNumError;
use crate::array::RustArray;
use super::{LanguageBinding, ArrayBinding, TypeInfo};
use std::os::raw::c_void;

/// JavaScript 数组绑定
pub struct JsArrayBinding {
    type_info: TypeInfo,
}

impl JsArrayBinding {
    pub fn new() -> Self {
        Self {
            type_info: TypeInfo {
                name: "JsRustArray".to_string(),
                size: std::mem::size_of::<RustArray<f64>>(),
                alignment: std::mem::align_of::<RustArray<f64>>(),
                is_pod: false,
                destructor: Some(Self::destroy_js_array),
            },
        }
    }
    
    extern "C" fn destroy_js_array(ptr: *mut c_void) {
        if !ptr.is_null() {
            unsafe {
                let _ = Box::from_raw(ptr as *mut RustArray<f64>);
            }
        }
    }
}

impl LanguageBinding for JsArrayBinding {
    type NativeType = RustArray<f64>;
    type ForeignType = *mut c_void;
    
    fn to_foreign(&self, native: Self::NativeType) -> Result<Self::ForeignType, RustNumError> {
        let boxed = Box::new(native);
        Ok(Box::into_raw(boxed) as *mut c_void)
    }
    
    fn from_foreign(&self, foreign: Self::ForeignType) -> Result<Self::NativeType, RustNumError> {
        if foreign.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let boxed = Box::from_raw(foreign as *mut RustArray<f64>);
            Ok(*boxed)
        }
    }
    
    fn type_info(&self) -> TypeInfo {
        self.type_info.clone()
    }
    
    fn cleanup(&self, foreign: Self::ForeignType) -> Result<(), RustNumError> {
        if !foreign.is_null() {
            unsafe {
                let _ = Box::from_raw(foreign as *mut RustArray<f64>);
            }
        }
        Ok(())
    }
}

impl ArrayBinding<f64> for JsArrayBinding {
    fn create_array(&self, data: Vec<f64>, shape: Vec<usize>) -> Result<*mut c_void, RustNumError> {
        let array = RustArray::from_vec(data, shape)?;
        self.to_foreign(array)
    }
    
    fn get_array_data(&self, ptr: *mut c_void) -> Result<(*const f64, usize), RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok((array.data().as_ptr(), array.data().len()))
        }
    }
    
    fn get_array_shape(&self, ptr: *mut c_void) -> Result<Vec<usize>, RustNumError> {
        if ptr.is_null() {
            return Err(RustNumError::InvalidInput("Null pointer".into()));
        }
        
        unsafe {
            let array = &*(ptr as *const RustArray<f64>);
            Ok(array.shape().to_vec())
        }
    }
    
    fn destroy_array(&self, ptr: *mut c_void) -> Result<(), RustNumError> {
        self.cleanup(ptr)
    }
}

/// JavaScript RustArray 包装器
#[repr(C)]
pub struct JsRustArray {
    ptr: *mut c_void,
    binding: JsArrayBinding,
}

impl JsRustArray {
    /// 创建新的 JavaScript 数组
    pub fn new(data: Vec<f64>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        let binding = JsArrayBinding::new();
        let ptr = binding.create_array(data, shape)?;
        
        Ok(Self { ptr, binding })
    }
    
    /// 从 JavaScript 数组创建
    pub fn from_js_array(js_data: &[f64], shape: Vec<usize>) -> Result<Self, RustNumError> {
        Self::new(js_data.to_vec(), shape)
    }
    
    /// 转换为 JavaScript 数组
    pub fn to_js_array(&self) -> Result<Vec<f64>, RustNumError> {
        let (data_ptr, len) = self.binding.get_array_data(self.ptr)?;
        
        unsafe {
            let slice = std::slice::from_raw_parts(data_ptr, len);
            Ok(slice.to_vec())
        }
    }
    
    /// 获取数组形状
    pub fn shape(&self) -> Result<Vec<usize>, RustNumError> {
        self.binding.get_array_shape(self.ptr)
    }
    
    /// 获取数组长度
    pub fn length(&self) -> Result<usize, RustNumError> {
        let (_, len) = self.binding.get_array_data(self.ptr)?;
        Ok(len)
    }
    
    /// 数组加法
    pub fn add(&self, other: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.add(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(JsRustArray {
            ptr: result_ptr,
            binding: JsArrayBinding::new(),
        })
    }
    
    /// 数组乘法
    pub fn multiply(&self, other: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.mul(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(JsRustArray {
            ptr: result_ptr,
            binding: JsArrayBinding::new(),
        })
    }
    
    /// 矩阵乘法
    pub fn matmul(&self, other: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        let self_array = self.binding.from_foreign(self.ptr)?;
        let other_array = other.binding.from_foreign(other.ptr)?;
        
        let result = self_array.matmul(&other_array)?;
        
        let result_ptr = self.binding.to_foreign(result)?;
        Ok(JsRustArray {
            ptr: result_ptr,
            binding: JsArrayBinding::new(),
        })
    }
    
    /// 求和
    pub fn sum(&self) -> Result<f64, RustNumError> {
        let array = self.binding.from_foreign(self.ptr)?;
        let sum_array = array.sum()?;
        
        if sum_array.data().is_empty() {
            Ok(0.0)
        } else {
            Ok(sum_array.data()[0])
        }
    }
    
    /// 平均值
    pub fn mean(&self) -> Result<f64, RustNumError> {
        let array = self.binding.from_foreign(self.ptr)?;
        let mean_array = array.mean()?;
        
        if mean_array.data().is_empty() {
            Ok(0.0)
        } else {
            Ok(mean_array.data()[0])
        }
    }
    
    /// 获取元素
    pub fn get(&self, index: usize) -> Result<f64, RustNumError> {
        let (data_ptr, len) = self.binding.get_array_data(self.ptr)?;
        
        if index >= len {
            return Err(RustNumError::IndexOutOfBounds { index, len });
        }
        
        unsafe {
            Ok(*data_ptr.add(index))
        }
    }
    
    /// 设置元素
    pub fn set(&mut self, index: usize, value: f64) -> Result<(), RustNumError> {
        let array = self.binding.from_foreign(self.ptr)?;
        
        if index >= array.data().len() {
            return Err(RustNumError::IndexOutOfBounds { 
                index, 
                len: array.data().len() 
            });
        }
        
        // 这里需要修改数组数据，但当前的设计不支持直接修改
        // 在实际实现中，需要重新设计以支持可变操作
        Err(RustNumError::UnsupportedOperation("Mutable operations not yet supported".into()))
    }
}

impl Drop for JsRustArray {
    fn drop(&mut self) {
        let _ = self.binding.cleanup(self.ptr);
    }
}

/// WebAssembly 绑定
pub struct WasmBindings;

impl WasmBindings {
    /// 创建数组（WASM 导出）
    pub fn create_array(data: &[f64], shape: &[usize]) -> Result<JsRustArray, RustNumError> {
        JsRustArray::new(data.to_vec(), shape.to_vec())
    }
    
    /// 数组加法（WASM 导出）
    pub fn array_add(a: &JsRustArray, b: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        a.add(b)
    }
    
    /// 数组乘法（WASM 导出）
    pub fn array_multiply(a: &JsRustArray, b: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        a.multiply(b)
    }
    
    /// 矩阵乘法（WASM 导出）
    pub fn matrix_multiply(a: &JsRustArray, b: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        a.matmul(b)
    }
    
    /// 数组求和（WASM 导出）
    pub fn array_sum(array: &JsRustArray) -> Result<f64, RustNumError> {
        array.sum()
    }
    
    /// 数组平均值（WASM 导出）
    pub fn array_mean(array: &JsRustArray) -> Result<f64, RustNumError> {
        array.mean()
    }
    
    /// 创建零数组
    pub fn zeros(shape: &[usize]) -> Result<JsRustArray, RustNumError> {
        let size: usize = shape.iter().product();
        let data = vec![0.0; size];
        JsRustArray::new(data, shape.to_vec())
    }
    
    /// 创建单位数组
    pub fn ones(shape: &[usize]) -> Result<JsRustArray, RustNumError> {
        let size: usize = shape.iter().product();
        let data = vec![1.0; size];
        JsRustArray::new(data, shape.to_vec())
    }
    
    /// 创建随机数组
    pub fn random(shape: &[usize]) -> Result<JsRustArray, RustNumError> {
        let size: usize = shape.iter().product();
        let data: Vec<f64> = (0..size).map(|i| (i as f64 * 0.01) % 1.0).collect();
        JsRustArray::new(data, shape.to_vec())
    }
    
    /// 数组重塑
    pub fn reshape(array: &JsRustArray, new_shape: &[usize]) -> Result<JsRustArray, RustNumError> {
        let data = array.to_js_array()?;
        let new_size: usize = new_shape.iter().product();
        
        if data.len() != new_size {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![data.len()],
                got: new_shape.to_vec(),
            });
        }
        
        JsRustArray::new(data, new_shape.to_vec())
    }
    
    /// 数组转置
    pub fn transpose(array: &JsRustArray) -> Result<JsRustArray, RustNumError> {
        let rust_array = array.binding.from_foreign(array.ptr)?;
        let transposed = rust_array.transpose()?;
        
        let result_ptr = array.binding.to_foreign(transposed)?;
        Ok(JsRustArray {
            ptr: result_ptr,
            binding: JsArrayBinding::new(),
        })
    }
}

/// JavaScript 工具函数
pub mod js_utils {
    use super::*;
    
    /// 将 JavaScript 类型数组转换为 Rust Vec
    pub fn typed_array_to_vec(data: &[f64]) -> Vec<f64> {
        data.to_vec()
    }
    
    /// 将 Rust Vec 转换为 JavaScript 兼容格式
    pub fn vec_to_js_compatible(data: Vec<f64>) -> Vec<f64> {
        data
    }
    
    /// 验证形状有效性
    pub fn validate_shape(shape: &[usize]) -> Result<(), RustNumError> {
        if shape.is_empty() {
            return Err(RustNumError::InvalidInput("Shape cannot be empty".into()));
        }
        
        for &dim in shape {
            if dim == 0 {
                return Err(RustNumError::InvalidInput("Shape dimensions must be positive".into()));
            }
        }
        
        Ok(())
    }
    
    /// 计算数组大小
    pub fn calculate_size(shape: &[usize]) -> usize {
        shape.iter().product()
    }
    
    /// 检查形状兼容性
    pub fn shapes_compatible(shape1: &[usize], shape2: &[usize]) -> bool {
        if shape1.len() != shape2.len() {
            return false;
        }
        
        shape1.iter().zip(shape2.iter()).all(|(a, b)| a == b)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_js_array_binding() {
        let binding = JsArrayBinding::new();
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let ptr = binding.create_array(data.clone(), shape.clone()).unwrap();
        
        let retrieved_shape = binding.get_array_shape(ptr).unwrap();
        assert_eq!(retrieved_shape, shape);
        
        let (data_ptr, len) = binding.get_array_data(ptr).unwrap();
        assert_eq!(len, 4);
        
        unsafe {
            let retrieved_data = std::slice::from_raw_parts(data_ptr, len);
            assert_eq!(retrieved_data, &data);
        }
        
        binding.destroy_array(ptr).unwrap();
    }
    
    #[test]
    fn test_js_rust_array() {
        let data = vec![1.0, 2.0, 3.0, 4.0];
        let shape = vec![2, 2];
        
        let array = JsRustArray::new(data.clone(), shape.clone()).unwrap();
        
        assert_eq!(array.shape().unwrap(), shape);
        assert_eq!(array.to_js_array().unwrap(), data);
        assert_eq!(array.length().unwrap(), 4);
        
        let sum = array.sum().unwrap();
        assert_eq!(sum, 10.0);
        
        let mean = array.mean().unwrap();
        assert_eq!(mean, 2.5);
    }
    
    #[test]
    fn test_js_rust_array_operations() {
        let a = JsRustArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
        let b = JsRustArray::new(vec![2.0, 3.0, 4.0, 5.0], vec![2, 2]).unwrap();
        
        let add_result = a.add(&b).unwrap();
        let add_data = add_result.to_js_array().unwrap();
        assert_eq!(add_data, vec![3.0, 5.0, 7.0, 9.0]);
        
        let mul_result = a.multiply(&b).unwrap();
        let mul_data = mul_result.to_js_array().unwrap();
        assert_eq!(mul_data, vec![2.0, 6.0, 12.0, 20.0]);
    }
    
    #[test]
    fn test_wasm_bindings() {
        let data = [1.0, 2.0, 3.0, 4.0];
        let shape = [2, 2];
        
        let array = WasmBindings::create_array(&data, &shape).unwrap();
        assert_eq!(array.shape().unwrap(), shape);
        
        let sum = WasmBindings::array_sum(&array).unwrap();
        assert_eq!(sum, 10.0);
        
        let zeros = WasmBindings::zeros(&[3, 3]).unwrap();
        assert_eq!(zeros.length().unwrap(), 9);
        assert_eq!(zeros.sum().unwrap(), 0.0);
        
        let ones = WasmBindings::ones(&[2, 3]).unwrap();
        assert_eq!(ones.length().unwrap(), 6);
        assert_eq!(ones.sum().unwrap(), 6.0);
    }
    
    #[test]
    fn test_js_utils() {
        let data = [1.0, 2.0, 3.0, 4.0];
        let vec_data = js_utils::typed_array_to_vec(&data);
        assert_eq!(vec_data, vec![1.0, 2.0, 3.0, 4.0]);
        
        let shape = [2, 2];
        assert!(js_utils::validate_shape(&shape).is_ok());
        
        let invalid_shape = [2, 0];
        assert!(js_utils::validate_shape(&invalid_shape).is_err());
        
        assert_eq!(js_utils::calculate_size(&[2, 3, 4]), 24);
        
        assert!(js_utils::shapes_compatible(&[2, 3], &[2, 3]));
        assert!(!js_utils::shapes_compatible(&[2, 3], &[3, 2]));
    }
    
    #[test]
    fn test_array_element_access() {
        let array = JsRustArray::new(vec![1.0, 2.0, 3.0, 4.0], vec![2, 2]).unwrap();
        
        assert_eq!(array.get(0).unwrap(), 1.0);
        assert_eq!(array.get(1).unwrap(), 2.0);
        assert_eq!(array.get(3).unwrap(), 4.0);
        
        assert!(array.get(4).is_err()); // 越界访问
    }
}
