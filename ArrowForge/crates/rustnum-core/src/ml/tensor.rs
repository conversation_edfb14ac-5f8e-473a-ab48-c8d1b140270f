//! 张量实现
//! 
//! 提供深度学习框架兼容的张量数据结构

use crate::error::RustNumError;
use crate::array::RustArray;
use super::autograd::{Variable, AutogradEngine};
use std::sync::Arc;

/// 设备类型
#[derive(Debug, Clone, PartialEq)]
pub enum Device {
    CPU,
    CUDA(usize), // GPU 设备 ID
    Metal,       // Apple Metal
}

impl Default for Device {
    fn default() -> Self {
        Device::CPU
    }
}

/// 数据类型
#[derive(Debug, Clone, PartialEq)]
pub enum DataType {
    Float32,
    Float64,
    Int32,
    Int64,
    Bool,
}

/// 张量
#[derive(Debug, Clone)]
pub struct Tensor<T> {
    data: RustArray<T>,
    device: Device,
    dtype: DataType,
    requires_grad: bool,
    grad: Option<Box<Tensor<T>>>,
    autograd_meta: Option<AutogradMeta>,
}

/// 自动微分元数据
#[derive(Debug, <PERSON>lone)]
struct AutogradMeta {
    variable_id: usize,
    engine: Arc<AutogradEngine>,
}

impl<T> Tensor<T> 
where 
    T: Copy + Default + Send + Sync + 'static
{
    /// 创建新张量
    pub fn new(data: Vec<T>, shape: Vec<usize>, device: Device) -> Result<Self, RustNumError> {
        let array = RustArray::from_vec(data, shape)?;
        Ok(Self {
            data: array,
            device,
            dtype: Self::infer_dtype(),
            requires_grad: false,
            grad: None,
            autograd_meta: None,
        })
    }
    
    /// 从 RustArray 创建张量
    pub fn from_array(array: &RustArray<T>, device: Device) -> Result<Self, RustNumError> {
        Ok(Self {
            data: array.clone(),
            device,
            dtype: Self::infer_dtype(),
            requires_grad: false,
            grad: None,
            autograd_meta: None,
        })
    }
    
    /// 创建零张量
    pub fn zeros(shape: &[usize]) -> Result<Self, RustNumError> {
        let array = RustArray::zeros(shape)?;
        Self::from_array(&array, Device::CPU)
    }
    
    /// 创建单位张量
    pub fn ones(shape: &[usize]) -> Result<Self, RustNumError> {
        let array = RustArray::ones(shape)?;
        Self::from_array(&array, Device::CPU)
    }
    
    /// 创建与另一个张量形状相同的零张量
    pub fn zeros_like(other: &Self) -> Result<Self, RustNumError> {
        Self::zeros(other.shape())
    }
    
    /// 创建与另一个张量形状相同的单位张量
    pub fn ones_like(other: &Self) -> Result<Self, RustNumError> {
        Self::ones(other.shape())
    }
    
    /// 推断数据类型
    fn infer_dtype() -> DataType {
        if std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() {
            DataType::Float32
        } else if std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>() {
            DataType::Float64
        } else if std::any::TypeId::of::<T>() == std::any::TypeId::of::<i32>() {
            DataType::Int32
        } else if std::any::TypeId::of::<T>() == std::any::TypeId::of::<i64>() {
            DataType::Int64
        } else {
            DataType::Float32 // 默认
        }
    }
    
    /// 获取形状
    pub fn shape(&self) -> &[usize] {
        self.data.shape()
    }
    
    /// 获取设备
    pub fn device(&self) -> &Device {
        &self.device
    }
    
    /// 获取数据类型
    pub fn dtype(&self) -> &DataType {
        &self.dtype
    }
    
    /// 获取元素总数
    pub fn numel(&self) -> usize {
        self.data.len()
    }
    
    /// 获取数据
    pub fn data(&self) -> &[T] {
        self.data.data()
    }
    
    /// 设置是否需要梯度
    pub fn requires_grad_(mut self, requires_grad: bool) -> Self {
        self.requires_grad = requires_grad;
        self
    }
    
    /// 检查是否需要梯度
    pub fn requires_grad(&self) -> bool {
        self.requires_grad
    }
    
    /// 获取梯度
    pub fn grad(&self) -> Option<&Tensor<T>> {
        self.grad.as_ref().map(|g| g.as_ref())
    }
    
    /// 清零梯度
    pub fn zero_grad(&mut self) -> Result<(), RustNumError> {
        self.grad = None;
        Ok(())
    }
    
    /// 移动到设备
    pub fn to_device(mut self, device: Device) -> Result<Self, RustNumError> {
        if self.device != device {
            // 这里应该实现实际的设备间数据传输
            // 目前只是简单地更新设备标记
            self.device = device;
        }
        Ok(self)
    }
    
    /// 转换数据类型
    pub fn to_dtype(self, dtype: DataType) -> Result<Self, RustNumError> {
        // 这里应该实现实际的数据类型转换
        // 目前只是简单地更新类型标记
        Ok(Self {
            data: self.data,
            device: self.device,
            dtype,
            requires_grad: self.requires_grad,
            grad: self.grad,
            autograd_meta: self.autograd_meta,
        })
    }
    
    /// 重塑形状
    pub fn reshape(&self, new_shape: &[usize]) -> Result<Self, RustNumError> {
        let reshaped_data = self.data.reshape(new_shape)?;
        Ok(Self {
            data: reshaped_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None, // 重塑后清空梯度
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// 转置
    pub fn transpose(&self) -> Result<Self, RustNumError> {
        let transposed_data = self.data.transpose()?;
        Ok(Self {
            data: transposed_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
}

/// 张量操作特征
pub trait TensorOps<T> {
    /// 加法
    fn add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 减法
    fn sub(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 乘法
    fn mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 除法
    fn div(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 矩阵乘法
    fn matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 标量加法
    fn add_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 标量乘法
    fn mul_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 求和
    fn sum(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 平均值
    fn mean(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 最大值
    fn max(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 最小值
    fn min(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl TensorOps<f32> for Tensor<f32> {
    fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        let result_data = self.data.add(&other.data)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad || other.requires_grad,
            grad: None,
            autograd_meta: None, // 需要在自动微分中设置
        })
    }
    
    fn sub(&self, other: &Self) -> Result<Self, RustNumError> {
        let result_data = self.data.sub(&other.data)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad || other.requires_grad,
            grad: None,
            autograd_meta: None,
        })
    }
    
    fn mul(&self, other: &Self) -> Result<Self, RustNumError> {
        let result_data = self.data.mul(&other.data)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad || other.requires_grad,
            grad: None,
            autograd_meta: None,
        })
    }
    
    fn div(&self, other: &Self) -> Result<Self, RustNumError> {
        let result_data = self.data.div(&other.data)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad || other.requires_grad,
            grad: None,
            autograd_meta: None,
        })
    }
    
    fn matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        let result_data = self.data.matmul(&other.data)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad || other.requires_grad,
            grad: None,
            autograd_meta: None,
        })
    }
    
    fn add_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let result_data = self.data.add_scalar(scalar)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    fn mul_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let result_data = self.data.mul_scalar(scalar)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    fn sum(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.sum()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    fn mean(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.mean()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    fn max(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.max()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: false, // max 操作通常不需要梯度
            grad: None,
            autograd_meta: None,
        })
    }
    
    fn min(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.min()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: false, // min 操作通常不需要梯度
            grad: None,
            autograd_meta: None,
        })
    }
}

// 为 f32 张量实现额外的数学函数
impl Tensor<f32> {
    /// 指数函数
    pub fn exp(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.exp()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// 对数函数
    pub fn log(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.log()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// ReLU 激活函数
    pub fn relu(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.relu()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// Sigmoid 激活函数
    pub fn sigmoid(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.sigmoid()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// Softmax 函数
    pub fn softmax(&self, dim: isize) -> Result<Self, RustNumError> {
        let result_data = self.data.softmax(dim)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// Log Softmax 函数
    pub fn log_softmax(&self, dim: isize) -> Result<Self, RustNumError> {
        let softmax = self.softmax(dim)?;
        softmax.log()
    }
    
    /// 负值
    pub fn neg(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.neg()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// 平方根
    pub fn sqrt(&self) -> Result<Self, RustNumError> {
        let result_data = self.data.sqrt()?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// 大于比较
    pub fn gt_scalar(&self, scalar: f32) -> Result<Tensor<f32>, RustNumError> {
        let result_data = self.data.gt_scalar(scalar)?;
        Ok(Tensor {
            data: result_data,
            device: self.device.clone(),
            dtype: DataType::Float32,
            requires_grad: false,
            grad: None,
            autograd_meta: None,
        })
    }
    
    /// 按轴求和
    pub fn sum_axis(&self, axis: usize) -> Result<Self, RustNumError> {
        let result_data = self.data.sum_axis(axis)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
    
    /// 除以标量
    pub fn div_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let result_data = self.data.div_scalar(scalar)?;
        Ok(Self {
            data: result_data,
            device: self.device.clone(),
            dtype: self.dtype.clone(),
            requires_grad: self.requires_grad,
            grad: None,
            autograd_meta: self.autograd_meta.clone(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_tensor_creation() {
        let data = vec![1.0f32, 2.0, 3.0, 4.0];
        let tensor = Tensor::new(data, vec![2, 2], Device::CPU).unwrap();
        
        assert_eq!(tensor.shape(), &[2, 2]);
        assert_eq!(tensor.device(), &Device::CPU);
        assert_eq!(tensor.dtype(), &DataType::Float32);
        assert!(!tensor.requires_grad());
    }
    
    #[test]
    fn test_tensor_operations() {
        let a = Tensor::ones(&[2, 2]).unwrap();
        let b = Tensor::ones(&[2, 2]).unwrap().mul_scalar(2.0).unwrap();
        
        let c = a.add(&b).unwrap();
        
        assert_eq!(c.shape(), &[2, 2]);
        for &val in c.data() {
            assert_eq!(val, 3.0); // 1 + 2 = 3
        }
    }
    
    #[test]
    fn test_tensor_math_functions() {
        let tensor = Tensor::ones(&[2, 2]).unwrap();
        
        let exp_result = tensor.exp().unwrap();
        let log_result = tensor.log().unwrap();
        let relu_result = tensor.relu().unwrap();
        
        assert_eq!(exp_result.shape(), &[2, 2]);
        assert_eq!(log_result.shape(), &[2, 2]);
        assert_eq!(relu_result.shape(), &[2, 2]);
    }
    
    #[test]
    fn test_tensor_reshape() {
        let tensor = Tensor::ones(&[2, 2]).unwrap();
        let reshaped = tensor.reshape(&[4]).unwrap();
        
        assert_eq!(reshaped.shape(), &[4]);
        assert_eq!(reshaped.numel(), 4);
    }
    
    #[test]
    fn test_tensor_device_transfer() {
        let tensor = Tensor::ones(&[2, 2]).unwrap();
        assert_eq!(tensor.device(), &Device::CPU);
        
        let gpu_tensor = tensor.to_device(Device::CUDA(0)).unwrap();
        assert_eq!(gpu_tensor.device(), &Device::CUDA(0));
    }
}
