//! 自动微分系统
//! 
//! 实现反向传播和梯度计算

use crate::error::RustNumError;
use crate::array::RustArray;
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex, Weak};
use std::cell::RefCell;

/// 变量 ID
pub type VariableId = usize;

/// 自动微分引擎
pub struct AutogradEngine {
    enabled: bool,
    computation_graph: RefCell<ComputationGraph>,
    next_id: RefCell<VariableId>,
}

impl AutogradEngine {
    /// 创建新的自动微分引擎
    pub fn new(enabled: bool) -> Self {
        Self {
            enabled,
            computation_graph: RefCell::new(ComputationGraph::new()),
            next_id: RefCell::new(0),
        }
    }
    
    /// 启用自动微分
    pub fn enable(&mut self) {
        self.enabled = true;
    }
    
    /// 禁用自动微分
    pub fn disable(&mut self) {
        self.enabled = false;
    }
    
    /// 检查是否启用
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }
    
    /// 生成新的变量 ID
    pub fn next_id(&self) -> VariableId {
        let mut id = self.next_id.borrow_mut();
        let current = *id;
        *id += 1;
        current
    }
    
    /// 注册变量
    pub fn register_variable(&self, var: Variable) -> VariableId {
        if !self.enabled {
            return var.id;
        }
        
        let mut graph = self.computation_graph.borrow_mut();
        graph.add_variable(var.clone());
        var.id
    }
    
    /// 注册操作
    pub fn register_operation(&self, op: Operation) -> VariableId {
        if !self.enabled {
            return op.output_id;
        }
        
        let mut graph = self.computation_graph.borrow_mut();
        graph.add_operation(op);
        op.output_id
    }
    
    /// 反向传播
    pub fn backward(&self, output_id: VariableId) -> Result<HashMap<VariableId, RustArray<f32>>, RustNumError> {
        if !self.enabled {
            return Ok(HashMap::new());
        }
        
        let graph = self.computation_graph.borrow();
        graph.backward(output_id)
    }
    
    /// 清空计算图
    pub fn clear(&self) {
        let mut graph = self.computation_graph.borrow_mut();
        graph.clear();
    }
}

/// 变量
#[derive(Debug, Clone)]
pub struct Variable {
    pub id: VariableId,
    pub data: RustArray<f32>,
    pub requires_grad: bool,
    pub grad: Option<RustArray<f32>>,
    pub creator: Option<OperationType>,
}

impl Variable {
    /// 创建新变量
    pub fn new(data: RustArray<f32>, requires_grad: bool) -> Self {
        Self {
            id: 0, // 将由引擎分配
            data,
            requires_grad,
            grad: None,
            creator: None,
        }
    }
    
    /// 创建叶子变量
    pub fn leaf(data: RustArray<f32>, requires_grad: bool) -> Self {
        Self::new(data, requires_grad)
    }
    
    /// 设置梯度
    pub fn set_grad(&mut self, grad: RustArray<f32>) {
        self.grad = Some(grad);
    }
    
    /// 获取梯度
    pub fn grad(&self) -> Option<&RustArray<f32>> {
        self.grad.as_ref()
    }
    
    /// 清零梯度
    pub fn zero_grad(&mut self) {
        self.grad = None;
    }
    
    /// 反向传播
    pub fn backward(&self, engine: &AutogradEngine) -> Result<(), RustNumError> {
        let gradients = engine.backward(self.id)?;
        // 这里需要将计算出的梯度设置到相应的变量中
        Ok(())
    }
}

/// 操作类型
#[derive(Debug, Clone)]
pub enum OperationType {
    Add,
    Sub,
    Mul,
    Div,
    MatMul,
    Sum,
    Mean,
    Exp,
    Log,
    Sin,
    Cos,
    Tanh,
    Relu,
    Sigmoid,
    Softmax,
}

/// 操作
#[derive(Debug, Clone)]
pub struct Operation {
    pub op_type: OperationType,
    pub input_ids: Vec<VariableId>,
    pub output_id: VariableId,
    pub forward_fn: Option<Arc<dyn Fn(&[&RustArray<f32>]) -> Result<RustArray<f32>, RustNumError> + Send + Sync>>,
    pub backward_fn: Option<Arc<dyn Fn(&RustArray<f32>, &[&RustArray<f32>]) -> Result<Vec<RustArray<f32>>, RustNumError> + Send + Sync>>,
}

/// 计算图
#[derive(Debug)]
pub struct ComputationGraph {
    variables: HashMap<VariableId, Variable>,
    operations: HashMap<VariableId, Operation>,
    dependencies: HashMap<VariableId, Vec<VariableId>>,
}

impl ComputationGraph {
    /// 创建新的计算图
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
            operations: HashMap::new(),
            dependencies: HashMap::new(),
        }
    }
    
    /// 添加变量
    pub fn add_variable(&mut self, var: Variable) {
        self.variables.insert(var.id, var);
    }
    
    /// 添加操作
    pub fn add_operation(&mut self, op: Operation) {
        // 记录依赖关系
        for &input_id in &op.input_ids {
            self.dependencies.entry(op.output_id).or_insert_with(Vec::new).push(input_id);
        }
        
        self.operations.insert(op.output_id, op);
    }
    
    /// 反向传播
    pub fn backward(&self, output_id: VariableId) -> Result<HashMap<VariableId, RustArray<f32>>, RustNumError> {
        let mut gradients: HashMap<VariableId, RustArray<f32>> = HashMap::new();
        let mut visited = std::collections::HashSet::new();
        
        // 拓扑排序
        let topo_order = self.topological_sort(output_id)?;
        
        // 初始化输出梯度为 1
        if let Some(output_var) = self.variables.get(&output_id) {
            let ones = RustArray::ones(output_var.data.shape())?;
            gradients.insert(output_id, ones);
        }
        
        // 反向遍历
        for &var_id in topo_order.iter().rev() {
            if let Some(op) = self.operations.get(&var_id) {
                if let Some(output_grad) = gradients.get(&var_id) {
                    // 计算输入梯度
                    let input_grads = self.compute_input_gradients(op, output_grad)?;
                    
                    // 累积梯度
                    for (i, &input_id) in op.input_ids.iter().enumerate() {
                        if let Some(input_grad) = input_grads.get(i) {
                            let accumulated_grad = gradients.entry(input_id)
                                .or_insert_with(|| RustArray::zeros(input_grad.shape()).unwrap());
                            
                            *accumulated_grad = accumulated_grad.add(input_grad)?;
                        }
                    }
                }
            }
        }
        
        Ok(gradients)
    }
    
    /// 拓扑排序
    fn topological_sort(&self, start_id: VariableId) -> Result<Vec<VariableId>, RustNumError> {
        let mut result = Vec::new();
        let mut visited = std::collections::HashSet::new();
        let mut temp_visited = std::collections::HashSet::new();
        
        self.dfs_visit(start_id, &mut visited, &mut temp_visited, &mut result)?;
        
        Ok(result)
    }
    
    /// 深度优先搜索访问
    fn dfs_visit(
        &self,
        node_id: VariableId,
        visited: &mut std::collections::HashSet<VariableId>,
        temp_visited: &mut std::collections::HashSet<VariableId>,
        result: &mut Vec<VariableId>,
    ) -> Result<(), RustNumError> {
        if temp_visited.contains(&node_id) {
            return Err(RustNumError::ComputationError("Cycle detected in computation graph".into()));
        }
        
        if visited.contains(&node_id) {
            return Ok(());
        }
        
        temp_visited.insert(node_id);
        
        // 访问依赖
        if let Some(deps) = self.dependencies.get(&node_id) {
            for &dep_id in deps {
                self.dfs_visit(dep_id, visited, temp_visited, result)?;
            }
        }
        
        temp_visited.remove(&node_id);
        visited.insert(node_id);
        result.push(node_id);
        
        Ok(())
    }
    
    /// 计算输入梯度
    fn compute_input_gradients(&self, op: &Operation, output_grad: &RustArray<f32>) -> Result<Vec<RustArray<f32>>, RustNumError> {
        // 获取输入数据
        let input_data: Vec<&RustArray<f32>> = op.input_ids.iter()
            .filter_map(|&id| self.variables.get(&id).map(|v| &v.data))
            .collect();
        
        // 根据操作类型计算梯度
        match op.op_type {
            OperationType::Add => {
                // 加法的梯度就是输出梯度
                Ok(vec![output_grad.clone(), output_grad.clone()])
            }
            OperationType::Sub => {
                // 减法的梯度：第一个输入为正，第二个输入为负
                Ok(vec![output_grad.clone(), output_grad.neg()?])
            }
            OperationType::Mul => {
                // 乘法的梯度：grad_a = grad_out * b, grad_b = grad_out * a
                if input_data.len() >= 2 {
                    let grad_a = output_grad.mul(input_data[1])?;
                    let grad_b = output_grad.mul(input_data[0])?;
                    Ok(vec![grad_a, grad_b])
                } else {
                    Err(RustNumError::ComputationError("Insufficient inputs for multiplication".into()))
                }
            }
            OperationType::MatMul => {
                // 矩阵乘法的梯度
                if input_data.len() >= 2 {
                    let grad_a = output_grad.matmul(&input_data[1].transpose()?)?;
                    let grad_b = input_data[0].transpose()?.matmul(output_grad)?;
                    Ok(vec![grad_a, grad_b])
                } else {
                    Err(RustNumError::ComputationError("Insufficient inputs for matrix multiplication".into()))
                }
            }
            OperationType::Sum => {
                // 求和的梯度是广播输出梯度
                if let Some(input) = input_data.first() {
                    let grad = self.broadcast_gradient(output_grad, input.shape())?;
                    Ok(vec![grad])
                } else {
                    Err(RustNumError::ComputationError("No input for sum operation".into()))
                }
            }
            OperationType::Relu => {
                // ReLU 的梯度：input > 0 ? grad_out : 0
                if let Some(input) = input_data.first() {
                    let mask = input.gt_scalar(0.0)?;
                    let grad = output_grad.mul(&mask)?;
                    Ok(vec![grad])
                } else {
                    Err(RustNumError::ComputationError("No input for ReLU operation".into()))
                }
            }
            OperationType::Sigmoid => {
                // Sigmoid 的梯度：grad_out * sigmoid(x) * (1 - sigmoid(x))
                if let Some(input) = input_data.first() {
                    let sigmoid_out = input.sigmoid()?;
                    let one_minus_sigmoid = sigmoid_out.sub_scalar(1.0)?.neg()?;
                    let grad = output_grad.mul(&sigmoid_out)?.mul(&one_minus_sigmoid)?;
                    Ok(vec![grad])
                } else {
                    Err(RustNumError::ComputationError("No input for sigmoid operation".into()))
                }
            }
            _ => {
                // 其他操作的梯度计算
                Ok(vec![output_grad.clone()])
            }
        }
    }
    
    /// 广播梯度
    fn broadcast_gradient(&self, grad: &RustArray<f32>, target_shape: &[usize]) -> Result<RustArray<f32>, RustNumError> {
        // 简化实现：如果形状匹配，直接返回；否则求和到目标形状
        if grad.shape() == target_shape {
            Ok(grad.clone())
        } else {
            // 这里需要实现更复杂的广播逆向逻辑
            // 简化为求和到第一个维度
            if target_shape.len() == 1 && grad.shape().len() > 1 {
                grad.sum_axis(0)
            } else {
                Ok(grad.clone())
            }
        }
    }
    
    /// 清空计算图
    pub fn clear(&mut self) {
        self.variables.clear();
        self.operations.clear();
        self.dependencies.clear();
    }
}

/// 函数特征
pub trait Function {
    /// 前向传播
    fn forward(&self, inputs: &[&Variable]) -> Result<Variable, RustNumError>;
    
    /// 反向传播
    fn backward(&self, grad_output: &RustArray<f32>, inputs: &[&Variable]) -> Result<Vec<RustArray<f32>>, RustNumError>;
}

/// 加法函数
pub struct AddFunction;

impl Function for AddFunction {
    fn forward(&self, inputs: &[&Variable]) -> Result<Variable, RustNumError> {
        if inputs.len() != 2 {
            return Err(RustNumError::InvalidInput("Add requires exactly 2 inputs".into()));
        }
        
        let result_data = inputs[0].data.add(&inputs[1].data)?;
        let requires_grad = inputs[0].requires_grad || inputs[1].requires_grad;
        
        Ok(Variable {
            id: 0, // 将由引擎分配
            data: result_data,
            requires_grad,
            grad: None,
            creator: Some(OperationType::Add),
        })
    }
    
    fn backward(&self, grad_output: &RustArray<f32>, _inputs: &[&Variable]) -> Result<Vec<RustArray<f32>>, RustNumError> {
        Ok(vec![grad_output.clone(), grad_output.clone()])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;
    
    #[test]
    fn test_autograd_engine() {
        let engine = AutogradEngine::new(true);
        assert!(engine.is_enabled());
        
        let id1 = engine.next_id();
        let id2 = engine.next_id();
        assert_ne!(id1, id2);
    }
    
    #[test]
    fn test_variable_creation() {
        let data = RustArray::<f32>::ones(&[2, 2]).unwrap();
        let var = Variable::new(data, true);
        
        assert!(var.requires_grad);
        assert!(var.grad.is_none());
    }
    
    #[test]
    fn test_computation_graph() {
        let mut graph = ComputationGraph::new();
        
        let data1 = RustArray::<f32>::ones(&[2, 2]).unwrap();
        let var1 = Variable::new(data1, true);
        graph.add_variable(var1);
        
        assert_eq!(graph.variables.len(), 1);
    }
    
    #[test]
    fn test_add_function() {
        let add_fn = AddFunction;
        
        let data1 = RustArray::<f32>::ones(&[2, 2]).unwrap();
        let data2 = RustArray::<f32>::full(&[2, 2], 2.0).unwrap();
        
        let var1 = Variable::new(data1, true);
        let var2 = Variable::new(data2, true);
        
        let result = add_fn.forward(&[&var1, &var2]).unwrap();
        
        assert!(result.requires_grad);
        assert_eq!(result.data.shape(), &[2, 2]);
        
        // 验证结果值
        for &val in result.data.data() {
            assert_eq!(val, 3.0); // 1 + 2 = 3
        }
    }
}
