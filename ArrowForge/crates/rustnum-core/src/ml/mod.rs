//! AI/ML 生态集成模块
//! 
//! 提供机器学习和深度学习框架的集成支持

pub mod autograd;
pub mod tensor;
pub mod pytorch_bridge;
pub mod tensorflow_bridge;
pub mod feature_engineering;
pub mod model_serving;

pub use autograd::{Variable, Function, AutogradEngine};
pub use tensor::{Tensor, TensorOps, Device, DataType};
pub use pytorch_bridge::{PyTorchBridge, TorchTensor};
pub use tensorflow_bridge::{TensorFlowBridge, TfTensor};
pub use feature_engineering::{FeatureTransformer, Pipeline, Scaler};
pub use model_serving::{ModelServer, InferenceEngine, ModelFormat};

use crate::error::RustNumError;
use crate::array::RustArray;
use std::collections::HashMap;
use std::sync::Arc;

/// ML 配置
#[derive(Debug, Clone)]
pub struct MLConfig {
    /// 默认设备
    pub default_device: Device,
    /// 自动梯度计算
    pub enable_autograd: bool,
    /// 内存优化
    pub memory_optimization: bool,
    /// 并行度
    pub num_threads: Option<usize>,
}

impl Default for MLConfig {
    fn default() -> Self {
        Self {
            default_device: Device::CPU,
            enable_autograd: true,
            memory_optimization: true,
            num_threads: None,
        }
    }
}

/// ML 上下文
pub struct MLContext {
    config: MLConfig,
    autograd_engine: AutogradEngine,
    model_registry: HashMap<String, Arc<dyn ModelInterface>>,
    feature_pipelines: HashMap<String, Pipeline>,
}

impl MLContext {
    /// 创建新的 ML 上下文
    pub fn new(config: MLConfig) -> Self {
        Self {
            config: config.clone(),
            autograd_engine: AutogradEngine::new(config.enable_autograd),
            model_registry: HashMap::new(),
            feature_pipelines: HashMap::new(),
        }
    }
    
    /// 创建张量
    pub fn tensor<T>(&self, data: Vec<T>, shape: Vec<usize>) -> Result<Tensor<T>, RustNumError>
    where
        T: Copy + Default + Send + Sync + 'static
    {
        Tensor::new(data, shape, self.config.default_device.clone())
    }
    
    /// 从 RustArray 创建张量
    pub fn from_array<T>(&self, array: &RustArray<T>) -> Result<Tensor<T>, RustNumError>
    where
        T: Copy + Default + Send + Sync + 'static
    {
        Tensor::from_array(array, self.config.default_device.clone())
    }
    
    /// 注册模型
    pub fn register_model(&mut self, name: String, model: Arc<dyn ModelInterface>) {
        self.model_registry.insert(name, model);
    }
    
    /// 获取模型
    pub fn get_model(&self, name: &str) -> Option<&Arc<dyn ModelInterface>> {
        self.model_registry.get(name)
    }
    
    /// 注册特征管道
    pub fn register_pipeline(&mut self, name: String, pipeline: Pipeline) {
        self.feature_pipelines.insert(name, pipeline);
    }
    
    /// 获取特征管道
    pub fn get_pipeline(&self, name: &str) -> Option<&Pipeline> {
        self.feature_pipelines.get(name)
    }
    
    /// 启用自动梯度
    pub fn enable_grad(&mut self) {
        self.autograd_engine.enable();
    }
    
    /// 禁用自动梯度
    pub fn disable_grad(&mut self) {
        self.autograd_engine.disable();
    }
}

/// 模型接口
pub trait ModelInterface: Send + Sync {
    /// 前向传播
    fn forward(&self, input: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError>;
    
    /// 预测
    fn predict(&self, input: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        self.forward(input)
    }
    
    /// 获取模型参数
    fn parameters(&self) -> Vec<&Tensor<f32>>;
    
    /// 设置训练模式
    fn train(&mut self);
    
    /// 设置评估模式
    fn eval(&mut self);
}

/// 损失函数特征
pub trait LossFunction {
    /// 计算损失
    fn compute_loss(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError>;
    
    /// 计算梯度
    fn compute_gradient(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError>;
}

/// 均方误差损失
pub struct MSELoss;

impl LossFunction for MSELoss {
    fn compute_loss(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        // 计算 MSE: mean((predictions - targets)^2)
        let diff = predictions.sub(targets)?;
        let squared = diff.mul(&diff)?;
        squared.mean()
    }
    
    fn compute_gradient(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        // MSE 梯度: 2 * (predictions - targets) / n
        let diff = predictions.sub(targets)?;
        let n = predictions.numel() as f32;
        diff.mul_scalar(2.0 / n)
    }
}

/// 交叉熵损失
pub struct CrossEntropyLoss;

impl LossFunction for CrossEntropyLoss {
    fn compute_loss(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        // 计算交叉熵损失
        let log_probs = predictions.log_softmax(-1)?;
        let loss = targets.mul(&log_probs)?.sum()?;
        loss.neg()?.div_scalar(predictions.shape()[0] as f32)
    }
    
    fn compute_gradient(&self, predictions: &Tensor<f32>, targets: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        // 交叉熵梯度: softmax(predictions) - targets
        let softmax = predictions.softmax(-1)?;
        softmax.sub(targets)
    }
}

/// 优化器特征
pub trait Optimizer {
    /// 更新参数
    fn step(&mut self, parameters: &mut [Tensor<f32>], gradients: &[Tensor<f32>]) -> Result<(), RustNumError>;
    
    /// 清零梯度
    fn zero_grad(&mut self, parameters: &mut [Tensor<f32>]) -> Result<(), RustNumError>;
    
    /// 设置学习率
    fn set_learning_rate(&mut self, lr: f32);
}

/// SGD 优化器
pub struct SGDOptimizer {
    learning_rate: f32,
    momentum: f32,
    velocity: Vec<Tensor<f32>>,
}

impl SGDOptimizer {
    pub fn new(learning_rate: f32, momentum: f32) -> Self {
        Self {
            learning_rate,
            momentum,
            velocity: Vec::new(),
        }
    }
}

impl Optimizer for SGDOptimizer {
    fn step(&mut self, parameters: &mut [Tensor<f32>], gradients: &[Tensor<f32>]) -> Result<(), RustNumError> {
        if self.velocity.is_empty() {
            // 初始化动量
            for param in parameters.iter() {
                self.velocity.push(Tensor::zeros_like(param)?);
            }
        }
        
        for (i, (param, grad)) in parameters.iter_mut().zip(gradients.iter()).enumerate() {
            // 更新动量: v = momentum * v + lr * grad
            self.velocity[i] = self.velocity[i].mul_scalar(self.momentum)?.add(&grad.mul_scalar(self.learning_rate)?)?;
            
            // 更新参数: param = param - v
            *param = param.sub(&self.velocity[i])?;
        }
        
        Ok(())
    }
    
    fn zero_grad(&mut self, parameters: &mut [Tensor<f32>]) -> Result<(), RustNumError> {
        // 清零梯度（在实际实现中，梯度存储在张量的 grad 字段中）
        for param in parameters.iter_mut() {
            param.zero_grad()?;
        }
        Ok(())
    }
    
    fn set_learning_rate(&mut self, lr: f32) {
        self.learning_rate = lr;
    }
}

/// Adam 优化器
pub struct AdamOptimizer {
    learning_rate: f32,
    beta1: f32,
    beta2: f32,
    epsilon: f32,
    step_count: usize,
    m: Vec<Tensor<f32>>, // 一阶动量
    v: Vec<Tensor<f32>>, // 二阶动量
}

impl AdamOptimizer {
    pub fn new(learning_rate: f32, beta1: f32, beta2: f32, epsilon: f32) -> Self {
        Self {
            learning_rate,
            beta1,
            beta2,
            epsilon,
            step_count: 0,
            m: Vec::new(),
            v: Vec::new(),
        }
    }
}

impl Optimizer for AdamOptimizer {
    fn step(&mut self, parameters: &mut [Tensor<f32>], gradients: &[Tensor<f32>]) -> Result<(), RustNumError> {
        if self.m.is_empty() {
            // 初始化动量
            for param in parameters.iter() {
                self.m.push(Tensor::zeros_like(param)?);
                self.v.push(Tensor::zeros_like(param)?);
            }
        }
        
        self.step_count += 1;
        
        for (i, (param, grad)) in parameters.iter_mut().zip(gradients.iter()).enumerate() {
            // 更新一阶动量: m = beta1 * m + (1 - beta1) * grad
            self.m[i] = self.m[i].mul_scalar(self.beta1)?.add(&grad.mul_scalar(1.0 - self.beta1)?)?;
            
            // 更新二阶动量: v = beta2 * v + (1 - beta2) * grad^2
            let grad_squared = grad.mul(grad)?;
            self.v[i] = self.v[i].mul_scalar(self.beta2)?.add(&grad_squared.mul_scalar(1.0 - self.beta2)?)?;
            
            // 偏差修正
            let m_hat = self.m[i].div_scalar(1.0 - self.beta1.powi(self.step_count as i32))?;
            let v_hat = self.v[i].div_scalar(1.0 - self.beta2.powi(self.step_count as i32))?;
            
            // 更新参数: param = param - lr * m_hat / (sqrt(v_hat) + epsilon)
            let denominator = v_hat.sqrt()?.add_scalar(self.epsilon)?;
            let update = m_hat.div(&denominator)?.mul_scalar(self.learning_rate)?;
            *param = param.sub(&update)?;
        }
        
        Ok(())
    }
    
    fn zero_grad(&mut self, parameters: &mut [Tensor<f32>]) -> Result<(), RustNumError> {
        for param in parameters.iter_mut() {
            param.zero_grad()?;
        }
        Ok(())
    }
    
    fn set_learning_rate(&mut self, lr: f32) {
        self.learning_rate = lr;
    }
}

/// ML 工厂
pub struct MLFactory;

impl MLFactory {
    /// 创建损失函数
    pub fn create_loss(loss_type: &str) -> Result<Box<dyn LossFunction>, RustNumError> {
        match loss_type {
            "mse" => Ok(Box::new(MSELoss)),
            "cross_entropy" => Ok(Box::new(CrossEntropyLoss)),
            _ => Err(RustNumError::UnsupportedOperation(format!("Unknown loss type: {}", loss_type))),
        }
    }
    
    /// 创建优化器
    pub fn create_optimizer(optimizer_type: &str, learning_rate: f32) -> Result<Box<dyn Optimizer>, RustNumError> {
        match optimizer_type {
            "sgd" => Ok(Box::new(SGDOptimizer::new(learning_rate, 0.9))),
            "adam" => Ok(Box::new(AdamOptimizer::new(learning_rate, 0.9, 0.999, 1e-8))),
            _ => Err(RustNumError::UnsupportedOperation(format!("Unknown optimizer type: {}", optimizer_type))),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_ml_context_creation() {
        let config = MLConfig::default();
        let context = MLContext::new(config);
        
        assert!(context.autograd_engine.is_enabled());
        assert_eq!(context.model_registry.len(), 0);
    }
    
    #[test]
    fn test_mse_loss() {
        let loss = MSELoss;
        
        // 创建简单的预测和目标张量（这里需要实际的张量实现）
        // let predictions = Tensor::new(vec![1.0, 2.0, 3.0], vec![3]);
        // let targets = Tensor::new(vec![1.5, 2.5, 2.5], vec![3]);
        
        // let loss_value = loss.compute_loss(&predictions, &targets).unwrap();
        // 验证损失计算
    }
    
    #[test]
    fn test_sgd_optimizer() {
        let mut optimizer = SGDOptimizer::new(0.01, 0.9);
        
        // 测试学习率设置
        optimizer.set_learning_rate(0.001);
        assert_eq!(optimizer.learning_rate, 0.001);
    }
    
    #[test]
    fn test_adam_optimizer() {
        let mut optimizer = AdamOptimizer::new(0.001, 0.9, 0.999, 1e-8);
        
        // 测试参数
        assert_eq!(optimizer.learning_rate, 0.001);
        assert_eq!(optimizer.beta1, 0.9);
        assert_eq!(optimizer.beta2, 0.999);
        assert_eq!(optimizer.step_count, 0);
    }
    
    #[test]
    fn test_ml_factory() {
        // 测试损失函数创建
        let mse_loss = MLFactory::create_loss("mse");
        assert!(mse_loss.is_ok());
        
        let ce_loss = MLFactory::create_loss("cross_entropy");
        assert!(ce_loss.is_ok());
        
        let unknown_loss = MLFactory::create_loss("unknown");
        assert!(unknown_loss.is_err());
        
        // 测试优化器创建
        let sgd_optimizer = MLFactory::create_optimizer("sgd", 0.01);
        assert!(sgd_optimizer.is_ok());
        
        let adam_optimizer = MLFactory::create_optimizer("adam", 0.001);
        assert!(adam_optimizer.is_ok());
        
        let unknown_optimizer = MLFactory::create_optimizer("unknown", 0.01);
        assert!(unknown_optimizer.is_err());
    }
}
