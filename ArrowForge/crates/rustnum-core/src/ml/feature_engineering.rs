//! 特征工程模块
//! 
//! 提供数据预处理和特征变换功能

use crate::error::RustNumError;
use super::tensor::Tensor;
use crate::array::RustArray;
use std::collections::HashMap;

/// 特征变换器特征
pub trait FeatureTransformer {
    /// 拟合变换器
    fn fit(&mut self, data: &Tensor<f32>) -> Result<(), RustNumError>;
    
    /// 应用变换
    fn transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError>;
    
    /// 拟合并变换
    fn fit_transform(&mut self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        self.fit(data)?;
        self.transform(data)
    }
    
    /// 逆变换
    fn inverse_transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError>;
}

/// 标准化器
#[derive(Debug, Clone)]
pub struct StandardScaler {
    mean: Option<Tensor<f32>>,
    std: Option<Tensor<f32>>,
    fitted: bool,
}

impl StandardScaler {
    /// 创建新的标准化器
    pub fn new() -> Self {
        Self {
            mean: None,
            std: None,
            fitted: false,
        }
    }
}

impl FeatureTransformer for StandardScaler {
    fn fit(&mut self, data: &Tensor<f32>) -> Result<(), RustNumError> {
        // 计算均值和标准差
        let mean = data.mean()?;
        
        // 计算方差
        let diff = data.sub(&mean)?;
        let variance = diff.mul(&diff)?.mean()?;
        let std = variance.sqrt()?;
        
        self.mean = Some(mean);
        self.std = Some(std);
        self.fitted = true;
        
        Ok(())
    }
    
    fn transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if !self.fitted {
            return Err(RustNumError::InvalidState("Scaler not fitted".into()));
        }
        
        let mean = self.mean.as_ref().unwrap();
        let std = self.std.as_ref().unwrap();
        
        // (data - mean) / std
        let centered = data.sub(mean)?;
        centered.div(std)
    }
    
    fn inverse_transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if !self.fitted {
            return Err(RustNumError::InvalidState("Scaler not fitted".into()));
        }
        
        let mean = self.mean.as_ref().unwrap();
        let std = self.std.as_ref().unwrap();
        
        // data * std + mean
        let scaled = data.mul(std)?;
        scaled.add(mean)
    }
}

/// 最小-最大缩放器
#[derive(Debug, Clone)]
pub struct MinMaxScaler {
    min: Option<Tensor<f32>>,
    max: Option<Tensor<f32>>,
    feature_range: (f32, f32),
    fitted: bool,
}

impl MinMaxScaler {
    /// 创建新的最小-最大缩放器
    pub fn new(feature_range: (f32, f32)) -> Self {
        Self {
            min: None,
            max: None,
            feature_range,
            fitted: false,
        }
    }
}

impl FeatureTransformer for MinMaxScaler {
    fn fit(&mut self, data: &Tensor<f32>) -> Result<(), RustNumError> {
        let min = data.min()?;
        let max = data.max()?;
        
        self.min = Some(min);
        self.max = Some(max);
        self.fitted = true;
        
        Ok(())
    }
    
    fn transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if !self.fitted {
            return Err(RustNumError::InvalidState("Scaler not fitted".into()));
        }
        
        let min = self.min.as_ref().unwrap();
        let max = self.max.as_ref().unwrap();
        
        // (data - min) / (max - min) * (max_range - min_range) + min_range
        let range = max.sub(min)?;
        let normalized = data.sub(min)?.div(&range)?;
        
        let scale = self.feature_range.1 - self.feature_range.0;
        normalized.mul_scalar(scale)?.add_scalar(self.feature_range.0)
    }
    
    fn inverse_transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if !self.fitted {
            return Err(RustNumError::InvalidState("Scaler not fitted".into()));
        }
        
        let min = self.min.as_ref().unwrap();
        let max = self.max.as_ref().unwrap();
        
        let scale = self.feature_range.1 - self.feature_range.0;
        let range = max.sub(min)?;
        
        // (data - min_range) / (max_range - min_range) * (max - min) + min
        let normalized = data.sub_scalar(self.feature_range.0)?.div_scalar(scale)?;
        normalized.mul(&range)?.add(min)
    }
}

/// 归一化器
#[derive(Debug, Clone)]
pub struct Normalizer {
    norm: NormType,
}

/// 归一化类型
#[derive(Debug, Clone)]
pub enum NormType {
    L1,
    L2,
    Max,
}

impl Normalizer {
    /// 创建新的归一化器
    pub fn new(norm: NormType) -> Self {
        Self { norm }
    }
}

impl FeatureTransformer for Normalizer {
    fn fit(&mut self, _data: &Tensor<f32>) -> Result<(), RustNumError> {
        // 归一化器不需要拟合
        Ok(())
    }
    
    fn transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        match self.norm {
            NormType::L1 => {
                let l1_norm = data.abs()?.sum()?;
                data.div(&l1_norm)
            }
            NormType::L2 => {
                let l2_norm = data.mul(data)?.sum()?.sqrt()?;
                data.div(&l2_norm)
            }
            NormType::Max => {
                let max_val = data.abs()?.max()?;
                data.div(&max_val)
            }
        }
    }
    
    fn inverse_transform(&self, _data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        Err(RustNumError::UnsupportedOperation("Normalizer inverse transform not supported".into()))
    }
}

/// 特征选择器
#[derive(Debug, Clone)]
pub struct FeatureSelector {
    selected_features: Option<Vec<usize>>,
    selection_method: SelectionMethod,
}

/// 特征选择方法
#[derive(Debug, Clone)]
pub enum SelectionMethod {
    VarianceThreshold(f32),
    KBest(usize),
    Percentile(f32),
}

impl FeatureSelector {
    /// 创建新的特征选择器
    pub fn new(method: SelectionMethod) -> Self {
        Self {
            selected_features: None,
            selection_method: method,
        }
    }
    
    /// 选择特征
    pub fn select_features(&mut self, data: &Tensor<f32>) -> Result<Vec<usize>, RustNumError> {
        match &self.selection_method {
            SelectionMethod::VarianceThreshold(threshold) => {
                self.select_by_variance(data, *threshold)
            }
            SelectionMethod::KBest(k) => {
                self.select_k_best(data, *k)
            }
            SelectionMethod::Percentile(percentile) => {
                self.select_by_percentile(data, *percentile)
            }
        }
    }
    
    fn select_by_variance(&mut self, data: &Tensor<f32>, threshold: f32) -> Result<Vec<usize>, RustNumError> {
        let num_features = data.shape()[1];
        let mut selected = Vec::new();
        
        for i in 0..num_features {
            // 计算第 i 个特征的方差
            let feature_data = self.extract_feature(data, i)?;
            let mean = feature_data.mean()?;
            let diff = feature_data.sub(&mean)?;
            let variance = diff.mul(&diff)?.mean()?;
            
            if variance.data()[0] > threshold {
                selected.push(i);
            }
        }
        
        self.selected_features = Some(selected.clone());
        Ok(selected)
    }
    
    fn select_k_best(&mut self, data: &Tensor<f32>, k: usize) -> Result<Vec<usize>, RustNumError> {
        let num_features = data.shape()[1];
        let mut feature_scores = Vec::new();
        
        for i in 0..num_features {
            let feature_data = self.extract_feature(data, i)?;
            let score = self.calculate_feature_score(&feature_data)?;
            feature_scores.push((i, score));
        }
        
        // 按分数排序并选择前 k 个
        feature_scores.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        let selected: Vec<usize> = feature_scores.into_iter().take(k).map(|(idx, _)| idx).collect();
        
        self.selected_features = Some(selected.clone());
        Ok(selected)
    }
    
    fn select_by_percentile(&mut self, data: &Tensor<f32>, percentile: f32) -> Result<Vec<usize>, RustNumError> {
        let num_features = data.shape()[1];
        let k = ((num_features as f32) * percentile / 100.0).ceil() as usize;
        self.select_k_best(data, k)
    }
    
    fn extract_feature(&self, data: &Tensor<f32>, feature_idx: usize) -> Result<Tensor<f32>, RustNumError> {
        // 提取第 feature_idx 列
        let num_samples = data.shape()[0];
        let mut feature_data = Vec::new();
        
        for i in 0..num_samples {
            let idx = i * data.shape()[1] + feature_idx;
            feature_data.push(data.data()[idx]);
        }
        
        Tensor::new(feature_data, vec![num_samples], data.device().clone())
    }
    
    fn calculate_feature_score(&self, feature_data: &Tensor<f32>) -> Result<f32, RustNumError> {
        // 简单的特征评分：使用方差
        let mean = feature_data.mean()?;
        let diff = feature_data.sub(&mean)?;
        let variance = diff.mul(&diff)?.mean()?;
        Ok(variance.data()[0])
    }
    
    /// 应用特征选择
    pub fn apply_selection(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if let Some(ref selected) = self.selected_features {
            let num_samples = data.shape()[0];
            let num_selected = selected.len();
            let mut selected_data = Vec::new();
            
            for i in 0..num_samples {
                for &feature_idx in selected {
                    let idx = i * data.shape()[1] + feature_idx;
                    selected_data.push(data.data()[idx]);
                }
            }
            
            Tensor::new(selected_data, vec![num_samples, num_selected], data.device().clone())
        } else {
            Err(RustNumError::InvalidState("Features not selected".into()))
        }
    }
}

/// 特征工程管道
#[derive(Debug)]
pub struct Pipeline {
    steps: Vec<(String, Box<dyn FeatureTransformer>)>,
    fitted: bool,
}

impl Pipeline {
    /// 创建新的管道
    pub fn new() -> Self {
        Self {
            steps: Vec::new(),
            fitted: false,
        }
    }
    
    /// 添加步骤
    pub fn add_step(&mut self, name: String, transformer: Box<dyn FeatureTransformer>) {
        self.steps.push((name, transformer));
    }
    
    /// 拟合管道
    pub fn fit(&mut self, data: &Tensor<f32>) -> Result<(), RustNumError> {
        let mut current_data = data.clone();
        
        for (_, transformer) in &mut self.steps {
            transformer.fit(&current_data)?;
            current_data = transformer.transform(&current_data)?;
        }
        
        self.fitted = true;
        Ok(())
    }
    
    /// 变换数据
    pub fn transform(&self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if !self.fitted {
            return Err(RustNumError::InvalidState("Pipeline not fitted".into()));
        }
        
        let mut current_data = data.clone();
        
        for (_, transformer) in &self.steps {
            current_data = transformer.transform(&current_data)?;
        }
        
        Ok(current_data)
    }
    
    /// 拟合并变换
    pub fn fit_transform(&mut self, data: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        self.fit(data)?;
        self.transform(data)
    }
    
    /// 获取步骤名称
    pub fn step_names(&self) -> Vec<&String> {
        self.steps.iter().map(|(name, _)| name).collect()
    }
}

/// 缩放器类型
pub type Scaler = Box<dyn FeatureTransformer>;

/// 特征工程工厂
pub struct FeatureEngineeringFactory;

impl FeatureEngineeringFactory {
    /// 创建标准化器
    pub fn create_standard_scaler() -> Scaler {
        Box::new(StandardScaler::new())
    }
    
    /// 创建最小-最大缩放器
    pub fn create_minmax_scaler(feature_range: (f32, f32)) -> Scaler {
        Box::new(MinMaxScaler::new(feature_range))
    }
    
    /// 创建归一化器
    pub fn create_normalizer(norm: NormType) -> Scaler {
        Box::new(Normalizer::new(norm))
    }
    
    /// 创建预设管道
    pub fn create_standard_pipeline() -> Pipeline {
        let mut pipeline = Pipeline::new();
        pipeline.add_step("scaler".to_string(), Self::create_standard_scaler());
        pipeline.add_step("normalizer".to_string(), Self::create_normalizer(NormType::L2));
        pipeline
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_standard_scaler() {
        let data = Tensor::new(vec![1.0, 2.0, 3.0, 4.0], vec![4, 1], Default::default()).unwrap();
        let mut scaler = StandardScaler::new();
        
        let transformed = scaler.fit_transform(&data).unwrap();
        assert_eq!(transformed.shape(), &[4, 1]);
        
        let inverse = scaler.inverse_transform(&transformed).unwrap();
        
        // 验证逆变换的正确性
        for (i, (&original, &recovered)) in data.data().iter().zip(inverse.data().iter()).enumerate() {
            assert!((original - recovered).abs() < 1e-5, 
                   "Mismatch at index {}: {} != {}", i, original, recovered);
        }
    }
    
    #[test]
    fn test_minmax_scaler() {
        let data = Tensor::new(vec![1.0, 2.0, 3.0, 4.0], vec![4, 1], Default::default()).unwrap();
        let mut scaler = MinMaxScaler::new((0.0, 1.0));
        
        let transformed = scaler.fit_transform(&data).unwrap();
        assert_eq!(transformed.shape(), &[4, 1]);
        
        // 验证缩放范围
        for &val in transformed.data() {
            assert!(val >= 0.0 && val <= 1.0);
        }
    }
    
    #[test]
    fn test_feature_selector() {
        let data = Tensor::new(
            vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0], 
            vec![2, 3], 
            Default::default()
        ).unwrap();
        
        let mut selector = FeatureSelector::new(SelectionMethod::KBest(2));
        let selected = selector.select_features(&data).unwrap();
        
        assert_eq!(selected.len(), 2);
        
        let selected_data = selector.apply_selection(&data).unwrap();
        assert_eq!(selected_data.shape(), &[2, 2]);
    }
    
    #[test]
    fn test_pipeline() {
        let data = Tensor::new(vec![1.0, 2.0, 3.0, 4.0], vec![4, 1], Default::default()).unwrap();
        let mut pipeline = FeatureEngineeringFactory::create_standard_pipeline();
        
        let transformed = pipeline.fit_transform(&data).unwrap();
        assert_eq!(transformed.shape(), &[4, 1]);
        
        let step_names = pipeline.step_names();
        assert_eq!(step_names.len(), 2);
        assert_eq!(step_names[0], "scaler");
        assert_eq!(step_names[1], "normalizer");
    }
}
