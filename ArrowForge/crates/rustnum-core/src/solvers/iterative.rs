use crate::error::RustNumError;
use crate::sparse::storage::CsrMatrix;
use crate::BlasWrapper;
// use std::ops::{Add, Mul, Sub}; // 移除未使用的导入

fn dot(x: &[f64], y: &[f64]) -> f64 {
    let wrapper = BlasWrapper::new();
    wrapper.dot(x, y).expect("BLAS dot operation failed")
}

fn norm2(x: &[f64]) -> f64 {
    dot(x, x).sqrt()
}

/// 迭代求解器配置
#[derive(Debug, Clone)]
pub struct IterativeSolverConfig {
    /// 最大迭代次数
    pub max_iter: usize,
    /// 收敛容差
    pub tolerance: f64,
    /// 是否打印迭代信息
    pub verbose: bool,
}

impl Default for IterativeSolverConfig {
    fn default() -> Self {
        Self {
            max_iter: 1000,
            tolerance: 1e-10,
            verbose: false,
        }
    }
}

/// 迭代求解器特征
pub trait IterativeSolver<T> {
    /// 共轭梯度法求解 Ax = b
    fn solve_cg(&self, b: &[T], config: Option<IterativeSolverConfig>) -> Result<Vec<T>, RustNumError>;
    
    /// GMRES求解 Ax = b
    fn solve_gmres(&self, b: &[T], restart: usize, config: Option<IterativeSolverConfig>) -> Result<Vec<T>, RustNumError>;
}

impl IterativeSolver<f64> for CsrMatrix<f64> {
    fn solve_cg(&self, b: &[f64], config: Option<IterativeSolverConfig>) -> Result<Vec<f64>, RustNumError> {
        let config = config.unwrap_or_default();
        
        if self.shape.0 != self.shape.1 {
            return Err(RustNumError::DimensionError(
                "CG法要求方阵".to_string()
            ));
        }
        
        if b.len() != self.shape.0 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.0],
                got: vec![b.len()],
            });
        }
        
        let n = self.shape.0;
        let mut x = vec![0.0; n]; // 初始解
        let mut r = b.to_vec(); // 初始残差 r = b - Ax
        
        // 计算初始残差
        let ax = self.mv_mul(&x)?;
        for i in 0..n {
            r[i] = b[i] - ax[i];
        }
        
        let mut p = r.clone(); // 初始搜索方向
        let mut r_norm = dot(&r, &r);
        let initial_r_norm = r_norm; // 定义 initial_r_norm

        for iter in 0..config.max_iter {
            // 计算 Ap
            let ap = self.mv_mul(&p)?;
            
            // 计算步长 alpha
            let pap = dot(&p, &ap);
            let alpha = r_norm / pap;
            
            // 更新解和残差
            for i in 0..n {
                x[i] += alpha * p[i];
                r[i] -= alpha * ap[i];
            }
            
            // 计算新残差范数
            let r_norm_new = dot(&r, &r);
            
            // 检查收敛性
            let relative_residual = (r_norm_new / initial_r_norm).sqrt();
            if config.verbose && iter % 100 == 0 {
                println!("CG迭代 {}: 相对残差 = {}", iter, relative_residual);
            }
            
            if relative_residual < config.tolerance {
                if config.verbose {
                    println!("CG收敛于迭代 {}", iter);
                }
                return Ok(x);
            }
            
            // 计算beta并更新搜索方向
            let beta = r_norm_new / r_norm;
            for i in 0..n {
                p[i] = r[i] + beta * p[i];
            }
            
            r_norm = r_norm_new;
        }
        
        Err(RustNumError::ConvergenceError {
            iterations: config.max_iter
        })
    }
    
    fn solve_gmres(&self, b: &[f64], restart: usize, config: Option<IterativeSolverConfig>) -> Result<Vec<f64>, RustNumError> {
        let config = config.unwrap_or_default();
        
        if self.shape.0 != self.shape.1 {
            return Err(RustNumError::DimensionError(
                "GMRES要求方阵".to_string()
            ));
        }
        
        if b.len() != self.shape.0 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.0],
                got: vec![b.len()],
            });
        }
        
        let n = self.shape.0;
        let mut x = vec![0.0; n]; // 初始解
        let mut r = b.to_vec();
        
        // 计算初始残差
        let ax = self.mv_mul(&x)?;
        for i in 0..n {
            r[i] = b[i] - ax[i];
        }
        
        let mut total_iter = 0;
        let b_norm = norm2(b);
        let mut r_norm = norm2(&r);

        
        // Arnoldi基向量
        let mut v = vec![vec![0.0; n]; restart + 1];
        // Hessenberg矩阵
        let mut h = vec![vec![0.0; restart]; restart + 1];
        
        while total_iter < config.max_iter {
            // 初始化第一个Arnoldi向量
            for i in 0..n {
                v[0][i] = r[i] / r_norm;
            }
            
            let mut g = vec![0.0; restart + 1];
            g[0] = r_norm;
            
            let mut j = 0;
            while j < restart && total_iter + j < config.max_iter {
                // Arnoldi过程
                let mut av = self.mv_mul(&v[j])?;
                
                for i in 0..=j {
                    h[i][j] = dot(&av, &v[i]);
                    for k in 0..n {
                        av[k] -= h[i][j] * v[i][k];
                    }
                }
                
                h[j+1][j] = norm2(&av);
                if h[j+1][j] != 0.0 {
                    for k in 0..n {
                        v[j+1][k] = av[k] / h[j+1][j];
                    }
                }
                
                // 应用Givens旋转消除h的次对角元
                for i in 0..j {
                    let (c, s) = givens(h[i][j], h[i+1][j]);
                    let (mut h_i_j, mut h_i1_j) = (h[i][j], h[i+1][j]);
                    apply_givens_rotation(&mut h_i_j, &mut h_i1_j, c, s);
                    h[i][j] = h_i_j;
                    h[i+1][j] = h_i1_j;
                    
                    let (mut g_i, mut g_i1) = (g[i], g[i+1]);
                    apply_givens_rotation(&mut g_i, &mut g_i1, c, s);
                    g[i] = g_i;
                    g[i+1] = g_i1;
                }
                
                // 计算新的Givens旋转
                let (c, s) = givens(h[j][j], h[j+1][j]);
                let (mut h_j_j, mut h_j1_j) = (h[j][j], h[j+1][j]);
                apply_givens_rotation(&mut h_j_j, &mut h_j1_j, c, s);
                h[j][j] = h_j_j;
                h[j+1][j] = h_j1_j;
                
                let (mut g_j, mut g_j1) = (g[j], g[j+1]);
                apply_givens_rotation(&mut g_j, &mut g_j1, c, s);
                g[j] = g_j;
                g[j+1] = g_j1;
                
                // 检查收敛性
                r_norm = g[j+1].abs();
                let relative_residual = r_norm / b_norm;
                
                if config.verbose && (j + 1) % 10 == 0 {
                    println!("GMRES迭代 {}: 相对残差 = {}", total_iter + j + 1, relative_residual);
                }
                
                if relative_residual < config.tolerance {
                    // 求解上三角系统
                    let y = solve_upper_triangular(&h[..j+1], &g[..j+1]);
                    
                    // 更新解
                    for i in 0..n {
                        for k in 0..=j {
                            x[i] += y[k] * v[k][i];
                        }
                    }
                    
                    if config.verbose {
                        println!("GMRES收敛于迭代 {}", total_iter + j + 1);
                    }
                    return Ok(x);
                }
                
                j += 1;
            }
            
            // 求解最小二乘问题更新解
            let y = solve_upper_triangular(&h[..restart], &g[..restart]);
            
            // 更新解
            for i in 0..n {
                for k in 0..restart {
                    x[i] += y[k] * v[k][i];
                }
            }
            
            // 计算新残差
            let ax = self.mv_mul(&x)?;
            for i in 0..n {
                r[i] = b[i] - ax[i];
            }
            r_norm = norm2(&r);
            
            total_iter += restart;
        }
        
        Err(RustNumError::ConvergenceError {
            iterations: config.max_iter
        })
    }
}

// 辅助函数


fn givens(a: f64, b: f64) -> (f64, f64) {
    if b == 0.0 {
        (1.0, 0.0)
    } else {
        let t = (a * a + b * b).sqrt();
        (a / t, -b / t)
    }
}

fn apply_givens_rotation(x: &mut f64, y: &mut f64, c: f64, s: f64) {
    let t1 = c * *x - s * *y;
    let t2 = s * *x + c * *y;
    *x = t1;
    *y = t2;
}

fn solve_upper_triangular(h: &[Vec<f64>], g: &[f64]) -> Vec<f64> {
    let n = h.len();
    let mut y = vec![0.0; n];
    
    for i in (0..n).rev() {
        let mut sum = g[i];
        for j in (i+1)..n {
            sum -= h[i][j] * y[j];
        }
        y[i] = sum / h[i][i];
    }
    
    y
}
