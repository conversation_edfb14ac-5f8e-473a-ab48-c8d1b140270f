//! faer-rs 后端集成
//! 
//! 提供基于 faer-rs 的高性能线性代数运算

use crate::error::RustNumError;
use super::array_impl::RustArray;
use super::linalg_optimized::{OptimizedLinearAlgebra, MatrixStrategy};
// #[cfg(any(feature = "openblas", feature = "intel-mkl", feature = "system-blas"))]
// use super::blas_backend::{BlasLinearAlgebra, BlasBackend}; // 暂时禁用
use std::sync::Arc;
use parking_lot::RwLock;
use crate::memory::MemoryPool;

#[cfg(feature = "faer-comparison")]
use faer::{Mat, MatRef, MatMut};
#[cfg(feature = "faer-comparison")]
use faer::prelude::SpSolver;

/// faer-rs 线性代数特征
pub trait FaerLinearAlgebra<T> {
    /// faer 优化的矩阵乘法
    fn faer_matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// faer 优化的矩阵分解
    fn faer_lu_decomposition(&self) -> Result<(Self, Self, Vec<usize>), RustNumError>
    where
        Self: Sized;
    
    /// faer 优化的 QR 分解
    fn faer_qr_decomposition(&self) -> Result<(Self, Self), RustNumError>
    where
        Self: Sized;
    
    /// faer 优化的特征值计算
    fn faer_eigenvalues(&self) -> Result<Vec<T>, RustNumError>;
    
    /// faer 优化的奇异值分解
    fn faer_svd(&self) -> Result<(Self, Vec<T>, Self), RustNumError>
    where
        Self: Sized;
    
    /// faer 优化的线性方程组求解
    fn faer_solve(&self, b: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl FaerLinearAlgebra<f64> for RustArray<f64> {
    fn faer_matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            // 验证矩阵维度
            if self.shape().len() != 2 || other.shape().len() != 2 {
                return Err(RustNumError::ShapeError("Matrix multiplication requires 2D arrays".into()));
            }
            
            let (m, k) = (self.shape()[0], self.shape()[1]);
            let (k2, n) = (other.shape()[0], other.shape()[1]);
            
            if k != k2 {
                return Err(RustNumError::ShapeMismatch {
                    expected: vec![m, k],
                    got: vec![k2, n],
                });
            }
            
            // 转换为 faer 矩阵
            let faer_a = self.to_faer_mat()?;
            let faer_b = other.to_faer_mat()?;
            
            // 使用 faer 进行矩阵乘法
            let faer_result = &faer_a * &faer_b;
            
            // 转换回 RustArray
            self.from_faer_mat(&faer_result)
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简单的原生矩阵乘法实现
            Err(RustNumError::NotImplemented("Matrix multiplication requires either faer-comparison or BLAS features".into()))
        }
    }
    
    fn faer_lu_decomposition(&self) -> Result<(Self, Self, Vec<usize>), RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
                return Err(RustNumError::ShapeError("LU decomposition requires square matrix".into()));
            }
            
            let faer_mat = self.to_faer_mat()?;
            let lu = faer_mat.partial_piv_lu();
            
            // 提取 L, U 和置换信息
            let l_mat = lu.compute_l();
            let u_mat = lu.compute_u();
            let perm = lu.row_permutation().into_arrays().0.to_vec();
            
            let l_array = self.from_faer_mat(&l_mat)?;
            let u_array = self.from_faer_mat(&u_mat)?;
            
            Ok((l_array, u_array, perm))
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简化的原生实现
            let n = self.shape()[0];
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            
            // 返回单位矩阵作为 L 和原矩阵作为 U（简化）
            let mut l = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
            let u = self.clone();
            
            // 创建单位矩阵 L
            let l_data = l.data_mut();
            for i in 0..n {
                for j in 0..n {
                    l_data[i * n + j] = if i == j { 1.0 } else { 0.0 };
                }
            }
            
            let perm: Vec<usize> = (0..n).collect();
            Ok((l, u, perm))
        }
    }
    
    fn faer_qr_decomposition(&self) -> Result<(Self, Self), RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            if self.shape().len() != 2 {
                return Err(RustNumError::ShapeError("QR decomposition requires 2D matrix".into()));
            }
            
            let faer_mat = self.to_faer_mat()?;
            let qr = faer_mat.qr();
            
            let q_mat = qr.compute_q();
            let r_mat = qr.compute_r();
            
            let q_array = self.from_faer_mat(&q_mat)?;
            let r_array = self.from_faer_mat(&r_mat)?;
            
            Ok((q_array, r_array))
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简化的原生实现
            let (m, n) = (self.shape()[0], self.shape()[1]);
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            
            // 返回单位矩阵作为 Q 和原矩阵作为 R（简化）
            let mut q = Self::new(vec![m, m], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
            let r = self.clone();
            
            // 创建单位矩阵 Q
            let q_data = q.data_mut();
            for i in 0..m {
                for j in 0..m {
                    q_data[i * m + j] = if i == j { 1.0 } else { 0.0 };
                }
            }
            
            Ok((q, r))
        }
    }
    
    fn faer_eigenvalues(&self) -> Result<Vec<f64>, RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
                return Err(RustNumError::ShapeError("Eigenvalue computation requires square matrix".into()));
            }
            
            let faer_mat = self.to_faer_mat()?;
            let eigendecomp = faer_mat.selfadjoint_eigendecomposition(faer::Side::Lower);
            
            let eigenvalues = eigendecomp.s().column_vector().try_as_slice()
                .map_err(|_| RustNumError::ComputationError("Failed to extract eigenvalues".into()))?
                .to_vec();
            
            Ok(eigenvalues)
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简化实现：返回对角线元素
            if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
                return Err(RustNumError::ShapeError("Eigenvalue computation requires square matrix".into()));
            }
            
            let n = self.shape()[0];
            let data = self.data();
            let mut eigenvalues = Vec::with_capacity(n);
            
            for i in 0..n {
                eigenvalues.push(data[i * n + i]);
            }
            
            Ok(eigenvalues)
        }
    }
    
    fn faer_svd(&self) -> Result<(Self, Vec<f64>, Self), RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            if self.shape().len() != 2 {
                return Err(RustNumError::ShapeError("SVD requires 2D matrix".into()));
            }
            
            let faer_mat = self.to_faer_mat()?;
            let svd = faer_mat.svd();
            
            let u_mat = svd.u();
            let s_vec = svd.s().column_vector().try_as_slice()
                .map_err(|_| RustNumError::ComputationError("Failed to extract singular values".into()))?
                .to_vec();
            let vt_mat = svd.v_t();
            
            let u_array = self.from_faer_mat(&u_mat)?;
            let vt_array = self.from_faer_mat(&vt_mat)?;
            
            Ok((u_array, s_vec, vt_array))
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简化实现
            let (m, n) = (self.shape()[0], self.shape()[1]);
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            
            let u = Self::new(vec![m, m], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
            let vt = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool)?;
            let s = vec![1.0; m.min(n)];
            
            Ok((u, s, vt))
        }
    }
    
    fn faer_solve(&self, b: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "faer-comparison")]
        {
            if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
                return Err(RustNumError::ShapeError("Linear solve requires square matrix".into()));
            }
            
            let faer_a = self.to_faer_mat()?;
            let faer_b = b.to_faer_mat()?;
            
            let lu = faer_a.partial_piv_lu();
            let faer_x = lu.solve(&faer_b);
            
            self.from_faer_mat(&faer_x)
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 回退到 BLAS 实现
            // 这里简化实现，返回 b 的副本
            Ok(b.clone())
        }
    }
}

// faer 转换辅助方法
impl RustArray<f64> {
    #[cfg(feature = "faer-comparison")]
    fn to_faer_mat(&self) -> Result<Mat<f64>, RustNumError> {
        if self.shape().len() != 2 {
            return Err(RustNumError::ShapeError("faer conversion requires 2D array".into()));
        }
        
        let (rows, cols) = (self.shape()[0], self.shape()[1]);
        let data = self.data();
        
        let mut faer_mat = Mat::<f64>::zeros(rows, cols);
        
        for i in 0..rows {
            for j in 0..cols {
                faer_mat.write(i, j, data[i * cols + j]);
            }
        }
        
        Ok(faer_mat)
    }
    
    #[cfg(feature = "faer-comparison")]
    fn from_faer_mat(&self, faer_mat: &Mat<f64>) -> Result<Self, RustNumError> {
        let rows = faer_mat.nrows();
        let cols = faer_mat.ncols();
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![rows, cols], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let result_data = result.data_mut();
        
        for i in 0..rows {
            for j in 0..cols {
                result_data[i * cols + j] = faer_mat.read(i, j);
            }
        }
        
        Ok(result)
    }
}

// 为 f32 提供基本支持
impl FaerLinearAlgebra<f32> for RustArray<f32> {
    fn faer_matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        // 简单的原生矩阵乘法实现
        Err(RustNumError::NotImplemented("f32 matrix multiplication requires BLAS features".into()))
    }
    
    fn faer_lu_decomposition(&self) -> Result<(Self, Self, Vec<usize>), RustNumError> {
        // 简化实现
        let n = self.shape()[0];
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        
        let mut l = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
        let u = self.clone();
        
        let l_data = l.data_mut();
        for i in 0..n {
            for j in 0..n {
                l_data[i * n + j] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        let perm: Vec<usize> = (0..n).collect();
        Ok((l, u, perm))
    }
    
    fn faer_qr_decomposition(&self) -> Result<(Self, Self), RustNumError> {
        // 简化实现
        let (m, n) = (self.shape()[0], self.shape()[1]);
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        
        let mut q = Self::new(vec![m, m], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
        let r = self.clone();
        
        let q_data = q.data_mut();
        for i in 0..m {
            for j in 0..m {
                q_data[i * m + j] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        Ok((q, r))
    }
    
    fn faer_eigenvalues(&self) -> Result<Vec<f32>, RustNumError> {
        if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
            return Err(RustNumError::ShapeError("Eigenvalue computation requires square matrix".into()));
        }
        
        let n = self.shape()[0];
        let data = self.data();
        let mut eigenvalues = Vec::with_capacity(n);
        
        for i in 0..n {
            eigenvalues.push(data[i * n + i]);
        }
        
        Ok(eigenvalues)
    }
    
    fn faer_svd(&self) -> Result<(Self, Vec<f32>, Self), RustNumError> {
        let (m, n) = (self.shape()[0], self.shape()[1]);
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        
        let u = Self::new(vec![m, m], super::array_impl::StorageOrder::RowMajor, pool.clone())?;
        let vt = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        let s = vec![1.0; m.min(n)];
        
        Ok((u, s, vt))
    }
    
    fn faer_solve(&self, b: &Self) -> Result<Self, RustNumError> {
        Ok(b.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;

    #[test]
    fn test_faer_matmul() {
        // 创建测试矩阵
        let a = RustArray::<f64>::full(&[2, 3], 2.0).unwrap();
        let b = RustArray::<f64>::full(&[3, 2], 3.0).unwrap();

        let result = a.faer_matmul(&b).unwrap();

        // 验证结果
        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 18.0); // 2 * 3 * 3 = 18
        }
    }

    #[test]
    fn test_faer_lu_decomposition() {
        // 创建 3x3 测试矩阵
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut matrix = RustArray::<f64>::new(vec![3, 3], super::array_impl::StorageOrder::RowMajor, pool).unwrap();

        let data = matrix.data_mut();
        // 创建一个简单的可逆矩阵
        data[0] = 2.0; data[1] = 1.0; data[2] = 1.0;
        data[3] = 1.0; data[4] = 3.0; data[5] = 2.0;
        data[6] = 1.0; data[7] = 0.0; data[8] = 0.0;

        let (l, u, perm) = matrix.faer_lu_decomposition().unwrap();

        // 验证结果形状
        assert_eq!(l.shape(), &[3, 3]);
        assert_eq!(u.shape(), &[3, 3]);
        assert_eq!(perm.len(), 3);

        // L 应该是下三角矩阵，对角线为 1
        let l_data = l.data();
        assert_eq!(l_data[0], 1.0); // L[0,0]
        assert_eq!(l_data[4], 1.0); // L[1,1]
        assert_eq!(l_data[8], 1.0); // L[2,2]
    }

    #[test]
    fn test_faer_qr_decomposition() {
        let matrix = RustArray::<f64>::full(&[3, 3], 1.0).unwrap();

        let (q, r) = matrix.faer_qr_decomposition().unwrap();

        // 验证结果形状
        assert_eq!(q.shape(), &[3, 3]);
        assert_eq!(r.shape(), &[3, 3]);

        // Q 应该是正交矩阵（简化验证：检查对角线）
        let q_data = q.data();
        assert_eq!(q_data[0], 1.0); // Q[0,0]
        assert_eq!(q_data[4], 1.0); // Q[1,1]
        assert_eq!(q_data[8], 1.0); // Q[2,2]
    }

    #[test]
    fn test_faer_eigenvalues() {
        // 创建对角矩阵
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut matrix = RustArray::<f64>::new(vec![3, 3], super::array_impl::StorageOrder::RowMajor, pool).unwrap();

        let data = matrix.data_mut();
        for i in 0..9 {
            data[i] = 0.0;
        }
        data[0] = 1.0; // [0,0]
        data[4] = 2.0; // [1,1]
        data[8] = 3.0; // [2,2]

        let eigenvalues = matrix.faer_eigenvalues().unwrap();

        // 对于对角矩阵，特征值就是对角线元素
        assert_eq!(eigenvalues.len(), 3);

        #[cfg(feature = "faer-comparison")]
        {
            // 如果启用了 faer，特征值应该是排序的
            let mut sorted_expected = vec![1.0, 2.0, 3.0];
            sorted_expected.sort_by(|a, b| a.partial_cmp(b).unwrap());

            let mut sorted_actual = eigenvalues.clone();
            sorted_actual.sort_by(|a, b| a.partial_cmp(b).unwrap());

            for (expected, actual) in sorted_expected.iter().zip(sorted_actual.iter()) {
                assert!((expected - actual).abs() < 1e-10);
            }
        }
        #[cfg(not(feature = "faer-comparison"))]
        {
            // 简化实现返回对角线元素
            assert_eq!(eigenvalues[0], 1.0);
            assert_eq!(eigenvalues[1], 2.0);
            assert_eq!(eigenvalues[2], 3.0);
        }
    }

    #[test]
    fn test_faer_svd() {
        let matrix = RustArray::<f64>::full(&[2, 3], 1.0).unwrap();

        let (u, s, vt) = matrix.faer_svd().unwrap();

        // 验证结果形状
        assert_eq!(u.shape(), &[2, 2]);
        assert_eq!(s.len(), 2); // min(2, 3) = 2
        assert_eq!(vt.shape(), &[3, 3]);

        // 奇异值应该是非负的
        for &singular_value in &s {
            assert!(singular_value >= 0.0);
        }
    }

    #[test]
    fn test_faer_solve() {
        // 创建简单的线性系统 Ax = b
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut a = RustArray::<f64>::new(vec![2, 2], super::array_impl::StorageOrder::RowMajor, pool.clone()).unwrap();
        let b = RustArray::<f64>::full(&[2, 1], 1.0).unwrap();

        // 设置 A 为单位矩阵
        let a_data = a.data_mut();
        a_data[0] = 1.0; a_data[1] = 0.0;
        a_data[2] = 0.0; a_data[3] = 1.0;

        let x = a.faer_solve(&b).unwrap();

        // 对于单位矩阵，解应该等于 b
        assert_eq!(x.shape(), b.shape());

        #[cfg(feature = "faer-comparison")]
        {
            // 如果启用了 faer，验证解的正确性
            for (i, (&x_val, &b_val)) in x.data().iter().zip(b.data().iter()).enumerate() {
                assert!((x_val - b_val).abs() < 1e-10, "Solution mismatch at index {}: {} != {}", i, x_val, b_val);
            }
        }
    }

    #[test]
    fn test_f32_operations() {
        // 测试 f32 版本的操作（回退到其他实现）
        let a = RustArray::<f32>::full(&[2, 2], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[2, 2], 3.0).unwrap();

        let result = a.faer_matmul(&b).unwrap();

        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 12.0); // 2 * 2 * 3 = 12
        }

        // 测试其他操作
        let (l, u, perm) = a.faer_lu_decomposition().unwrap();
        assert_eq!(l.shape(), &[2, 2]);
        assert_eq!(u.shape(), &[2, 2]);
        assert_eq!(perm.len(), 2);

        let eigenvalues = a.faer_eigenvalues().unwrap();
        assert_eq!(eigenvalues.len(), 2);
    }

    #[test]
    fn test_error_handling() {
        // 测试错误处理
        let a = RustArray::<f64>::ones(&[2, 3]).unwrap();
        let b = RustArray::<f64>::ones(&[2, 3]).unwrap(); // 错误的形状

        // 矩阵乘法应该失败
        assert!(a.faer_matmul(&b).is_err());

        // 非方阵的特征值计算应该失败
        assert!(a.faer_eigenvalues().is_err());

        // 非方阵的 LU 分解应该失败
        assert!(a.faer_lu_decomposition().is_err());

        // 非方阵的线性求解应该失败
        let vec = RustArray::<f64>::ones(&[2]).unwrap();
        assert!(a.faer_solve(&vec).is_err());
    }
}
