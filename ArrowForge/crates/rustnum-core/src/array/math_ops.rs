//! 数组数学运算
//! 
//! 提供 NumPy 兼容的数学运算 API

use crate::error::RustNumError;
use super::array_impl::RustArray;
use std::ops::{Add, Sub, Mul, Div};

/// 数学运算特征
pub trait ArrayMath<T> {
    /// 元素级加法
    fn add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 元素级减法
    fn sub(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 元素级乘法
    fn mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 元素级除法
    fn div(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 标量加法
    fn add_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 标量乘法
    fn mul_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 平方根
    fn sqrt(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 指数函数
    fn exp(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 自然对数
    fn ln(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 幂运算
    fn pow(&self, exponent: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl ArrayMath<f64> for RustArray<f64> {
    fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a += b;
        }
        
        Ok(result)
    }
    
    fn sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a -= b;
        }
        
        Ok(result)
    }
    
    fn mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a *= b;
        }
        
        Ok(result)
    }
    
    fn div(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            if b == 0.0 {
                return Err(RustNumError::DivisionByZero);
            }
            *a /= b;
        }
        
        Ok(result)
    }
    
    fn add_scalar(&self, scalar: f64) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a += scalar;
        }
        
        Ok(result)
    }
    
    fn mul_scalar(&self, scalar: f64) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a *= scalar;
        }
        
        Ok(result)
    }
    
    fn sqrt(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            if *a < 0.0 {
                return Err(RustNumError::InvalidOperation(
                    "Cannot take square root of negative number".into()
                ));
            }
            *a = a.sqrt();
        }
        
        Ok(result)
    }
    
    fn exp(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a = a.exp();
        }
        
        Ok(result)
    }
    
    fn ln(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            if *a <= 0.0 {
                return Err(RustNumError::InvalidOperation(
                    "Cannot take logarithm of non-positive number".into()
                ));
            }
            *a = a.ln();
        }
        
        Ok(result)
    }
    
    fn pow(&self, exponent: f64) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a = a.powf(exponent);
        }
        
        Ok(result)
    }
}

impl ArrayMath<f32> for RustArray<f32> {
    fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a += b;
        }
        
        Ok(result)
    }
    
    fn sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a -= b;
        }
        
        Ok(result)
    }
    
    fn mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            *a *= b;
        }
        
        Ok(result)
    }
    
    fn div(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let other_data = other.data();
        
        for (a, &b) in result_data.iter_mut().zip(other_data.iter()) {
            if b == 0.0 {
                return Err(RustNumError::DivisionByZero);
            }
            *a /= b;
        }
        
        Ok(result)
    }
    
    fn add_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a += scalar;
        }
        
        Ok(result)
    }
    
    fn mul_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a *= scalar;
        }
        
        Ok(result)
    }
    
    fn sqrt(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            if *a < 0.0 {
                return Err(RustNumError::InvalidOperation(
                    "Cannot take square root of negative number".into()
                ));
            }
            *a = a.sqrt();
        }
        
        Ok(result)
    }
    
    fn exp(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a = a.exp();
        }
        
        Ok(result)
    }
    
    fn ln(&self) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            if *a <= 0.0 {
                return Err(RustNumError::InvalidOperation(
                    "Cannot take logarithm of non-positive number".into()
                ));
            }
            *a = a.ln();
        }
        
        Ok(result)
    }
    
    fn pow(&self, exponent: f32) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        
        for a in result_data.iter_mut() {
            *a = a.powf(exponent);
        }
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;

    #[test]
    fn test_array_addition() {
        let a = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let b = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let result = a.add(&b).unwrap();

        for &val in result.data() {
            assert_eq!(val, 2.0);
        }
    }

    #[test]
    fn test_array_subtraction() {
        let a = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let b = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let result = a.sub(&b).unwrap();

        for &val in result.data() {
            assert_eq!(val, 0.0);
        }
    }

    #[test]
    fn test_array_multiplication() {
        let a = RustArray::<f64>::full(&[2, 2], 3.0).unwrap();
        let b = RustArray::<f64>::full(&[2, 2], 2.0).unwrap();
        let result = a.mul(&b).unwrap();

        for &val in result.data() {
            assert_eq!(val, 6.0);
        }
    }

    #[test]
    fn test_array_division() {
        let a = RustArray::<f64>::full(&[2, 2], 6.0).unwrap();
        let b = RustArray::<f64>::full(&[2, 2], 2.0).unwrap();
        let result = a.div(&b).unwrap();

        for &val in result.data() {
            assert_eq!(val, 3.0);
        }
    }

    #[test]
    fn test_scalar_operations() {
        let a = RustArray::<f64>::ones(&[2, 2]).unwrap();

        // 标量加法
        let result = a.add_scalar(5.0).unwrap();
        for &val in result.data() {
            assert_eq!(val, 6.0);
        }

        // 标量乘法
        let result = a.mul_scalar(3.0).unwrap();
        for &val in result.data() {
            assert_eq!(val, 3.0);
        }
    }

    #[test]
    fn test_math_functions() {
        let a = RustArray::<f64>::full(&[2, 2], 4.0).unwrap();

        // 平方根
        let result = a.sqrt().unwrap();
        for &val in result.data() {
            assert_eq!(val, 2.0);
        }

        // 幂运算
        let a = RustArray::<f64>::full(&[2, 2], 2.0).unwrap();
        let result = a.pow(3.0).unwrap();
        for &val in result.data() {
            assert_eq!(val, 8.0);
        }
    }

    #[test]
    fn test_shape_mismatch_error() {
        let a = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let b = RustArray::<f64>::ones(&[3, 3]).unwrap();

        assert!(a.add(&b).is_err());
        assert!(a.sub(&b).is_err());
        assert!(a.mul(&b).is_err());
        assert!(a.div(&b).is_err());
    }

    #[test]
    fn test_division_by_zero() {
        let a = RustArray::<f64>::ones(&[2, 2]).unwrap();
        let b = RustArray::<f64>::zeros(&[2, 2]).unwrap();

        assert!(a.div(&b).is_err());
    }

    #[test]
    fn test_sqrt_negative_error() {
        let a = RustArray::<f64>::full(&[2, 2], -1.0).unwrap();
        assert!(a.sqrt().is_err());
    }

    #[test]
    fn test_ln_invalid_error() {
        let a = RustArray::<f64>::zeros(&[2, 2]).unwrap();
        assert!(a.ln().is_err());

        let b = RustArray::<f64>::full(&[2, 2], -1.0).unwrap();
        assert!(b.ln().is_err());
    }
}
