use std::ops::{Add, Sub, Mul, Div};
use crate::error::RustNumError;
use super::array_impl::{RustArray, ArrayView, StorageOrder};
use crate::memory::MemoryPool;
use std::sync::Arc;
use parking_lot::RwLock;

/// 基本数组运算特征
pub trait ArrayOps<T> {
    /// 元素级加法
    fn add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 元素级乘法
    fn mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 矩阵乘法
    fn matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 求和
    fn sum(&self) -> T;
    
    /// 求平均值
    fn mean(&self) -> T
    where
        T: Add<Output = T> + Div<Output = T> + From<usize>;
}

impl<T> ArrayOps<T> for RustArray<T>
where
    T: Copy + Add<Output = T> + Mul<Output = T>,
{
    fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(self.shape().to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 简单的元素级加法实现
        let self_data = self.data();
        let other_data = other.data();
        let result_data = result.data_mut();
        
        for i in 0..self_data.len() {
            result_data[i] = self_data[i] + other_data[i];
        }
        
        Ok(result)
    }
    
    fn mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(self.shape().to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 简单的元素级乘法实现
        let self_data = self.data();
        let other_data = other.data();
        let result_data = result.data_mut();
        
        for i in 0..self_data.len() {
            result_data[i] = self_data[i] * other_data[i];
        }
        
        Ok(result)
    }
    
    fn matmul(&self, _other: &Self) -> Result<Self, RustNumError> {
        // TODO: 实现矩阵乘法
        unimplemented!("Matrix multiplication not implemented yet");
    }
    
    fn sum(&self) -> T {
        let data = self.data();
        let mut result = data[0];
        for i in 1..data.len() {
            result = result + data[i];
        }
        result
    }
    
    fn mean(&self) -> T
    where
        T: Add<Output = T> + Div<Output = T> + From<usize>,
    {
        let sum_val = self.sum();
        let len = T::from(self.data().len());
        sum_val / len
    }
}

/// 数组索引特征
pub trait ArrayIndex<T> {
    /// 获取元素引用
    fn get(&self, indices: &[usize]) -> Option<&T>;
    
    /// 获取可变元素引用
    fn get_mut(&mut self, indices: &[usize]) -> Option<&mut T>;
    
    /// 设置元素值
    fn set(&mut self, indices: &[usize], value: T) -> Result<(), RustNumError>;
}

// TODO: 实现ArrayIndex trait
// impl<T> ArrayIndex<T> for RustArray<T> {
//     // 实现将在后续版本中添加
// }

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::convenience::*;
    
    #[test]
    fn test_array_add() {
        let arr1 = ones_f64(&[2, 2]).unwrap();
        let arr2 = ones_f64(&[2, 2]).unwrap();
        let result = arr1.add(&arr2).unwrap();
        assert_eq!(result.shape(), &[2, 2]);
        
        // 验证结果：1 + 1 = 2
        for &val in result.data() {
            assert_eq!(val, 2.0);
        }
    }
    
    #[test]
    fn test_array_mul() {
        let arr1 = full_f64(&[2, 2], 3.0).unwrap();
        let arr2 = full_f64(&[2, 2], 2.0).unwrap();
        let result = arr1.mul(&arr2).unwrap();
        assert_eq!(result.shape(), &[2, 2]);
        
        // 验证结果：3 * 2 = 6
        for &val in result.data() {
            assert_eq!(val, 6.0);
        }
    }
    
    #[test]
    fn test_array_sum() {
        let arr = arange_f64(1.0, 5.0, 1.0).unwrap(); // [1, 2, 3, 4]
        let sum = arr.sum();
        assert_eq!(sum, 10.0); // 1 + 2 + 3 + 4 = 10
    }
    
    #[test]
    fn test_array_mean() {
        let arr = arange_f64(1.0, 5.0, 1.0).unwrap(); // [1, 2, 3, 4]
        let mean = arr.mean();
        assert_eq!(mean, 2.5); // 10 / 4 = 2.5
    }
}
