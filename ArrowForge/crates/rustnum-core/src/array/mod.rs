//! 数组模块
// use std::ops::{Deref, DerefMut}; // 暂时未使用

pub mod array_impl;
pub mod creation;
pub mod math_ops;
pub mod simd_math;
pub mod bench_simd;
pub mod linalg_optimized;
pub mod lapack;
pub mod svd;
pub mod inverse;
pub mod simple_array;
// #[cfg(any(feature = "openblas", feature = "intel-mkl", feature = "system-blas"))]
// pub mod blas_backend; // 暂时禁用
#[cfg(feature = "faer-comparison")]
pub mod faer_backend;
#[cfg(feature = "arrow")]
pub mod arrow_integration;
// pub mod ops; // 暂时禁用，等待修复

pub use array_impl::{RustArray, Buffer, Layout, StorageOrder, ArrayView};
pub use simple_array::SimpleArray;

// 类型别名，用于兼容性
pub type Array2D<T> = RustArray<T>;
pub use creation::{ArrayCreation, convenience};
pub use math_ops::ArrayMath;
pub use simd_math::SimdArrayMath;
pub use linalg_optimized::{OptimizedLinearAlgebra, MatrixStrategy};
pub use lapack::LapackOps;
pub use svd::{SvdOps, SvdResult, SvdConfig};
pub use inverse::{InverseOps, InverseMethod, InverseConfig};
// #[cfg(any(feature = "openblas", feature = "intel-mkl", feature = "system-blas"))]
// pub use blas_backend::{BlasLinearAlgebra, BlasBackend, BlasConfig, initialize_blas}; // 暂时禁用
#[cfg(feature = "faer-comparison")]
pub use faer_backend::FaerLinearAlgebra;
#[cfg(feature = "arrow")]
pub use arrow_integration::{ArrowIntegration, ArrowTypeMapping, ArrowCompute};
// pub use ops::ArrayOps; // 暂时禁用，等待修复
