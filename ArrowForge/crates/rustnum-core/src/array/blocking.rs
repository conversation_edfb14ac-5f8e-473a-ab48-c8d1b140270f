use std::cmp;
use cache_size::l1_cache_size;

/// 自适应分块策略
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct BlockingStrategy {
    /// L1缓存大小
    l1_cache_size: usize,
    /// 每个元素的字节数
    element_size: usize,
    /// 最小分块大小
    min_block_size: usize,
    /// 最大分块大小
    max_block_size: usize,
}

impl Default for BlockingStrategy {
    fn default() -> Self {
        Self {
            l1_cache_size: l1_cache_size().unwrap_or(32 * 1024), // 默认32KB
            element_size: std::mem::size_of::<f64>(),
            min_block_size: 32,
            max_block_size: 256,
        }
    }
}

impl BlockingStrategy {
    /// 创建新的分块策略
    pub fn new(element_size: usize) -> Self {
        Self {
            element_size,
            ..Default::default()
        }
    }
    
    /// 计算最优分块大小
    pub fn optimal_block_size(&self, m: usize, n: usize, k: usize) -> (usize, usize, usize) {
        // 考虑三个维度的矩阵乘法 C(m,n) = A(m,k) * B(k,n)
        
        // 计算理论最优分块大小（基于缓存大小）
        let cache_elements = self.l1_cache_size / self.element_size;
        let mut block_size = (cache_elements / 3).sqrt(); // 为三个块分配相等空间
        
        // 限制在合理范围内
        block_size = cmp::min(
            cmp::max(block_size, self.min_block_size),
            self.max_block_size
        );
        
        // 调整分块大小以适应实际矩阵维度
        let block_m = cmp::min(block_size, m);
        let block_n = cmp::min(block_size, n);
        let block_k = cmp::min(block_size, k);
        
        (block_m, block_n, block_k)
    }
    
    /// 根据SIMD宽度对齐分块大小
    pub fn align_block_size(&self, size: usize, simd_width: usize) -> usize {
        let elements_per_vector = simd_width / self.element_size;
        ((size + elements_per_vector - 1) / elements_per_vector) * elements_per_vector
    }
    
    /// 生成分块迭代器
    pub fn block_iter(&self, m: usize, n: usize, k: usize) 
        -> impl Iterator<Item = ((usize, usize), (usize, usize), (usize, usize))>
    {
        let (bm, bn, bk) = self.optimal_block_size(m, n, k);
        
        BlockIterator {
            m, n, k,
            bm, bn, bk,
            current: (0, 0, 0),
        }
    }
}

/// 分块迭代器
struct BlockIterator {
    m: usize,
    n: usize,
    k: usize,
    bm: usize,
    bn: usize,
    bk: usize,
    current: (usize, usize, usize),
}

impl Iterator for BlockIterator {
    type Item = ((usize, usize), (usize, usize), (usize, usize));
    
    fn next(&mut self) -> Option<Self::Item> {
        let (i, j, l) = self.current;
        
        if i >= self.m {
            return None;
        }
        
        let i_end = (i + self.bm).min(self.m);
        let j_end = (j + self.bn).min(self.n);
        let l_end = (l + self.bk).min(self.k);
        
        let result = (
            (i, i_end),
            (j, j_end),
            (l, l_end)
        );
        
        // 更新下一个块的位置
        if l_end < self.k {
            self.current.2 = l_end;
        } else if j_end < self.n {
            self.current.1 = j_end;
            self.current.2 = 0;
        } else {
            self.current.0 = i_end;
            self.current.1 = 0;
            self.current.2 = 0;
        }
        
        Some(result)
    }
}
