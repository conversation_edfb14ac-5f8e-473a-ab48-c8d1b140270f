use crate::error::RustNumError;
use crate::parallel::ParallelManager;
use crate::RustArray;

#[cfg(feature = "parallel")]
use rayon::prelude::*;


/// 并行矩阵运算特征
pub trait ParallelArrayOps<T: Send + Sync> {
    /// 并行矩阵乘法
    fn parallel_matmul(&self, other: &RustArray<T>) -> Result<RustArray<T>, RustNumError>;
    
    /// 并行元素级运算
    fn parallel_map<F>(&self, f: F) -> RustArray<T>
    where
        F: Fn(T) -> T + Send + Sync;
        
    /// 并行规约操作
    fn parallel_reduce<F>(&self, init: T, f: F) -> T
    where
        F: Fn(T, T) -> T + Send + Sync;
}

impl ParallelArrayOps<f64> for RustArray<f64> {
    fn parallel_matmul(&self, other: &RustArray<f64>) -> Result<RustArray<f64>, RustNumError> {
        if self.ndim() != 2 || other.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "矩阵乘法要求输入为2D矩阵".to_string()
            ));
        }
        
        let (m, k1) = (self.shape()[0], self.shape()[1]);
        let (k2, n) = (other.shape()[0], other.shape()[1]);
        
        if k1 != k2 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![k1],
                got: vec![k2],
            });
        }
        
        // 判断是否需要并行计算
        if !ParallelManager::should_parallelize(m * n) {
            return Ok(self.matmul(other));
        }
        
        let mut result = RustArray::zeros((m, n));
        let block_size = ParallelManager::get_config().min_parallel_size.sqrt();
        
        // 分块并行计算
        #[cfg(feature = "parallel")]
        ParallelManager::execute(|| {
            // 按行分块
            (0..m).into_par_iter()
                .step_by(block_size)
                .for_each(|i| {
                    let end_i = (i + block_size).min(m);
                    
                    // 按列分块
                    for j in (0..n).step_by(block_size) {
                        let end_j = (j + block_size).min(n);
                        
                        // 计算当前块
                        for ii in i..end_i {
                            for jj in j..end_j {
                                let mut sum = 0.0;
                                for kk in 0..k1 {
                                    sum += self[[ii, kk]] * other[[kk, jj]];
                                }
                                result[[ii, jj]] = sum;
                            }
                        }
                    }
                });
        });
        
        #[cfg(not(feature = "parallel"))]
        {
            // 按行分块
            for i in (0..m).step_by(block_size) {
                let end_i = (i + block_size).min(m);
                
                // 按列分块
                for j in (0..n).step_by(block_size) {
                    let end_j = (j + block_size).min(n);
                    
                    // 计算当前块
                    for ii in i..end_i {
                        for jj in j..end_j {
                            let mut sum = 0.0;
                            for kk in 0..k1 {
                                sum += self[[ii, kk]] * other[[kk, jj]];
                            }
                            result[[ii, jj]] = sum;
                        }
                    }
                }
            }
        }
        
        Ok(result)
    }
    
    fn parallel_map<F>(&self, f: F) -> RustArray<f64>
    where
        F: Fn(f64) -> f64 + Send + Sync,
    {
        if !ParallelManager::should_parallelize(self.len()) {
            return self.map(f);
        }
        
        let mut result = RustArray::zeros(self.shape());
        
        #[cfg(feature = "parallel")]
        ParallelManager::execute(|| {
            result.as_mut_slice()
                .par_iter_mut()
                .zip(self.as_slice().par_iter())
                .for_each(|(out, &x)| {
                    *out = f(x);
                });
        });
        
        #[cfg(not(feature = "parallel"))]
        {
            for (out, &x) in result.as_mut_slice().iter_mut().zip(self.as_slice().iter()) {
                *out = f(x);
            }
        }
        
        result
    }
    
    fn parallel_reduce<F>(&self, init: f64, f: F) -> f64
    where
        F: Fn(f64, f64) -> f64 + Send + Sync,
    {
        if !ParallelManager::should_parallelize(self.len()) {
            return self.as_slice().iter().fold(init, |acc, &x| f(acc, x));
        }
        
        #[cfg(feature = "parallel")]
        return ParallelManager::execute(|| {
            self.as_slice()
                .par_iter()
                .fold(|| init, |acc, &x| f(acc, x))
                .reduce(|| init, f)
        });
        
        #[cfg(not(feature = "parallel"))]
        return self.as_slice().iter().fold(init, |acc, &x| f(acc, x));
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;
    
    #[test]
    fn test_parallel_matmul() {
        // 创建大矩阵进行测试
        let n = 1000;
        let a = RustArray::from_fn((n, n), |i, j| (i + j) as f64);
        let b = RustArray::from_fn((n, n), |i, j| (i * j) as f64);
        
        // 比较并行和串行结果
        let parallel_result = a.parallel_matmul(&b).unwrap();
        let serial_result = a.matmul(&b);
        
        // 验证结果一致性
        assert!(parallel_result.allclose(&serial_result, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_parallel_map() {
        let n = 1000000;
        let a = RustArray::from_fn((n,), |i| i as f64);
        
        // 测试并行map
        let result = a.parallel_map(|x| x * x);
        
        // 验证结果
        for i in 0..n {
            assert_relative_eq!(result[i], (i * i) as f64, epsilon = 1e-10);
        }
    }
    
    #[test]
    fn test_parallel_reduce() {
        let n = 1000000;
        let a = RustArray::from_fn((n,), |i| i as f64);
        
        // 计算和
        let sum = a.parallel_reduce(0.0, |acc, x| acc + x);
        let expected = (n * (n - 1) / 2) as f64;
        
        assert_relative_eq!(sum, expected, epsilon = 1e-10);
    }
}
