//! 数组创建函数
//! 
//! 提供 NumPy 兼容的数组创建 API

use std::sync::Arc;
use parking_lot::RwLock;
use crate::error::RustNumError;
use crate::memory::MemoryPool;
use super::array_impl::{RustArray, StorageOrder};

/// 数组创建特征
pub trait ArrayCreation<T> {
    /// 创建全零数组
    fn zeros(shape: &[usize]) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 创建全一数组
    fn ones(shape: &[usize]) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 创建填充指定值的数组
    fn full(shape: &[usize], value: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 创建单位矩阵
    fn eye(n: usize) -> Result<Self, RustNumError>
    where
        Self: Sized;

    /// 从向量创建数组
    fn from_vec(data: Vec<T>, shape: &[usize]) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 创建等差数列数组
    fn arange(start: T, stop: T, step: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 创建线性等分数组
    fn linspace(start: T, stop: T, num: usize) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl ArrayCreation<f64> for RustArray<f64> {
    fn zeros(shape: &[usize]) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 使用零填充
        unsafe {
            let ptr = array.data_mut().as_mut_ptr();
            let len = array.len();
            std::ptr::write_bytes(ptr, 0, len * std::mem::size_of::<f64>());
        }
        
        Ok(array)
    }
    
    fn ones(shape: &[usize]) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 填充为 1.0
        for item in array.data_mut().iter_mut() {
            *item = 1.0;
        }
        
        Ok(array)
    }
    
    fn full(shape: &[usize], value: f64) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 填充指定值
        for item in array.data_mut().iter_mut() {
            *item = value;
        }
        
        Ok(array)
    }
    
    fn eye(n: usize) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![n, n], StorageOrder::RowMajor, pool)?;
        
        // 创建单位矩阵
        let data = array.data_mut();
        for i in 0..n {
            for j in 0..n {
                let idx = i * n + j;
                data[idx] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        Ok(array)
    }
    
    fn arange(start: f64, stop: f64, step: f64) -> Result<Self, RustNumError> {
        if step == 0.0 {
            return Err(RustNumError::ValueError("Step cannot be zero".to_string()));
        }
        
        if (step > 0.0 && start >= stop) || (step < 0.0 && start <= stop) {
            return Err(RustNumError::ValueError("Invalid range for given step".to_string()));
        }
        
        let num_elements = ((stop - start) / step).abs().ceil() as usize;
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![num_elements], StorageOrder::RowMajor, pool)?;
        
        let data = array.data_mut();
        for (i, item) in data.iter_mut().enumerate() {
            *item = start + (i as f64) * step;
        }
        
        Ok(array)
    }
    
    fn linspace(start: f64, stop: f64, num: usize) -> Result<Self, RustNumError> {
        if num == 0 {
            return Err(RustNumError::ValueError("Number of samples must be positive".to_string()));
        }
        
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![num], StorageOrder::RowMajor, pool)?;
        
        let data = array.data_mut();
        if num == 1 {
            data[0] = start;
        } else {
            let step = (stop - start) / ((num - 1) as f64);
            for (i, item) in data.iter_mut().enumerate() {
                *item = start + (i as f64) * step;
            }
        }
        
        Ok(array)
    }

    fn from_vec(data: Vec<f64>, shape: &[usize]) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        if data.len() != total_elements {
            return Err(RustNumError::ShapeError(
                format!("Data length {} doesn't match shape {:?} (expected {})",
                       data.len(), shape, total_elements)
            ));
        }

        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;

        let array_data = array.data_mut();
        array_data.copy_from_slice(&data);

        Ok(array)
    }
}

impl ArrayCreation<f32> for RustArray<f32> {
    fn zeros(shape: &[usize]) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 使用零填充
        unsafe {
            let ptr = array.data_mut().as_mut_ptr();
            let len = array.len();
            std::ptr::write_bytes(ptr, 0, len * std::mem::size_of::<f32>());
        }
        
        Ok(array)
    }
    
    fn ones(shape: &[usize]) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 填充为 1.0
        for item in array.data_mut().iter_mut() {
            *item = 1.0;
        }
        
        Ok(array)
    }
    
    fn full(shape: &[usize], value: f32) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;
        
        // 填充指定值
        for item in array.data_mut().iter_mut() {
            *item = value;
        }
        
        Ok(array)
    }
    
    fn eye(n: usize) -> Result<Self, RustNumError> {
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![n, n], StorageOrder::RowMajor, pool)?;
        
        // 创建单位矩阵
        let data = array.data_mut();
        for i in 0..n {
            for j in 0..n {
                let idx = i * n + j;
                data[idx] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        Ok(array)
    }
    
    fn arange(start: f32, stop: f32, step: f32) -> Result<Self, RustNumError> {
        if step == 0.0 {
            return Err(RustNumError::ValueError("Step cannot be zero".to_string()));
        }
        
        if (step > 0.0 && start >= stop) || (step < 0.0 && start <= stop) {
            return Err(RustNumError::ValueError("Invalid range for given step".to_string()));
        }
        
        let num_elements = ((stop - start) / step).abs().ceil() as usize;
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![num_elements], StorageOrder::RowMajor, pool)?;
        
        let data = array.data_mut();
        for (i, item) in data.iter_mut().enumerate() {
            *item = start + (i as f32) * step;
        }
        
        Ok(array)
    }
    
    fn linspace(start: f32, stop: f32, num: usize) -> Result<Self, RustNumError> {
        if num == 0 {
            return Err(RustNumError::ValueError("Number of samples must be positive".to_string()));
        }
        
        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(vec![num], StorageOrder::RowMajor, pool)?;
        
        let data = array.data_mut();
        if num == 1 {
            data[0] = start;
        } else {
            let step = (stop - start) / ((num - 1) as f32);
            for (i, item) in data.iter_mut().enumerate() {
                *item = start + (i as f32) * step;
            }
        }
        
        Ok(array)
    }

    fn from_vec(data: Vec<f32>, shape: &[usize]) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        if data.len() != total_elements {
            return Err(RustNumError::ShapeError(
                format!("Data length {} doesn't match shape {:?} (expected {})",
                       data.len(), shape, total_elements)
            ));
        }

        let pool = Arc::new(RwLock::new(crate::memory::create_default_pool()));
        let mut array = Self::new(shape.to_vec(), StorageOrder::RowMajor, pool)?;

        let array_data = array.data_mut();
        array_data.copy_from_slice(&data);

        Ok(array)
    }
}

/// 便利函数，用于快速创建数组
pub mod convenience {
    use super::*;
    
    /// 创建 f64 类型的全零数组
    pub fn zeros_f64(shape: &[usize]) -> Result<RustArray<f64>, RustNumError> {
        RustArray::zeros(shape)
    }
    
    /// 创建 f32 类型的全零数组
    pub fn zeros_f32(shape: &[usize]) -> Result<RustArray<f32>, RustNumError> {
        RustArray::zeros(shape)
    }
    
    /// 创建 f64 类型的全一数组
    pub fn ones_f64(shape: &[usize]) -> Result<RustArray<f64>, RustNumError> {
        RustArray::ones(shape)
    }
    
    /// 创建 f32 类型的全一数组
    pub fn ones_f32(shape: &[usize]) -> Result<RustArray<f32>, RustNumError> {
        RustArray::ones(shape)
    }
    
    /// 创建 f64 类型的单位矩阵
    pub fn eye_f64(n: usize) -> Result<RustArray<f64>, RustNumError> {
        RustArray::eye(n)
    }
    
    /// 创建 f32 类型的单位矩阵
    pub fn eye_f32(n: usize) -> Result<RustArray<f32>, RustNumError> {
        RustArray::eye(n)
    }
    
    /// 创建 f64 类型的等差数列数组
    pub fn arange_f64(start: f64, stop: f64, step: f64) -> Result<RustArray<f64>, RustNumError> {
        RustArray::arange(start, stop, step)
    }
    
    /// 创建 f32 类型的等差数列数组
    pub fn arange_f32(start: f32, stop: f32, step: f32) -> Result<RustArray<f32>, RustNumError> {
        RustArray::arange(start, stop, step)
    }
    
    /// 创建 f64 类型的线性等分数组
    pub fn linspace_f64(start: f64, stop: f64, num: usize) -> Result<RustArray<f64>, RustNumError> {
        RustArray::linspace(start, stop, num)
    }
    
    /// 创建 f32 类型的线性等分数组
    pub fn linspace_f32(start: f32, stop: f32, num: usize) -> Result<RustArray<f32>, RustNumError> {
        RustArray::linspace(start, stop, num)
    }
    
    /// 创建 f64 类型的填充指定值的数组
    pub fn full_f64(shape: &[usize], value: f64) -> Result<RustArray<f64>, RustNumError> {
        RustArray::full(shape, value)
    }
    
    /// 创建 f32 类型的填充指定值的数组
    pub fn full_f32(shape: &[usize], value: f32) -> Result<RustArray<f32>, RustNumError> {
        RustArray::full(shape, value)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use super::convenience::*;
    
    #[test]
    fn test_zeros_creation() {
        let arr = zeros_f64(&[2, 3]).unwrap();
        assert_eq!(arr.shape(), &[2, 3]);
        assert_eq!(arr.len(), 6);
        
        // 验证所有元素都是 0
        for &val in arr.data() {
            assert_eq!(val, 0.0);
        }
    }
    
    #[test]
    fn test_ones_creation() {
        let arr = ones_f32(&[3, 2]).unwrap();
        assert_eq!(arr.shape(), &[3, 2]);
        
        // 验证所有元素都是 1
        for &val in arr.data() {
            assert_eq!(val, 1.0);
        }
    }
    
    #[test]
    fn test_eye_creation() {
        let arr = eye_f64(3).unwrap();
        assert_eq!(arr.shape(), &[3, 3]);
        
        // 验证单位矩阵
        let data = arr.data();
        for i in 0..3 {
            for j in 0..3 {
                let expected = if i == j { 1.0 } else { 0.0 };
                assert_eq!(data[i * 3 + j], expected);
            }
        }
    }
    
    #[test]
    fn test_full_creation() {
        let arr = RustArray::<f64>::full(&[2, 2], 3.14).unwrap();
        assert_eq!(arr.shape(), &[2, 2]);
        
        for &val in arr.data() {
            assert_eq!(val, 3.14);
        }
    }
    
    #[test]
    fn test_arange_creation() {
        // 测试基本功能 - 使用更简单的创建方式
        let arr = zeros_f64(&[5]).unwrap();
        assert_eq!(arr.len(), 5);
        assert_eq!(arr.data().len(), 5);
        
        // 测试ones
        let arr = ones_f64(&[3]).unwrap();
        assert_eq!(arr.len(), 3);
        for &val in arr.data().iter() {
            assert!((val - 1.0).abs() < 1e-10);
        }
        
        // 测试小数步长
        let arr = arange_f32(0.0, 2.0, 0.5).unwrap();
        assert_eq!(arr.len(), 4);
        let expected = [0.0, 0.5, 1.0, 1.5];
        for (i, &val) in arr.data().iter().enumerate() {
            assert!((val - expected[i]).abs() < 1e-6);
        }
        
        // 测试负步长
        let arr = arange_f64(5.0, 0.0, -1.0).unwrap();
        assert_eq!(arr.len(), 5);
        let expected = [5.0, 4.0, 3.0, 2.0, 1.0];
        for (i, &val) in arr.data().iter().enumerate() {
            assert!((val - expected[i]).abs() < 1e-10);
        }
    }
    
    #[test]
    fn test_linspace_creation() {
        // 测试基本线性等分
        let arr = linspace_f64(0.0, 10.0, 11).unwrap();
        assert_eq!(arr.len(), 11);
        for (i, &val) in arr.data().iter().enumerate() {
            let expected = i as f64;
            assert!((val - expected).abs() < 1e-10);
        }
        
        // 测试单个元素
        let arr = linspace_f32(5.0, 10.0, 1).unwrap();
        assert_eq!(arr.len(), 1);
        assert_eq!(arr.data()[0], 5.0);
        
        // 测试两个元素
        let arr = linspace_f64(0.0, 1.0, 2).unwrap();
        assert_eq!(arr.len(), 2);
        assert!((arr.data()[0] - 0.0).abs() < 1e-10);
        assert!((arr.data()[1] - 1.0).abs() < 1e-10);
    }
    
    #[test]
    fn test_arange_error_cases() {
        // 测试零步长
        assert!(arange_f64(0.0, 5.0, 0.0).is_err());
        
        // 测试无效范围
        assert!(arange_f64(5.0, 0.0, 1.0).is_err());
        assert!(arange_f64(0.0, 5.0, -1.0).is_err());
    }
    
    #[test]
    fn test_linspace_error_cases() {
        // 测试零个元素
        assert!(linspace_f64(0.0, 1.0, 0).is_err());
    }
}
