use std::alloc::Layout;
use std::ptr::NonNull;
use std::sync::Arc;

use crate::memory::{MemoryPool, MemoryError};

/// 数组存储后端
#[derive(Debug)]
pub struct Storage<T> {
    /// 数据指针
    ptr: NonNull<T>,
    /// 元素数量
    size: usize,
    /// 内存池
    pool: Arc<MemoryPool>,
    /// 内存布局
    layout: Layout,
}

impl<T> Storage<T> {
    /// 创建新的存储
    pub fn new(size: usize, pool: Arc<MemoryPool>) -> Result<Self, MemoryError> {
        let layout = Layout::array::<T>(size)
            .map_err(|_| MemoryError::InvalidSize)?;
        
        let ptr = pool.allocate(layout)?;
        
        Ok(Self {
            ptr: ptr.cast(),
            size,
            pool,
            layout,
        })
    }

    /// 创建未初始化的存储
    pub fn uninit(size: usize, pool: Arc<MemoryPool>) -> Result<Self, MemoryError> {
        Self::new(size, pool)
    }

    /// 从现有数据创建存储
    pub fn from_slice(data: &[T], pool: Arc<MemoryPool>) -> Result<Self, MemoryError>
    where
        T: Copy,
    {
        let mut storage = Self::new(data.len(), pool)?;
        storage.copy_from_slice(data);
        Ok(storage)
    }

    /// 获取数据指针
    pub fn as_ptr(&self) -> *const T {
        self.ptr.as_ptr()
    }

    /// 获取可变数据指针
    pub fn as_mut_ptr(&mut self) -> *mut T {
        self.ptr.as_ptr()
    }

    /// 获取元素数量
    pub fn len(&self) -> usize {
        self.size
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.size == 0
    }

    /// 获取内存布局
    pub fn layout(&self) -> Layout {
        self.layout
    }

    /// 从切片复制数据
    pub fn copy_from_slice(&mut self, src: &[T])
    where
        T: Copy,
    {
        assert!(src.len() <= self.size);
        unsafe {
            std::ptr::copy_nonoverlapping(
                src.as_ptr(),
                self.as_mut_ptr(),
                src.len()
            );
        }
    }

    /// 复制到切片
    pub fn copy_to_slice(&self, dst: &mut [T])
    where
        T: Copy,
    {
        assert!(dst.len() >= self.size);
        unsafe {
            std::ptr::copy_nonoverlapping(
                self.as_ptr(),
                dst.as_mut_ptr(),
                self.size
            );
        }
    }

    /// 创建存储的视图
    pub fn view(&self, offset: usize, len: usize) -> Option<StorageView<T>> {
        if offset + len > self.size {
            return None;
        }

        unsafe {
            Some(StorageView {
                ptr: NonNull::new_unchecked(self.as_ptr().add(offset) as *mut T),
                len,
                _marker: std::marker::PhantomData,
            })
        }
    }

    /// 创建存储的可变视图
    pub fn view_mut(&mut self, offset: usize, len: usize) -> Option<StorageViewMut<T>> {
        if offset + len > self.size {
            return None;
        }

        unsafe {
            Some(StorageViewMut {
                ptr: NonNull::new_unchecked(self.as_mut_ptr().add(offset)),
                len,
                _marker: std::marker::PhantomData,
            })
        }
    }
}

/// 存储视图（只读）
#[derive(Debug)]
pub struct StorageView<T> {
    ptr: NonNull<T>,
    len: usize,
    _marker: std::marker::PhantomData<T>,
}

/// 存储视图（可变）
#[derive(Debug)]
pub struct StorageViewMut<T> {
    ptr: NonNull<T>,
    len: usize,
    _marker: std::marker::PhantomData<T>,
}

impl<T> StorageView<T> {
    /// 获取数据指针
    pub fn as_ptr(&self) -> *const T {
        self.ptr.as_ptr()
    }

    /// 获取长度
    pub fn len(&self) -> usize {
        self.len
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.len == 0
    }

    /// 复制到切片
    pub fn copy_to_slice(&self, dst: &mut [T])
    where
        T: Copy,
    {
        assert!(dst.len() >= self.len);
        unsafe {
            std::ptr::copy_nonoverlapping(
                self.as_ptr(),
                dst.as_mut_ptr(),
                self.len
            );
        }
    }
}

impl<T> StorageViewMut<T> {
    /// 获取可变数据指针
    pub fn as_mut_ptr(&mut self) -> *mut T {
        self.ptr.as_ptr()
    }

    /// 获取长度
    pub fn len(&self) -> usize {
        self.len
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.len == 0
    }

    /// 从切片复制数据
    pub fn copy_from_slice(&mut self, src: &[T])
    where
        T: Copy,
    {
        assert!(src.len() <= self.len);
        unsafe {
            std::ptr::copy_nonoverlapping(
                src.as_ptr(),
                self.as_mut_ptr(),
                src.len()
            );
        }
    }
}

impl<T> Drop for Storage<T> {
    fn drop(&mut self) {
        unsafe {
            self.pool.deallocate(self.ptr.cast(), self.layout);
        }
    }
}

unsafe impl<T: Send> Send for Storage<T> {}
unsafe impl<T: Sync> Sync for Storage<T> {}
unsafe impl<T: Send> Send for StorageView<T> {}
unsafe impl<T: Sync> Sync for StorageView<T> {}
unsafe impl<T: Send> Send for StorageViewMut<T> {}
unsafe impl<T: Sync> Sync for StorageViewMut<T> {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::memory::create_default_pool;

    #[test]
    fn test_storage_creation() {
        let pool = Arc::new(create_default_pool());
        let storage: Storage<f32> = Storage::new(100, pool).unwrap();
        assert_eq!(storage.len(), 100);
        assert!(!storage.is_empty());
    }

    #[test]
    fn test_storage_from_slice() {
        let pool = Arc::new(create_default_pool());
        let data = vec![1.0f32, 2.0, 3.0, 4.0];
        let storage = Storage::from_slice(&data, pool).unwrap();
        
        let mut output = vec![0.0f32; 4];
        storage.copy_to_slice(&mut output);
        assert_eq!(output, data);
    }

    #[test]
    fn test_storage_view() {
        let pool = Arc::new(create_default_pool());
        let data = vec![1.0f32, 2.0, 3.0, 4.0];
        let storage = Storage::from_slice(&data, pool).unwrap();
        
        let view = storage.view(1, 2).unwrap();
        let mut output = vec![0.0f32; 2];
        view.copy_to_slice(&mut output);
        assert_eq!(output, vec![2.0, 3.0]);
    }

    #[test]
    fn test_storage_view_mut() {
        let pool = Arc::new(create_default_pool());
        let mut storage: Storage<f32> = Storage::new(4, pool).unwrap();
        
        let data = vec![1.0f32, 2.0];
        let mut view = storage.view_mut(1, 2).unwrap();
        view.copy_from_slice(&data);
        
        let mut output = vec![0.0f32; 4];
        storage.copy_to_slice(&mut output);
        assert_eq!(&output[1..3], &[1.0, 2.0]);
    }
}
