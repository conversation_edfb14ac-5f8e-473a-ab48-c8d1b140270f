use std::ptr::NonNull;
use std::sync::Arc;
use parking_lot::RwLock;

use crate::error::RustNumError;
use crate::memory::MemoryPool;

/// 数组视图 (零拷贝)
pub struct ArrayView<'a, T> {
    pub ptr: NonNull<T>,
    pub shape: &'a [usize],
    pub strides: &'a [isize],
    pub layout: Layout,
    pub _marker: std::marker::PhantomData<&'a T>,
}

impl<'a, T> ArrayView<'a, T> {
    pub fn as_slice(&self) -> &[T] {
        unsafe { std::slice::from_raw_parts(self.ptr.as_ptr(), self.shape.iter().product()) }
    }

    pub fn as_mut_slice(&mut self) -> &mut [T] {
        unsafe { std::slice::from_raw_parts_mut(self.ptr.as_ptr(), self.shape.iter().product()) }
    }

    /// 获取指定索引处的值
    pub fn get(&self, index: &[usize]) -> Option<&T> {
        if index.len() != self.shape.len() {
            return None;
        }

        let mut offset = 0;
        for i in 0..index.len() {
            if index[i] >= self.shape[i] {
                return None;
            }
            offset += (index[i] as isize * self.strides[i]) as usize;
        }

        unsafe {
            Some(&*self.ptr.as_ptr().add(offset))
        }
    }
}

/// 数据布局信息
#[derive(Debug, Clone, Copy)]
pub struct Layout {
    /// 内存对齐方式
    pub alignment: usize,
    /// 是否连续存储
    pub is_contiguous: bool,
    /// 内存排列方式（行主序/列主序）
    pub order: StorageOrder,
}

/// 存储顺序
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum StorageOrder {
    /// 行主序 (C-style)
    RowMajor,
    /// 列主序 (Fortran-style)
    ColumnMajor,
}

/// 数组的主要实现
#[derive(Clone)]
pub struct RustArray<T> {
    /// 数据缓冲区
    data: Buffer<T>,
    /// 数组形状
    shape: Vec<usize>,
    /// 步长信息
    strides: Vec<isize>,
    /// 布局信息
    layout: Layout,
    /// 内存池引用
    memory_pool: Arc<RwLock<MemoryPool>>,
}

/// 数据缓冲区
#[derive(Clone)]
pub struct Buffer<T> {
    pub ptr: NonNull<T>,
    pub len: usize,
    pub capacity: usize,
    pool: Arc<RwLock<MemoryPool>>,
}

impl<T> Buffer<T> where T: Copy + Default {
    pub fn new(size: usize, alignment: usize, pool: Arc<RwLock<MemoryPool>>) -> Result<Self, RustNumError> {
        if size == 0 {
            return Ok(Self {
                ptr: NonNull::dangling(),
                len: 0,
                capacity: 0,
                pool: pool.clone(),
            });
        }

        let layout = std::alloc::Layout::from_size_align(size * std::mem::size_of::<T>(), alignment)
            .map_err(|_| RustNumError::AllocationError("Invalid layout for allocation".to_string()))?;

        // 暂时使用标准分配器，避免内存池的复杂性
        let ptr_u8 = unsafe {
            let raw_ptr = std::alloc::alloc(layout);
            if raw_ptr.is_null() {
                return Err(RustNumError::AllocationError("Memory allocation failed".to_string()));
            }
            std::ptr::NonNull::new_unchecked(raw_ptr)
        };
        let ptr = ptr_u8.cast::<T>();

        Ok(Self {
            ptr,
            len: size,
            capacity: size,
            pool,
        })
    }
}

// 实现 Drop trait
impl<T> Drop for Buffer<T> {
    fn drop(&mut self) {
        if self.capacity > 0 {
            unsafe {
                // 暂时使用标准分配器，避免内存池的复杂性
                let layout = std::alloc::Layout::from_size_align_unchecked(
                    self.len * std::mem::size_of::<T>(),
                    std::mem::align_of::<T>(),
                );
                std::alloc::dealloc(self.ptr.cast::<u8>().as_ptr(), layout);
            }
        }
    }
}

impl<T> RustArray<T> where T: Copy + Default {
    /// 计算步长
    fn compute_strides(shape: &[usize], order: StorageOrder) -> Vec<isize> {
        let ndim = shape.len();
        let mut strides = vec![0; ndim];
        if ndim == 0 { return strides; }

        match order {
            StorageOrder::RowMajor => {
                let mut stride = 1;
                for i in (0..ndim).rev() {
                    strides[i] = stride;
                    stride *= shape[i] as isize;
                }
            }
            StorageOrder::ColumnMajor => {
                let mut stride = 1;
                for i in 0..ndim {
                    strides[i] = stride;
                    stride *= shape[i] as isize;
                }
            }
        }
        strides
    }
    /// 创建新的RustArray实例
    pub fn new(
        shape: Vec<usize>,
        order: StorageOrder,
        memory_pool: Arc<RwLock<MemoryPool>>,
    ) -> Result<Self, RustNumError> {
        let size = shape.iter().product();
        let alignment = 64; // 假设 64 字节对齐

        let data = Buffer::new(size, alignment, memory_pool.clone())?;

        let strides = Self::compute_strides(&shape, order);

        let layout = Layout {
            alignment,
            is_contiguous: true, // 初始创建时认为是连续的
            order,
        };

        Ok(Self {
            data,
            shape: shape.to_vec(),
            strides,
            layout,
            memory_pool,
        })
    }





}

// 为 RustArray 实现方法
impl<T> RustArray<T> where T: Copy + Default {
    /// 获取数组视图
    pub fn view(&self) -> ArrayView<'_, T> {
        ArrayView {
            ptr: self.data.ptr,
            shape: &self.shape,
            strides: &self.strides,
            layout: self.layout,
            _marker: std::marker::PhantomData,
        }
    }

    /// 获取数组形状
    pub fn shape(&self) -> &[usize] {
        &self.shape
    }
    
    /// 获取数组步长
    pub fn strides(&self) -> &[isize] {
        &self.strides
    }
    
    /// 检查数据是否连续存储
    pub fn is_contiguous(&self) -> bool {
        self.layout.is_contiguous
    }
    
    /// 获取存储顺序
    pub fn order(&self) -> StorageOrder {
        self.layout.order
    }

    /// 设置指定索引处的值
    pub fn set(&mut self, index: &[usize], value: T) -> Result<(), RustNumError> {
        if index.len() != self.shape.len() {
            return Err(RustNumError::ShapeError("Index length must match array dimension".to_string()));
        }

        let mut offset = 0;
        for i in 0..index.len() {
            if index[i] >= self.shape[i] {
                return Err(RustNumError::IndexOutOfBounds {
                    index: index[i],
                    len: self.shape[i],
                });
            }
            offset += (index[i] as isize * self.strides[i]) as usize;
        }

        unsafe {
            *self.data.ptr.as_ptr().add(offset) = value;
        }
        Ok(())
    }

    /// 获取指定索引处的值
    pub fn get(&self, index: &[usize]) -> Result<T, RustNumError> {
        if index.len() != self.shape.len() {
            return Err(RustNumError::ShapeError("Index length must match array dimension".to_string()));
        }

        let mut offset = 0;
        for i in 0..index.len() {
            if index[i] >= self.shape[i] {
                return Err(RustNumError::IndexOutOfBounds {
                    index: index[i],
                    len: self.shape[i],
                });
            }
            offset += (index[i] as isize * self.strides[i]) as usize;
        }

        unsafe {
            Ok(*self.data.ptr.as_ptr().add(offset))
        }
    }

    /// 获取可变切片
    pub fn as_mut_slice(&mut self) -> &mut [T] {
        unsafe { std::slice::from_raw_parts_mut(self.data.ptr.as_ptr(), self.data.len) }
    }

    /// 获取底层数据的不可变切片
    pub fn data(&self) -> &[T] {
        unsafe { std::slice::from_raw_parts(self.data.ptr.as_ptr(), self.data.len) }
    }

    /// 获取底层数据的可变切片
    pub fn data_mut(&mut self) -> &mut [T] {
        unsafe { std::slice::from_raw_parts_mut(self.data.ptr.as_ptr(), self.data.len) }
    }

    /// 获取数组元素总数
    pub fn len(&self) -> usize {
        self.data.len
    }

    /// 检查数组是否为空
    pub fn is_empty(&self) -> bool {
        self.data.len == 0
    }
    





    /// 转置数组
    pub fn transpose(&self) -> RustArray<T> {
        let new_shape: Vec<usize> = self.shape.iter().rev().cloned().collect();
        let new_strides: Vec<isize> = self.strides.iter().rev().cloned().collect();
        
        RustArray {
            data: self.data.clone(),
            shape: new_shape,
            strides: new_strides,
            layout: Layout {
                alignment: self.layout.alignment,
                is_contiguous: self.layout.is_contiguous,
                order: match self.layout.order {
                    StorageOrder::RowMajor => StorageOrder::ColumnMajor,
                    StorageOrder::ColumnMajor => StorageOrder::RowMajor,
                },
            },
            memory_pool: self.memory_pool.clone(),
        }
    }

    /// 转置数组（带轴参数）
    pub fn transpose_with_axes(&self, axes: Option<&[usize]>) -> Result<RustArray<T>, RustNumError> {
        let new_shape = if let Some(a) = axes {
            if a.len() != self.shape.len() {
                return Err(RustNumError::ShapeError("Axes length must match array dimension".to_string()));
            }
            a.iter().map(|&i| self.shape[i]).collect::<Vec<usize>>()
        } else {
            self.shape.iter().rev().cloned().collect::<Vec<usize>>()
        };

        let new_strides = if let Some(a) = axes {
            a.iter().map(|&i| self.strides[i]).collect::<Vec<isize>>()
        } else {
            self.strides.iter().rev().cloned().collect::<Vec<isize>>()
        };

        // 创建一个新的 RustArray 实例，共享底层数据
        Ok(RustArray {
            data: self.data.clone(), // 共享 Buffer
            shape: new_shape.clone(),
            strides: new_strides,
            layout: self.layout, // 布局信息可能需要更新，这里简化处理
            memory_pool: self.memory_pool.clone(),
        })
    }

    /// 重整数组形状
    pub fn reshape(&self, new_shape: Vec<usize>) -> Result<RustArray<T>, RustNumError> {
        let new_size: usize = new_shape.iter().product();
        let old_size: usize = self.shape.iter().product();

        if new_size != old_size {
            return Err(RustNumError::ShapeMismatch {
                expected: new_shape,
                got: self.shape.clone(),
            });
        }

        Ok(RustArray {
            data: self.data.clone(),
            shape: new_shape.clone(),
            strides: Self::compute_strides(&new_shape, self.layout.order),
            layout: Layout {
                alignment: self.layout.alignment,
                is_contiguous: true,
                order: self.layout.order,
            },
            memory_pool: self.memory_pool.clone(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::memory::MemoryPool;

    #[test]
    fn test_rust_array_creation() {
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let arr = RustArray::<f64>::new(vec![2, 3], StorageOrder::RowMajor, pool.clone()).unwrap();
        assert_eq!(arr.shape(), &[2, 3]);
        assert_eq!(arr.strides(), &[3, 1]);
        assert_eq!(arr.order(), StorageOrder::RowMajor);
        assert!(arr.is_contiguous());
    }

    #[test]
    fn test_rust_array_view() {
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut arr = RustArray::<f64>::new(vec![2, 3], StorageOrder::RowMajor, pool.clone()).unwrap();
        arr.as_mut_slice()[0] = 1.0;
        arr.as_mut_slice()[1] = 2.0;
        arr.as_mut_slice()[2] = 3.0;
        arr.as_mut_slice()[3] = 4.0;
        arr.as_mut_slice()[4] = 5.0;
        arr.as_mut_slice()[5] = 6.0;

        let view = arr.view();
        assert_eq!(view.get(&[0, 0]), Some(&1.0));
        assert_eq!(view.get(&[0, 1]), Some(&2.0));
        assert_eq!(view.get(&[1, 2]), Some(&6.0));
    }

    #[test]
    fn test_rust_array_transpose() {
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let arr = RustArray::<f64>::new(vec![2, 3], StorageOrder::RowMajor, pool.clone()).unwrap();
        let transposed_view = arr.transpose();
        assert_eq!(transposed_view.shape(), &[3, 2]);
        assert_eq!(transposed_view.strides(), &[1, 3]); // Transposed strides should be [1, 3] for row-major after transpose
        assert_eq!(transposed_view.order(), StorageOrder::ColumnMajor);
        assert!(transposed_view.is_contiguous()); // Transposed from row-major to column-major should still be contiguous
    }

    #[test]
    fn test_rust_array_reshape() {
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let arr = RustArray::<f64>::new(vec![2, 3], StorageOrder::RowMajor, pool.clone()).unwrap();
        let reshaped_view = arr.reshape(vec![3, 2]).unwrap();
        assert_eq!(reshaped_view.shape(), &[3, 2]);
        assert_eq!(reshaped_view.strides(), &[2, 1]);
        assert_eq!(reshaped_view.order(), StorageOrder::RowMajor);
        assert!(reshaped_view.is_contiguous());

        let err = arr.reshape(vec![2, 2]);
        assert!(err.is_err());
        if let Err(RustNumError::ShapeMismatch { expected, got }) = err {
            assert_eq!(expected, vec![2, 2]);
            assert_eq!(got, vec![2, 3]);
        } else {
            panic!("Unexpected error type");
        }
    }
}

// 为 RustArray 实现 Debug trait
impl<T: std::fmt::Debug + Copy> std::fmt::Debug for RustArray<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("RustArray")
            .field("shape", &self.shape)
            .field("strides", &self.strides)
            .field("layout", &self.layout)
            .field("data_sample", &self.get_debug_sample())
            .finish()
    }
}

impl<T: std::fmt::Debug + Copy> RustArray<T> {
    /// 获取用于调试的数据样本
    fn get_debug_sample(&self) -> Vec<T> {
        let total_elements = self.shape.iter().product::<usize>();
        let sample_size = std::cmp::min(10, total_elements); // 最多显示10个元素

        let mut sample = Vec::with_capacity(sample_size);
        for i in 0..sample_size {
            if i < self.data.len {
                // 使用 unsafe 访问原始指针数据（仅用于调试）
                unsafe {
                    let ptr = self.data.ptr.as_ptr().add(i);
                    sample.push(*ptr);
                }
            }
        }
        sample
    }

    /// 获取底层数据的切片引用
    pub fn as_slice(&self) -> &[T] {
        unsafe {
            std::slice::from_raw_parts(self.data.ptr.as_ptr(), self.data.len)
        }
    }


}
