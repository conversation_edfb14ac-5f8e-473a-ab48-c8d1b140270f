//! SIMD 优化的数学运算
//! 
//! 为数组数学运算提供 SIMD 加速

use crate::error::RustNumError;
use crate::simd::{SimdOp, execute_simd_op};
use super::array_impl::RustArray;
use super::math_ops::ArrayMath;

/// SIMD 优化的数学运算特征
pub trait SimdArrayMath<T> {
    /// SIMD 优化的元素级加法
    fn simd_add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// SIMD 优化的元素级减法
    fn simd_sub(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// SIMD 优化的元素级乘法
    fn simd_mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// SIMD 优化的元素级除法
    fn simd_div(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// SIMD 优化的标量加法
    fn simd_add_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// SIMD 优化的标量乘法
    fn simd_mul_scalar(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl SimdArrayMath<f32> for RustArray<f32> {
    fn simd_add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        // 使用 SIMD 优化的运算
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Add, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Sub, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Mul, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_div(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        // 检查除零
        for &val in other_data {
            if val == 0.0 {
                return Err(RustNumError::DivisionByZero);
            }
        }
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Div, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_add_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        
        // 创建标量数组进行 SIMD 运算
        let scalar_vec = vec![scalar; result_data.len()];
        execute_simd_op(SimdOp::Add, &input_data, &scalar_vec, result_data);
        
        Ok(result)
    }
    
    fn simd_mul_scalar(&self, scalar: f32) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        
        // 创建标量数组进行 SIMD 运算
        let scalar_vec = vec![scalar; result_data.len()];
        execute_simd_op(SimdOp::Mul, &input_data, &scalar_vec, result_data);
        
        Ok(result)
    }
}

impl SimdArrayMath<f64> for RustArray<f64> {
    fn simd_add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Add, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Sub, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = self.clone();
        let other_data = other.data();
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Mul, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_div(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let other_data = other.data();
        
        // 检查除零
        for &val in other_data {
            if val == 0.0 {
                return Err(RustNumError::DivisionByZero);
            }
        }
        
        let mut result = self.clone();
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        
        execute_simd_op(SimdOp::Div, &input_data, other_data, result_data);
        
        Ok(result)
    }
    
    fn simd_add_scalar(&self, scalar: f64) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let len = result.data().len();
        let scalar_vec = vec![scalar; len];
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Add, &input_data, &scalar_vec, result_data);
        
        Ok(result)
    }
    
    fn simd_mul_scalar(&self, scalar: f64) -> Result<Self, RustNumError> {
        let mut result = self.clone();
        let len = result.data().len();
        let scalar_vec = vec![scalar; len];
        
        let result_data = result.data_mut();
        let input_data: Vec<_> = result_data.iter().copied().collect();
        execute_simd_op(SimdOp::Mul, &input_data, &scalar_vec, result_data);
        
        Ok(result)
    }
}

/// 便利函数，自动选择最优实现
impl<T> RustArray<T> 
where 
    T: Copy + Default + PartialEq + PartialOrd,
    Self: SimdArrayMath<T> + ArrayMath<T>,
{
    /// 智能加法 - 自动选择 SIMD 或标量实现
    pub fn smart_add(&self, other: &Self) -> Result<Self, RustNumError> {
        // 对于大数组使用 SIMD，小数组使用标量运算
        if self.len() >= 64 {
            self.simd_add(other)
        } else {
            self.add(other)
        }
    }
    
    /// 智能乘法 - 自动选择 SIMD 或标量实现
    pub fn smart_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.len() >= 64 {
            self.simd_mul(other)
        } else {
            self.mul(other)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;
    
    #[test]
    fn test_simd_array_addition() {
        let a = RustArray::<f32>::ones(&[1024]).unwrap();
        let b = RustArray::<f32>::ones(&[1024]).unwrap();
        let result = a.simd_add(&b).unwrap();
        
        for &val in result.data() {
            assert_eq!(val, 2.0);
        }
    }
    
    #[test]
    fn test_simd_array_multiplication() {
        let a = RustArray::<f32>::full(&[1024], 3.0).unwrap();
        let b = RustArray::<f32>::full(&[1024], 2.0).unwrap();
        let result = a.simd_mul(&b).unwrap();
        
        for &val in result.data() {
            assert_eq!(val, 6.0);
        }
    }
    
    #[test]
    fn test_simd_scalar_operations() {
        let a = RustArray::<f32>::ones(&[1024]).unwrap();
        
        let add_result = a.simd_add_scalar(5.0).unwrap();
        for &val in add_result.data() {
            assert_eq!(val, 6.0);
        }
        
        let mul_result = a.simd_mul_scalar(3.0).unwrap();
        for &val in mul_result.data() {
            assert_eq!(val, 3.0);
        }
    }
    
    #[test]
    fn test_smart_operations() {
        // 测试大数组（应该使用 SIMD）
        let large_a = RustArray::<f32>::ones(&[1024]).unwrap();
        let large_b = RustArray::<f32>::ones(&[1024]).unwrap();
        let large_result = large_a.smart_add(&large_b).unwrap();
        
        for &val in large_result.data() {
            assert_eq!(val, 2.0);
        }
        
        // 测试小数组（应该使用标量运算）
        let small_a = RustArray::<f32>::ones(&[4]).unwrap();
        let small_b = RustArray::<f32>::ones(&[4]).unwrap();
        let small_result = small_a.smart_add(&small_b).unwrap();
        
        for &val in small_result.data() {
            assert_eq!(val, 2.0);
        }
    }
    
    #[test]
    fn test_simd_error_handling() {
        let a = RustArray::<f32>::ones(&[1024]).unwrap();
        let b = RustArray::<f32>::ones(&[512]).unwrap();
        
        // 测试形状不匹配
        assert!(a.simd_add(&b).is_err());
        
        // 测试除零错误
        let zeros = RustArray::<f32>::zeros(&[1024]).unwrap();
        assert!(a.simd_div(&zeros).is_err());
    }
}
