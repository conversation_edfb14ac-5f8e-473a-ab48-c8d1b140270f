use super::super::array::RustArray;
use num_traits::Float;

/// LU分解结果
#[derive(Debug)]
pub struct LUDecomposition<T> {
    /// L矩阵（下三角）
    pub l: RustArray<T>,
    /// U矩阵（上三角）
    pub u: RustArray<T>,
    /// 置换矩阵
    pub p: RustArray<T>,
}

/// QR分解结果
#[derive(Debug)]
pub struct QRDecomposition<T> {
    /// Q矩阵（正交）
    pub q: RustArray<T>,
    /// R矩阵（上三角）
    pub r: RustArray<T>,
}

impl<T: Copy + Default + Float> RustArray<T> {
    /// LU分解
    pub fn lu_decomposition(&self) -> Option<LUDecomposition<T>> {
        if self.dims().len() != 2 || self.dims()[0] != self.dims()[1] {
            return None;
        }

        let n = self.dims()[0];
        let mut l = Self::zeros(vec![n, n]);
        let mut u = self.clone();
        let mut p = Self::zeros(vec![n, n]);

        // 初始化置换矩阵为单位矩阵
        for i in 0..n {
            p[&[i, i]] = T::one();
        }

        // 主元消去
        for k in 0..n {
            // 寻找主元
            let mut pivot = k;
            let mut max_val = u[&[k, k]].abs();
            for i in (k + 1)..n {
                let val = u[&[i, k]].abs();
                if val > max_val {
                    max_val = val;
                    pivot = i;
                }
            }

            // 交换行
            if pivot != k {
                for j in 0..n {
                    let tmp = u[&[k, j]];
                    u[&[k, j]] = u[&[pivot, j]];
                    u[&[pivot, j]] = tmp;

                    let tmp = p[&[k, j]];
                    p[&[k, j]] = p[&[pivot, j]];
                    p[&[pivot, j]] = tmp;

                    if j < k {
                        let tmp = l[&[k, j]];
                        l[&[k, j]] = l[&[pivot, j]];
                        l[&[pivot, j]] = tmp;
                    }
                }
            }

            // 计算L和U的元素
            l[&[k, k]] = T::one();
            for i in (k + 1)..n {
                let factor = u[&[i, k]] / u[&[k, k]];
                l[&[i, k]] = factor;
                for j in k..n {
                    u[&[i, j]] = u[&[i, j]] - factor * u[&[k, j]];
                }
            }
        }

        Some(LUDecomposition { l, u, p })
    }

    /// QR分解（使用Gram-Schmidt正交化）
    pub fn qr_decomposition(&self) -> Option<QRDecomposition<T>> {
        if self.dims().len() != 2 {
            return None;
        }

        let m = self.dims()[0];
        let n = self.dims()[1];
        let mut q = Self::zeros(vec![m, n]);
        let mut r = Self::zeros(vec![n, n]);

        // 复制输入矩阵到Q
        for i in 0..m {
            for j in 0..n {
                q[&[i, j]] = self[&[i, j]];
            }
        }

        // Gram-Schmidt正交化
        for j in 0..n {
            // 计算列向量的范数
            let mut norm = T::zero();
            for i in 0..m {
                norm = norm + q[&[i, j]] * q[&[i, j]];
            }
            norm = norm.sqrt();
            r[&[j, j]] = norm;

            // 归一化
            if norm > T::zero() {
                for i in 0..m {
                    q[&[i, j]] = q[&[i, j]] / norm;
                }
            }

            // 正交化后续列
            for k in (j + 1)..n {
                let mut dot = T::zero();
                for i in 0..m {
                    dot = dot + q[&[i, j]] * q[&[i, k]];
                }
                r[&[j, k]] = dot;

                for i in 0..m {
                    q[&[i, k]] = q[&[i, k]] - dot * q[&[i, j]];
                }
            }
        }

        Some(QRDecomposition { q, r })
    }

    /// 使用QR分解求解线性方程组 Ax = b
    pub fn solve(&self, b: &Self) -> Option<Self> {
        if self.dims().len() != 2 || b.dims().len() != 1 ||
           self.dims()[0] != self.dims()[1] || self.dims()[0] != b.dims()[0] {
            return None;
        }

        // 计算QR分解
        let qr = self.qr_decomposition()?;
        let n = self.dims()[0];

        // 计算Q^T * b
        let mut y = Self::zeros(vec![n]);
        for i in 0..n {
            let mut sum = T::zero();
            for j in 0..n {
                sum = sum + qr.q[&[j, i]] * b[&[j]];
            }
            y[&[i]] = sum;
        }

        // 回代求解Rx = y
        let mut x = Self::zeros(vec![n]);
        for i in (0..n).rev() {
            let mut sum = y[&[i]];
            for j in (i + 1)..n {
                sum = sum - qr.r[&[i, j]] * x[&[j]];
            }
            x[&[i]] = sum / qr.r[&[i, i]];
        }

        Some(x)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_lu_decomposition() {
        let a = RustArray::from_vec(
            vec![4.0f64, 3.0, 8.0, 3.0],
            vec![2, 2]
        ).unwrap();

        let lu = a.lu_decomposition().unwrap();
        
        // 验证 P * A = L * U
        let pa = lu.p.matmul(&a, None).unwrap();
        let lu_prod = lu.l.matmul(&lu.u, None).unwrap();

        for i in 0..2 {
            for j in 0..2 {
                assert!((pa[&[i, j]] - lu_prod[&[i, j]]).abs() < 1e-5);
            }
        }
    }

    #[test]
    fn test_qr_decomposition() {
        let a = RustArray::from_vec(
            vec![4.0f32, 3.0, 8.0, 3.0],
            vec![2, 2]
        ).unwrap();

        let qr = a.qr_decomposition().unwrap();
        
        // 验证 Q * R = A
        let qr_prod = qr.q.matmul(&qr.r, None).unwrap();

        for i in 0..2 {
            for j in 0..2 {
                assert!((a[&[i, j]] - qr_prod[&[i, j]]).abs() < 1e-5);
            }
        }

        // 验证Q是正交矩阵
        let qt = qr.q.matrix_transpose().unwrap();
        let q_qt = qr.q.matmul(&qt, None).unwrap();

        for i in 0..2 {
            for j in 0..2 {
                let expected = if i == j { 1.0 } else { 0.0 };
                assert!((q_qt[&[i, j]] - expected).abs() < 1e-5);
            }
        }
    }

    #[test]
    fn test_linear_solve() {
        let a = RustArray::from_vec(
            vec![4.0f32, 3.0, 8.0, 3.0],
            vec![2, 2]
        ).unwrap();
        let b = RustArray::from_vec(
            vec![7.0f32, 11.0],
            vec![2]
        ).unwrap();

        let x = a.solve(&b).unwrap();
        
        // 验证解
        let ax = a.matmul(&RustArray::from_vec(vec![x[&[0]], x[&[1]]], vec![2, 1]).unwrap(), None).unwrap();
        assert!((ax[&[0]] - b[&[0]]).abs() < 1e-5);
        assert!((ax[&[1]] - b[&[1]]).abs() < 1e-5);
    }
}
