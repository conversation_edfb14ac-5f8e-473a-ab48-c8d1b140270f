mod ops;
mod decomposition;

pub use ops::MatmulStrategy;
pub use decomposition::{LUDecomposition, QRDecomposition};

use super::array::RustArray;
use num_traits::Float;

impl<T: Copy + Default + Float> RustArray<T> {
    /// 创建单位矩阵
    pub fn eye(n: usize) -> Self {
        let mut result = Self::zeros(vec![n, n]);
        for i in 0..n {
            result[&[i, i]] = T::one();
        }
        result
    }

    /// 是否为对称矩阵
    pub fn is_symmetric(&self) -> bool {
        if self.dims().len() != 2 || self.dims()[0] != self.dims()[1] {
            return false;
        }

        let n = self.dims()[0];
        for i in 0..n {
            for j in (i + 1)..n {
                if (self[&[i, j]] - self[&[j, i]]).abs() > T::epsilon() {
                    return false;
                }
            }
        }
        true
    }

    /// 是否为正定矩阵
    pub fn is_positive_definite(&self) -> bool {
        if !self.is_symmetric() {
            return false;
        }

        // 使用Cholesky分解判断
        // 如果矩阵能够完成Cholesky分解，则它是正定的
        if let Some(qr) = self.qr_decomposition() {
            let n = self.dims()[0];
            for i in 0..n {
                if qr.r[&[i, i]] <= T::zero() {
                    return false;
                }
            }
            true
        } else {
            false
        }
    }

    /// 计算矩阵的行列式（使用LU分解）
    pub fn determinant(&self) -> Option<T> {
        if self.dims().len() != 2 || self.dims()[0] != self.dims()[1] {
            return None;
        }

        let lu = self.lu_decomposition()?;
        let n = self.dims()[0];
        let mut det = T::one();

        // 行列式是U的对角线元素的乘积
        for i in 0..n {
            det = det * lu.u[&[i, i]];
        }

        Some(det)
    }

    /// 计算矩阵的逆（使用QR分解）
    pub fn inverse(&self) -> Option<Self> {
        if self.dims().len() != 2 || self.dims()[0] != self.dims()[1] {
            return None;
        }

        let n = self.dims()[0];
        let mut result = Self::zeros(vec![n, n]);
        let e = Self::eye(n);

        // 逐列求解
        for j in 0..n {
            let mut b = Self::zeros(vec![n]);
            for i in 0..n {
                b[&[i]] = e[&[i, j]];
            }
            
            let col = self.solve(&b)?;
            for i in 0..n {
                result[&[i, j]] = col[&[i]];
            }
        }

        Some(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_eye() {
        let e = RustArray::<f32>::eye(3);
        assert_eq!(e[&[0, 0]], 1.0);
        assert_eq!(e[&[1, 1]], 1.0);
        assert_eq!(e[&[2, 2]], 1.0);
        assert_eq!(e[&[0, 1]], 0.0);
    }

    #[test]
    fn test_symmetric() {
        let sym = RustArray::from_vec(
            vec![1.0f32, 2.0, 2.0, 1.0],
            vec![2, 2]
        ).unwrap();
        assert!(sym.is_symmetric());

        let non_sym = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0, 1.0],
            vec![2, 2]
        ).unwrap();
        assert!(!non_sym.is_symmetric());
    }

    #[test]
    fn test_positive_definite() {
        let pd = RustArray::from_vec(
            vec![2.0f32, 1.0, 1.0, 2.0],
            vec![2, 2]
        ).unwrap();
        assert!(pd.is_positive_definite());

        let non_pd = RustArray::from_vec(
            vec![1.0f32, 2.0, 2.0, 1.0],
            vec![2, 2]
        ).unwrap();
        assert!(!non_pd.is_positive_definite());
    }

    #[test]
    fn test_determinant() {
        let a = RustArray::from_vec(
            vec![2.0f32, 1.0, 1.0, 3.0],
            vec![2, 2]
        ).unwrap();
        let det = a.determinant().unwrap();
        assert!((det - 5.0).abs() < 1e-5);
    }

    #[test]
    fn test_inverse() {
        let a = RustArray::from_vec(
            vec![2.0f32, 1.0, 1.0, 3.0],
            vec![2, 2]
        ).unwrap();
        let inv = a.inverse().unwrap();
        
        // 验证 A * A^(-1) = I
        let prod = a.matmul(&inv, None).unwrap();
        assert!((prod[&[0, 0]] - 1.0).abs() < 1e-5);
        assert!((prod[&[0, 1]]).abs() < 1e-5);
        assert!((prod[&[1, 0]]).abs() < 1e-5);
        assert!((prod[&[1, 1]] - 1.0).abs() < 1e-5);
    }
}
