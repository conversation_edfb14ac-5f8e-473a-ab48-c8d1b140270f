use super::super::array::RustArray;
use crate::memory::create_default_pool;
use std::sync::Arc;

/// 矩阵乘法算法类型
#[derive(Debug, <PERSON><PERSON>, Copy)]
pub enum MatmulStrategy {
    /// 基础实现
    Basic,
    /// 分块优化
    Blocked {
        block_size: usize,
    },
    /// BLAS加速
    Blas,
}

impl Default for MatmulStrategy {
    fn default() -> Self {
        MatmulStrategy::Blocked {
            block_size: 32,
        }
    }
}

impl<T: Copy + Default + std::ops::Add<Output = T> + std::ops::Mul<Output = T>> RustArray<T> {
    /// 矩阵乘法
    pub fn matmul(&self, other: &Self, strategy: Option<MatmulStrategy>) -> Option<Self> {
        // 检查维度
        if self.dims().len() != 2 || other.dims().len() != 2 {
            return None;
        }
        if self.dims()[1] != other.dims()[0] {
            return None;
        }

        let m = self.dims()[0];
        let k = self.dims()[1];
        let n = other.dims()[1];

        let strategy = strategy.unwrap_or_default();
        let mut result = Self::zeros(vec![m, n]);

        match strategy {
            MatmulStrategy::Basic => {
                // 基础实现
                for i in 0..m {
                    for j in 0..n {
                        let mut sum = T::default();
                        for l in 0..k {
                            sum = sum + self[&[i, l]] * other[&[l, j]];
                        }
                        result[&[i, j]] = sum;
                    }
                }
            }
            MatmulStrategy::Blocked { block_size } => {
                // 分块实现
                let bs = block_size;
                for i0 in (0..m).step_by(bs) {
                    let i_end = (i0 + bs).min(m);
                    for j0 in (0..n).step_by(bs) {
                        let j_end = (j0 + bs).min(n);
                        for k0 in (0..k).step_by(bs) {
                            let k_end = (k0 + bs).min(k);
                            
                            // 计算块内乘法
                            for i in i0..i_end {
                                for j in j0..j_end {
                                    let mut sum = T::default();
                                    for l in k0..k_end {
                                        sum = sum + self[&[i, l]] * other[&[l, j]];
                                    }
                                    result[&[i, j]] = result[&[i, j]] + sum;
                                }
                            }
                        }
                    }
                }
            }
            MatmulStrategy::Blas => {
                // TODO: 实现BLAS加速
                // 当前使用基础实现
                for i in 0..m {
                    for j in 0..n {
                        let mut sum = T::default();
                        for l in 0..k {
                            sum = sum + self[&[i, l]] * other[&[l, j]];
                        }
                        result[&[i, j]] = sum;
                    }
                }
            }
        }

        Some(result)
    }

    /// 矩阵转置
    pub fn matrix_transpose(&self) -> Option<Self> {
        if self.dims().len() != 2 {
            return None;
        }

        let m = self.dims()[0];
        let n = self.dims()[1];
        let mut result = Self::zeros(vec![n, m]);

        // 分块转置以提高缓存效率
        let block_size = 32;
        for i0 in (0..m).step_by(block_size) {
            let i_end = (i0 + block_size).min(m);
            for j0 in (0..n).step_by(block_size) {
                let j_end = (j0 + block_size).min(n);
                
                for i in i0..i_end {
                    for j in j0..j_end {
                        result[&[j, i]] = self[&[i, j]];
                    }
                }
            }
        }

        Some(result)
    }
}

// 为浮点数类型实现额外的矩阵运算
impl<T: Copy + Default + num_traits::Float> RustArray<T> {
    /// 计算矩阵的 L2 范数（Frobenius范数）
    pub fn matrix_norm(&self) -> Option<T> {
        if self.dims().len() != 2 {
            return None;
        }

        let mut sum = T::zero();
        for i in 0..self.dims()[0] {
            for j in 0..self.dims()[1] {
                let val = self[&[i, j]];
                sum = sum + val * val;
            }
        }

        Some(sum.sqrt())
    }

    /// 计算矩阵的迹（对角线元素之和）
    pub fn matrix_trace(&self) -> Option<T> {
        if self.dims().len() != 2 || self.dims()[0] != self.dims()[1] {
            return None;
        }

        let mut sum = T::zero();
        for i in 0..self.dims()[0] {
            sum = sum + self[&[i, i]];
        }

        Some(sum)
    }

    /// 计算矩阵的对角线元素
    pub fn matrix_diagonal(&self) -> Option<Self> {
        if self.dims().len() != 2 {
            return None;
        }

        let n = self.dims()[0].min(self.dims()[1]);
        let mut result = Self::zeros(vec![n]);

        for i in 0..n {
            result[&[i]] = self[&[i, i]];
        }

        Some(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_matrix_multiplication() {
        // 创建测试矩阵
        let a = RustArray::from_vec(
            vec![1.0f64, 2.0, 3.0, 4.0],
            vec![2, 2]
        ).unwrap();
        let b = RustArray::from_vec(
            vec![2.0f64, 0.0, 1.0, 3.0],
            vec![2, 2]
        ).unwrap();

        // 测试基础矩阵乘法
        let c = a.matmul(&b, Some(MatmulStrategy::Basic)).unwrap();
        assert_eq!(c[&[0, 0]], 4.0);
        assert_eq!(c[&[0, 1]], 6.0);
        assert_eq!(c[&[1, 0]], 10.0);
        assert_eq!(c[&[1, 1]], 12.0);

        // 测试分块矩阵乘法
        let c = a.matmul(&b, Some(MatmulStrategy::Blocked { block_size: 2 })).unwrap();
        assert_eq!(c[&[0, 0]], 4.0);
        assert_eq!(c[&[0, 1]], 6.0);
        assert_eq!(c[&[1, 0]], 10.0);
        assert_eq!(c[&[1, 1]], 12.0);
    }

    #[test]
    fn test_matrix_transpose() {
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0, 4.0],
            vec![2, 2]
        ).unwrap();

        let b = a.matrix_transpose().unwrap();
        assert_eq!(b[&[0, 0]], 1.0);
        assert_eq!(b[&[0, 1]], 3.0);
        assert_eq!(b[&[1, 0]], 2.0);
        assert_eq!(b[&[1, 1]], 4.0);
    }

    #[test]
    fn test_matrix_norm() {
        let a = RustArray::from_vec(
            vec![3.0f32, 0.0, 0.0, 4.0],
            vec![2, 2]
        ).unwrap();

        let norm = a.matrix_norm().unwrap();
        assert!((norm - 5.0).abs() < 1e-6);
    }

    #[test]
    fn test_matrix_trace() {
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0, 4.0],
            vec![2, 2]
        ).unwrap();

        let trace = a.matrix_trace().unwrap();
        assert_eq!(trace, 5.0);
    }

    #[test]
    fn test_matrix_diagonal() {
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0, 4.0],
            vec![2, 2]
        ).unwrap();

        let diag = a.matrix_diagonal().unwrap();
        assert_eq!(diag[&[0]], 1.0);
        assert_eq!(diag[&[1]], 4.0);
    }
}
