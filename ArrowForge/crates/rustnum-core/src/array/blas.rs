use std::sync::atomic::{AtomicBool, Ordering};
use blas::*;
use crate::error::RustNumError;
use super::array_impl::{RustArray, StorageOrder};

/// BLAS操作的阈值大小（低于此大小使用原生实现）
pub(crate) const BLAS_THRESHOLD: usize = 64 * 64;

/// BLAS后端初始化状态
static BLAS_INITIALIZED: AtomicBool = AtomicBool::new(false);

/// 初始化BLAS后端
pub fn initialize_blas() {
    if !BLAS_INITIALIZED.load(Ordering::Relaxed) {
        #[cfg(feature = "blas-openblas")]
        {
            openblas_src::init();
        }
        #[cfg(feature = "blas-mkl")]
        {
            intel_mkl_src::init();
        }
        BLAS_INITIALIZED.store(true, Ordering::Relaxed);
    }
}

/// BLAS矩阵乘法实现
pub unsafe fn blas_matmul<T>(
    a: &RustArray<T>,
    b: &RustArray<T>,
    c: &mut RustArray<T>,
) -> Result<(), RustNumError>
where
    T: Copy + Default,
{
    let (m, k) = (a.shape()[0], a.shape()[1]);
    let n = b.shape()[1];
    
    // 确保BLAS已初始化
    initialize_blas();
    
    match (a.order(), b.order()) {
        (StorageOrder::RowMajor, StorageOrder::RowMajor) => {
            // 需要转置以适应BLAS的列主序
            let a_trans = a.transpose_for_blas()?;
            let b_trans = b.transpose_for_blas()?;
            
            blas_matmul_col_major(&a_trans, &b_trans, c)
        }
        (StorageOrder::ColumnMajor, StorageOrder::ColumnMajor) => {
            blas_matmul_col_major(a, b, c)
        }
        _ => {
            // 混合存储顺序，转换为列主序
            let a_col = a.ensure_column_major()?;
            let b_col = b.ensure_column_major()?;
            
            blas_matmul_col_major(&a_col, &b_col, c)
        }
    }
}

/// 列主序BLAS矩阵乘法
unsafe fn blas_matmul_col_major<T>(
    a: &RustArray<T>,
    b: &RustArray<T>,
    c: &mut RustArray<T>,
) -> Result<(), RustNumError> {
    let (m, k) = (a.shape()[0], a.shape()[1]);
    let n = b.shape()[1];
    
    match std::any::TypeId::of::<T>() {
        t if t == std::any::TypeId::of::<f32>() => {
            let a_ptr = a.as_ptr() as *const f32;
            let b_ptr = b.as_ptr() as *const f32;
            let c_ptr = c.as_mut_ptr() as *mut f32;
            
            sgemm(
                b'N',  // no transpose
                b'N',  // no transpose
                m as i32,
                n as i32,
                k as i32,
                1.0,   // alpha
                a_ptr,
                m as i32,  // leading dimension of A
                b_ptr,
                k as i32,  // leading dimension of B
                0.0,   // beta
                c_ptr,
                m as i32,  // leading dimension of C
            );
        }
        t if t == std::any::TypeId::of::<f64>() => {
            let a_ptr = a.as_ptr() as *const f64;
            let b_ptr = b.as_ptr() as *const f64;
            let c_ptr = c.as_mut_ptr() as *mut f64;
            
            dgemm(
                b'N',  // no transpose
                b'N',  // no transpose
                m as i32,
                n as i32,
                k as i32,
                1.0,   // alpha
                a_ptr,
                m as i32,  // leading dimension of A
                b_ptr,
                k as i32,  // leading dimension of B
                0.0,   // beta
                c_ptr,
                m as i32,  // leading dimension of C
            );
        }
        _ => {
            return Err(RustNumError::OperationNotSupported(
                "BLAS only supports f32 and f64".into()
            ));
        }
    }
    
    Ok(())
}

/// BLAS特征扩展
pub trait BlasExt<T> {
    /// 确保数组是列主序
    fn ensure_column_major(&self) -> Result<RustArray<T>, RustNumError>;
    /// 为BLAS操作转置数组
    fn transpose_for_blas(&self) -> Result<RustArray<T>, RustNumError>;
}

impl<T: Copy> BlasExt<T> for RustArray<T> {
    fn ensure_column_major(&self) -> Result<RustArray<T>, RustNumError> {
        match self.order() {
            StorageOrder::ColumnMajor => Ok(self.clone()),
            StorageOrder::RowMajor => {
                let mut result = RustArray::new_with_order(
                    self.shape().to_vec(),
                    StorageOrder::ColumnMajor
                )?;
                
                // 复制并重排数据
                let (m, n) = (self.shape()[0], self.shape()[1]);
                for i in 0..m {
                    for j in 0..n {
                        result.set(&[i, j], *self.get(&[i, j]).unwrap())?;
                    }
                }
                
                Ok(result)
            }
        }
    }
    
    fn transpose_for_blas(&self) -> Result<RustArray<T>, RustNumError> {
        let mut result = RustArray::new_with_order(
            vec![self.shape()[1], self.shape()[0]],
            StorageOrder::ColumnMajor
        )?;
        
        // 转置并复制数据
        let (m, n) = (self.shape()[0], self.shape()[1]);
        for i in 0..m {
            for j in 0..n {
                result.set(&[j, i], *self.get(&[i, j]).unwrap())?;
            }
        }
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_blas_matmul() {
        // 创建测试矩阵
        let mut a = RustArray::<f64>::new_with_order(
            vec![3, 2],
            StorageOrder::ColumnMajor
        ).unwrap();
        let mut b = RustArray::<f64>::new_with_order(
            vec![2, 4],
            StorageOrder::ColumnMajor
        ).unwrap();
        let mut c = RustArray::<f64>::new_with_order(
            vec![3, 4],
            StorageOrder::ColumnMajor
        ).unwrap();
        
        // 设置测试数据
        a.set(&[0, 0], 1.0).unwrap();
        a.set(&[1, 0], 2.0).unwrap();
        a.set(&[2, 0], 3.0).unwrap();
        a.set(&[0, 1], 4.0).unwrap();
        a.set(&[1, 1], 5.0).unwrap();
        a.set(&[2, 1], 6.0).unwrap();
        
        b.set(&[0, 0], 1.0).unwrap();
        b.set(&[1, 0], 2.0).unwrap();
        b.set(&[0, 1], 3.0).unwrap();
        b.set(&[1, 1], 4.0).unwrap();
        b.set(&[0, 2], 5.0).unwrap();
        b.set(&[1, 2], 6.0).unwrap();
        b.set(&[0, 3], 7.0).unwrap();
        b.set(&[1, 3], 8.0).unwrap();
        
        // 执行BLAS矩阵乘法
        unsafe {
            blas_matmul(&a, &b, &mut c).unwrap();
        }
        
        // 验证结果
        // C = A * B 的结果应该是：
        // [[ 9 19 29 39]
        //  [12 26 40 54]
        //  [15 33 51 69]]
        assert_eq!(*c.get(&[0, 0]).unwrap(), 9.0);
        assert_eq!(*c.get(&[1, 0]).unwrap(), 12.0);
        assert_eq!(*c.get(&[2, 0]).unwrap(), 15.0);
        assert_eq!(*c.get(&[0, 3]).unwrap(), 39.0);
        assert_eq!(*c.get(&[1, 3]).unwrap(), 54.0);
        assert_eq!(*c.get(&[2, 3]).unwrap(), 69.0);
    }
}
