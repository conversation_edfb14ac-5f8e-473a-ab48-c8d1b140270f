use crate::error::RustNumError;
use crate::RustArray;
use std::os::raw::*;

/// 计算LAPACK操作的最优工作空间大小
pub(crate) fn query_optimal_workspace<F>(query_fn: F) -> Result<i32, RustNumError>
where
    F: Fn(*mut f64, i32) -> i32
{
    let mut work = [0.0];
    let info = query_fn(work.as_mut_ptr(), -1);
    
    if info < 0 {
        return Err(RustNumError::LapackError(
            format!("工作空间查询失败: 参数{}非法", -info)
        ));
    }
    
    Ok(work[0] as i32)
}

/// 验证矩阵是否为方阵
pub(crate) fn validate_square_matrix(arr: &RustArray<f64>) -> Result<i32, RustNumError> {
    if arr.ndim() != 2 {
        return Err(RustNumError::DimensionError(
            "需要2D方阵".to_string()
        ));
    }
    
    let (m, n) = (arr.shape()[0] as i32, arr.shape()[1] as i32);
    if m != n {
        return Err(RustNumError::DimensionError(
            format!("需要方阵，但得到了{}x{}矩阵", m, n)
        ));
    }
    
    Ok(n)
}

/// 验证矩阵是否为对称矩阵
pub(crate) fn validate_symmetric_matrix(arr: &RustArray<f64>) -> Result<(), RustNumError> {
    let n = validate_square_matrix(arr)?;
    
    for i in 0..n as usize {
        for j in 0..i {
            if (arr[[i, j]] - arr[[j, i]]).abs() > 1e-10 {
                return Err(RustNumError::ValueError(
                    format!("矩阵不是对称的：A[{},{}] != A[{},{}]", i, j, j, i)
                ));
            }
        }
    }
    
    Ok(())
}

/// 检查矩阵是否为正定矩阵
pub(crate) fn is_positive_definite(arr: &RustArray<f64>) -> bool {
    if let Ok(_) = arr.cholesky() {
        true
    } else {
        false
    }
}

/// 优化的矩阵乘法分块大小
pub(crate) fn optimal_block_size(n: usize) -> usize {
    // 基于缓存大小选择分块大小
    // 假设L1缓存为32KB，每个f64占8字节
    const L1_CACHE_SIZE: usize = 32 * 1024;
    const SIZEOF_F64: usize = 8;
    
    let block_size = (L1_CACHE_SIZE / (3 * SIZEOF_F64)).sqrt();
    block_size.min(n)
}
