use std::ops::{Add, Mul};
use blas::*;
use crate::error::RustNumError;
use super::array_impl::{RustArray, StorageOrder};
use super::blas::BLAS_THRESHOLD;

/// BLAS Level 1 操作特征
pub trait BlasLevel1<T> {
    /// 向量点积 (dot product)
    fn dot(&self, other: &Self) -> Result<T, RustNumError>;
    
    /// 向量范数 (vector norm)
    fn norm(&self) -> Result<T, RustNumError>;
    
    /// 向量缩放 (scale)
    fn scale(&self, alpha: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 向量加法 (axpy: y = alpha * x + y)
    fn axpy(&self, alpha: T, y: &mut Self) -> Result<(), RustNumError>;
}

impl<T: Copy + Default + Add<Output = T> + Mul<Output = T>> BlasLevel1<T> for RustArray<T> {
    fn dot(&self, other: &Self) -> Result<T, RustNumError> {
        // 检查维度
        if self.shape() != other.shape() || self.shape().len() != 1 {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let n = self.shape()[0];
        
        // 对于较大的向量使用BLAS
        if n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                 std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_dot(other);
            }
        }
        
        // 原生实现
        let mut result = T::default();
        for i in 0..n {
            result = result + (*self.get(&[i]).unwrap() * *other.get(&[i]).unwrap());
        }
        
        Ok(result)
    }
    
    fn norm(&self) -> Result<T, RustNumError> {
        if self.shape().len() != 1 {
            return Err(RustNumError::OperationNotSupported(
                "Norm operation requires 1D array".into()
            ));
        }
        
        let n = self.shape()[0];
        
        // 对于较大的向量使用BLAS
        if n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                 std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_nrm2();
            }
        }
        
        // 原生实现
        self.dot(self).map(|x| x)
    }
    
    fn scale(&self, alpha: T) -> Result<Self, RustNumError> {
        if self.shape().len() != 1 {
            return Err(RustNumError::OperationNotSupported(
                "Scale operation requires 1D array".into()
            ));
        }
        
        let n = self.shape()[0];
        let mut result = Self::new(self.shape().to_vec())?;
        
        // 对于较大的向量使用BLAS
        if n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                 std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                self.blas_scal(alpha, &mut result)?;
                return Ok(result);
            }
        }
        
        // 原生实现
        for i in 0..n {
            result.set(&[i], *self.get(&[i]).unwrap() * alpha)?;
        }
        
        Ok(result)
    }
    
    fn axpy(&self, alpha: T, y: &mut Self) -> Result<(), RustNumError> {
        if self.shape() != y.shape() || self.shape().len() != 1 {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: y.shape().to_vec(),
            });
        }
        
        let n = self.shape()[0];
        
        // 对于较大的向量使用BLAS
        if n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                 std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_axpy(alpha, y);
            }
        }
        
        // 原生实现
        for i in 0..n {
            let xi = *self.get(&[i]).unwrap();
            let yi = *y.get(&[i]).unwrap();
            y.set(&[i], alpha * xi + yi)?;
        }
        
        Ok(())
    }
}

/// BLAS Level 1 内部实现
impl<T> RustArray<T> {
    /// BLAS dot实现
    unsafe fn blas_dot(&self, other: &Self) -> Result<T, RustNumError> {
        let n = self.shape()[0] as i32;
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                let result = sdot(
                    n,
                    self.as_ptr() as *const f32,
                    1,
                    other.as_ptr() as *const f32,
                    1,
                );
                Ok(std::mem::transmute_copy(&result))
            }
            t if t == std::any::TypeId::of::<f64>() => {
                let result = ddot(
                    n,
                    self.as_ptr() as *const f64,
                    1,
                    other.as_ptr() as *const f64,
                    1,
                );
                Ok(std::mem::transmute_copy(&result))
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
    
    /// BLAS nrm2实现
    unsafe fn blas_nrm2(&self) -> Result<T, RustNumError> {
        let n = self.shape()[0] as i32;
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                let result = snrm2(
                    n,
                    self.as_ptr() as *const f32,
                    1,
                );
                Ok(std::mem::transmute_copy(&result))
            }
            t if t == std::any::TypeId::of::<f64>() => {
                let result = dnrm2(
                    n,
                    self.as_ptr() as *const f64,
                    1,
                );
                Ok(std::mem::transmute_copy(&result))
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
    
    /// BLAS scal实现
    unsafe fn blas_scal(&self, alpha: T, result: &mut Self) -> Result<(), RustNumError> {
        let n = self.shape()[0] as i32;
        
        // 先复制数据
        std::ptr::copy_nonoverlapping(
            self.as_ptr(),
            result.as_mut_ptr(),
            n as usize,
        );
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                sscal(
                    n,
                    std::mem::transmute_copy(&alpha),
                    result.as_mut_ptr() as *mut f32,
                    1,
                );
                Ok(())
            }
            t if t == std::any::TypeId::of::<f64>() => {
                dscal(
                    n,
                    std::mem::transmute_copy(&alpha),
                    result.as_mut_ptr() as *mut f64,
                    1,
                );
                Ok(())
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
    
    /// BLAS axpy实现
    unsafe fn blas_axpy(&self, alpha: T, y: &mut Self) -> Result<(), RustNumError> {
        let n = self.shape()[0] as i32;
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                saxpy(
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f32,
                    1,
                    y.as_mut_ptr() as *mut f32,
                    1,
                );
                Ok(())
            }
            t if t == std::any::TypeId::of::<f64>() => {
                daxpy(
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f64,
                    1,
                    y.as_mut_ptr() as *mut f64,
                    1,
                );
                Ok(())
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_vector_operations() {
        // 创建测试向量
        let mut v1 = RustArray::<f64>::new(vec![3]).unwrap();
        let mut v2 = RustArray::<f64>::new(vec![3]).unwrap();
        
        // 设置测试数据
        v1.set(&[0], 1.0).unwrap();
        v1.set(&[1], 2.0).unwrap();
        v1.set(&[2], 3.0).unwrap();
        
        v2.set(&[0], 4.0).unwrap();
        v2.set(&[1], 5.0).unwrap();
        v2.set(&[2], 6.0).unwrap();
        
        // 测试点积
        let dot_result = v1.dot(&v2).unwrap();
        assert_eq!(dot_result, 32.0);  // 1*4 + 2*5 + 3*6 = 32
        
        // 测试范数
        let norm_result = v1.norm().unwrap();
        assert!((norm_result - 14.0_f64.sqrt()).abs() < 1e-10);
        
        // 测试缩放
        let scaled = v1.scale(2.0).unwrap();
        assert_eq!(*scaled.get(&[0]).unwrap(), 2.0);
        assert_eq!(*scaled.get(&[1]).unwrap(), 4.0);
        assert_eq!(*scaled.get(&[2]).unwrap(), 6.0);
        
        // 测试axpy
        let mut y = v2.clone();
        v1.axpy(2.0, &mut y).unwrap();
        assert_eq!(*y.get(&[0]).unwrap(), 6.0);   // 2*1 + 4
        assert_eq!(*y.get(&[1]).unwrap(), 9.0);   // 2*2 + 5
        assert_eq!(*y.get(&[2]).unwrap(), 12.0);  // 2*3 + 6
    }
}
