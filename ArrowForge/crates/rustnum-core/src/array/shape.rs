use std::fmt;
use std::ops::Range;

/// 数组布局类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum Layout {
    /// 行优先（C风格）
    RowMajor,
    /// 列优先（Fortran风格）
    ColumnMajor,
}

impl Default for Layout {
    fn default() -> Self {
        Layout::RowMajor
    }
}

/// 数组形状
#[derive(Debug, Clone, PartialEq)]
pub struct Shape {
    /// 各维度大小
    dims: Vec<usize>,
    /// 步长（每个维度上相邻元素的间隔）
    strides: Vec<isize>,
    /// 总元素数量
    size: usize,
    /// 内存布局
    layout: Layout,
}

impl Shape {
    /// 创建新的形状
    pub fn new(dims: Vec<usize>, layout: Layout) -> Self {
        let size = dims.iter().product();
        let strides = Self::compute_strides(&dims, layout);
        
        Self {
            dims,
            strides,
            size,
            layout,
        }
    }

    /// 计算步长
    fn compute_strides(dims: &[usize], layout: Layout) -> Vec<isize> {
        let ndim = dims.len();
        let mut strides = vec![0; ndim];

        match layout {
            Layout::RowMajor => {
                let mut stride = 1;
                for i in (0..ndim).rev() {
                    strides[i] = stride as isize;
                    stride *= dims[i];
                }
            }
            Layout::ColumnMajor => {
                let mut stride = 1;
                for i in 0..ndim {
                    strides[i] = stride as isize;
                    stride *= dims[i];
                }
            }
        }

        strides
    }

    /// 获取维度数量
    pub fn ndim(&self) -> usize {
        self.dims.len()
    }

    /// 获取总元素数量
    pub fn size(&self) -> usize {
        self.size
    }

    /// 获取指定维度的大小
    pub fn dim(&self, axis: usize) -> usize {
        self.dims[axis]
    }

    /// 获取所有维度
    pub fn dims(&self) -> &[usize] {
        &self.dims
    }

    /// 获取步长
    pub fn strides(&self) -> &[isize] {
        &self.strides
    }

    /// 获取布局
    pub fn layout(&self) -> Layout {
        self.layout
    }

    /// 检查形状是否连续
    pub fn is_contiguous(&self) -> bool {
        let expected = Self::compute_strides(&self.dims, self.layout);
        self.strides == expected
    }

    /// 计算元素的线性索引
    pub fn get_linear_index_from_indices(&self, indices: &[usize]) -> Option<usize> {
        if indices.len() != self.ndim() {
            return None;
        }

        for (i, &idx) in indices.iter().enumerate() {
            if idx >= self.dims[i] {
                return None;
            }
        }

        let mut offset = 0isize;
        for (i, &idx) in indices.iter().enumerate() {
            offset += idx as isize * self.strides[i];
        }

        Some(offset as usize)
    }

    /// 从线性索引获取多维索引
    pub fn get_indices(&self, linear_idx: usize) -> Option<Vec<usize>> {
        if linear_idx >= self.size {
            return None;
        }

        let mut indices = vec![0; self.ndim()];
        let mut remaining = linear_idx;

        match self.layout {
            Layout::RowMajor => {
                for i in (0..self.ndim()).rev() {
                    indices[i] = remaining % self.dims[i];
                    remaining /= self.dims[i];
                }
            }
            Layout::ColumnMajor => {
                for i in 0..self.ndim() {
                    indices[i] = remaining % self.dims[i];
                    remaining /= self.dims[i];
                }
            }
        }

        Some(indices)
    }

    /// 创建形状的视图
    pub fn view(&self, ranges: &[Range<usize>]) -> Option<Shape> {
        let (new_shape, _) = self.view_and_strides(ranges)?;
        Some(new_shape)
    }

    /// 创建形状的视图并返回新的形状和步长
    pub fn view_and_strides(&self, ranges: &[Range<usize>]) -> Option<(Shape, Vec<isize>)> {
        if ranges.len() != self.ndim() {
            return None;
        }

        let mut new_dims = Vec::with_capacity(self.ndim());
        let mut new_strides = Vec::with_capacity(self.ndim());

        for (i, range) in ranges.iter().enumerate() {
            if range.end > self.dims[i] {
                return None;
            }
            new_dims.push(range.end - range.start);
            new_strides.push(self.strides[i]);
        }

        Some((
            Shape {
                dims: new_dims,
                strides: new_strides.clone(),
                size: new_dims.iter().product(),
                layout: self.layout,
            },
            new_strides,
        ))
    }

    /// 转置形状
    pub fn transpose(&self, axes: Option<&[usize]>) -> Option<Shape> {
        let (new_shape, _) = self.transpose_and_strides(axes)?;
        Some(new_shape)
    }

    /// 转置形状并返回新的形状和步长
    pub fn transpose_and_strides(&self, axes: Option<&[usize]>) -> Option<(Shape, Vec<isize>)> {
        let ndim = self.ndim();
        let axes = match axes {
            Some(axes) => {
                if axes.len() != ndim {
                    return None;
                }
                // 验证轴的有效性
                let mut used = vec![false; ndim];
                for &axis in axes {
                    if axis >= ndim || used[axis] {
                        return None;
                    }
                    used[axis] = true;
                }
                axes.to_vec()
            }
            None => (0..ndim).rev().collect(),
        };

        let mut new_dims = vec![0; ndim];
        let mut new_strides = vec![0; ndim];

        for (i, &axis) in axes.iter().enumerate() {
            new_dims[i] = self.dims[axis];
            new_strides[i] = self.strides[axis];
        }

        Some(Shape {
            dims: new_dims,
            strides: new_strides,
            size: self.size,
            layout: if self.is_contiguous() {
                match self.layout {
                    Layout::RowMajor => Layout::ColumnMajor,
                    Layout::ColumnMajor => Layout::RowMajor,
                }
            } else {
                self.layout
            },
        })
    }

    /// 重塑形状
    pub fn reshape(&self, new_dims: &[usize]) -> Option<Shape> {
        let new_size: usize = new_dims.iter().product();
        if new_size != self.size {
            return None;
        }

        // 只有连续的数组才能被重塑
        if !self.is_contiguous() {
            return None;
        }

        Some(Shape::new(new_dims.to_vec(), self.layout))
    }
}

impl fmt::Display for Shape {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Shape({:?}, layout={:?})", self.dims, self.layout)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_shape_creation() {
        let shape = Shape::new(vec![2, 3, 4], Layout::RowMajor);
        assert_eq!(shape.ndim(), 3);
        assert_eq!(shape.size(), 24);
        assert_eq!(shape.dims(), &[2, 3, 4]);
        assert!(shape.is_contiguous());
    }

    #[test]
    fn test_strides_computation() {
        // 行优先
        let shape = Shape::new(vec![2, 3, 4], Layout::RowMajor);
        assert_eq!(shape.strides(), &[12, 4, 1]);

        // 列优先
        let shape = Shape::new(vec![2, 3, 4], Layout::ColumnMajor);
        assert_eq!(shape.strides(), &[1, 2, 6]);
    }

    #[test]
    fn test_linear_indexing() {
        let shape = Shape::new(vec![2, 3], Layout::RowMajor);
        
        // 测试有效索引
        assert_eq!(shape.get_linear_index(&[0, 0]), Some(0));
        assert_eq!(shape.get_linear_index(&[0, 1]), Some(1));
        assert_eq!(shape.get_linear_index(&[1, 0]), Some(3));
        
        // 测试无效索引
        assert_eq!(shape.get_linear_index(&[2, 0]), None);
        assert_eq!(shape.get_linear_index(&[0, 3]), None);
    }

    #[test]
    fn test_multi_index() {
        let shape = Shape::new(vec![2, 3], Layout::RowMajor);
        
        // 测试有效索引
        assert_eq!(shape.get_indices(0), Some(vec![0, 0]));
        assert_eq!(shape.get_indices(1), Some(vec![0, 1]));
        assert_eq!(shape.get_indices(3), Some(vec![1, 0]));
        
        // 测试无效索引
        assert_eq!(shape.get_indices(6), None);
    }

    #[test]
    fn test_view() {
        let shape = Shape::new(vec![4, 4], Layout::RowMajor);
        
        // 测试有效视图
        let view = shape.view(&[0..2, 1..3]).unwrap();
        assert_eq!(view.dims(), &[2, 2]);
        assert_eq!(view.size(), 4);
        
        // 测试无效视图
        assert!(shape.view(&[0..5, 0..2]).is_none());
    }

    #[test]
    fn test_transpose() {
        let shape = Shape::new(vec![2, 3, 4], Layout::RowMajor);
        
        // 默认转置
        let transposed = shape.transpose(None).unwrap();
        assert_eq!(transposed.dims(), &[4, 3, 2]);
        
        // 自定义轴转置
        let custom = shape.transpose(Some(&[1, 0, 2])).unwrap();
        assert_eq!(custom.dims(), &[3, 2, 4]);
    }

    #[test]
    fn test_reshape() {
        let shape = Shape::new(vec![2, 3, 4], Layout::RowMajor);
        
        // 测试有效重塑
        let reshaped = shape.reshape(&[4, 6]).unwrap();
        assert_eq!(reshaped.size(), 24);
        assert_eq!(reshaped.dims(), &[4, 6]);
        
        // 测试无效重塑
        assert!(shape.reshape(&[5, 5]).is_none());
    }
}
