#[cfg(test)]
mod tests {
    use super::*;
    use crate::RustArray;
    
    #[test]
    fn test_solve_general() {
        // 创建一个线性方程组: Ax = b
        // A = [2 1; 1 3], b = [4; 5]
        let a = RustArray::from_slice(&[
            2.0, 1.0,
            1.0, 3.0
        ], &[2, 2]);
        
        let b = RustArray::from_slice(&[4.0, 5.0], &[2]);
        
        let x = a.solve(&b).unwrap();
        
        // 验证解
        // 预期解: x ≈ [1.0, 1.0]
        assert!((x[0] - 1.0).abs() < 1e-10);
        assert!((x[1] - 1.0).abs() < 1e-10);
        
        // 验证 Ax = b
        let b_computed = a.matmul(&x);
        assert!(b_computed.allclose(&b, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_solve_multi() {
        // 创建多个右端项的线性方程组: AX = B
        let a = RustArray::from_slice(&[
            2.0, 1.0,
            1.0, 3.0
        ], &[2, 2]);
        
        let b = RustArray::from_slice(&[
            4.0, 2.0,
            5.0, 4.0
        ], &[2, 2]);
        
        let x = a.solve_multi(&b).unwrap();
        
        // 验证 AX = B
        let b_computed = a.matmul(&x);
        assert!(b_computed.allclose(&b, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_solve_spd() {
        // 创建一个对称正定矩阵
        let a = RustArray::from_slice(&[
            2.0, -1.0,
            -1.0, 2.0
        ], &[2, 2]);
        
        let b = RustArray::from_slice(&[1.0, 0.0], &[2]);
        
        let x = a.solve_spd(&b).unwrap();
        
        // 验证 Ax = b
        let b_computed = a.matmul(&x);
        assert!(b_computed.allclose(&b, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_solve_lstsq() {
        // 创建一个超定方程组
        let a = RustArray::from_slice(&[
            1.0, 1.0,
            1.0, 2.0,
            1.0, 3.0
        ], &[3, 2]);
        
        let b = RustArray::from_slice(&[2.0, 4.0, 6.0], &[3]);
        
        let x = a.solve_lstsq(&b).unwrap();
        
        // 验证最小二乘解的维度
        assert_eq!(x.shape(), &[2]);
        
        // 验证残差最小
        let residual = &b - &a.matmul(&x);
        let norm = residual.dot(&residual);
        
        // 尝试扰动解，验证是否为最小值
        let eps = 1e-6;
        let x_perturbed = RustArray::from_slice(&[
            x[0] + eps,
            x[1] + eps
        ], &[2]);
        let residual_perturbed = &b - &a.matmul(&x_perturbed);
        let norm_perturbed = residual_perturbed.dot(&residual_perturbed);
        
        assert!(norm <= norm_perturbed);
    }
    
    #[test]
    #[should_panic(expected = "矩阵不是正定的")]
    fn test_solve_spd_non_positive_definite() {
        // 创建一个非正定矩阵
        let a = RustArray::from_slice(&[
            1.0, 2.0,
            2.0, 1.0
        ], &[2, 2]);
        
        let b = RustArray::from_slice(&[1.0, 1.0], &[2]);
        
        // 这应该会失败，因为矩阵不是正定的
        a.solve_spd(&b).unwrap();
    }
    
    #[test]
    #[should_panic(expected = "方程组无解")]
    fn test_solve_singular() {
        // 创建一个奇异矩阵
        let a = RustArray::from_slice(&[
            1.0, 1.0,
            1.0, 1.0
        ], &[2, 2]);
        
        let b = RustArray::from_slice(&[1.0, 2.0], &[2]);
        
        // 这应该会失败，因为矩阵是奇异的
        a.solve(&b).unwrap();
    }
}
