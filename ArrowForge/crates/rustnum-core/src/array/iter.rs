use crate::RustArray;

#[cfg(feature = "parallel")]
use rayon::prelude::*;

/// A parallel iterator for RustArray
#[cfg(feature = "parallel")]
pub struct ParIter<'a, T: Sync> {
    array: &'a RustArray<T>,
}

#[cfg(feature = "parallel")]
impl<'a, T: Sync> IntoParallelIterator for &'a RustArray<T> {
    type Item = &'a T;
    type Iter = rayon::slice::Iter<'a, T>;

    fn into_par_iter(self) -> Self::Iter {
        // This is a simplification. A real implementation would need to handle strides.
        self.data.as_slice().par_iter()
    }
}

use crate::array::shape::Shape;
pub struct ArrayView<'a, T> {
    pub(crate) data: &'a [T],
    pub(crate) shape: &'a Shape,
    pub(crate) strides: &'a [isize],
}