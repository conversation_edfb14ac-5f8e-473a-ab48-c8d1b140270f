use std::ops::Mul;
use crate::error::RustNumError;
use super::array_impl::{RustArray, ArrayView, StorageOrder};

/// 线性代数操作特征
pub trait LinearAlgebra<T> {
    /// 矩阵乘法
    fn matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 矩阵转置
    fn transpose(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 矩阵求逆
    fn inv(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl<T> LinearAlgebra<T> for RustArray<T>
where
    T: Copy + Mul<Output = T> + std::iter::Sum + Default,
{
    fn matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        // 检查矩阵维度是否匹配
        if self.shape().len() != 2 || other.shape().len() != 2 {
            return Err(RustNumError::OperationNotSupported(
                "Matrix multiplication requires 2D arrays".into()
            ));
        }
        
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let (k2, n) = (other.shape()[0], other.shape()[1]);
        
        if k != k2 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![k, n],
                got: vec![k2, n],
            });
        }
        
        // 创建结果矩阵（使用列主序以优化BLAS操作）
        let mut result = Self::new_with_order(vec![m, n], StorageOrder::ColumnMajor)?;
        
        // 对于较大的矩阵使用BLAS
        if m * k * n >= super::blas::BLAS_THRESHOLD {
            unsafe {
                super::blas::blas_matmul(self, other, &mut result)?;
                return Ok(result);
            }
        }
        
        // 对于小矩阵使用原生实现
        match (self.order(), other.order()) {
            (StorageOrder::RowMajor, StorageOrder::RowMajor) => {
                unsafe {
                    self.matmul_row_major(other, &mut result)?;
                }
            }
            (StorageOrder::ColumnMajor, StorageOrder::ColumnMajor) => {
                unsafe {
                    self.matmul_col_major(other, &mut result)?;
                }
            }
            _ => {
                self.matmul_generic(other, &mut result)?;
            }
        }
        
        Ok(result)
    }
    
    fn transpose(&self) -> Result<Self, RustNumError> {
        if self.shape().len() != 2 {
            return Err(RustNumError::OperationNotSupported(
                "Transpose requires 2D array".into()
            ));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        let mut result = Self::new(vec![n, m])?;
        
        // 如果原数组是连续存储的，使用优化的实现
        if self.is_contiguous() {
            unsafe {
                self.transpose_contiguous(&mut result)?;
            }
        } else {
            // 使用通用实现
            self.transpose_generic(&mut result)?;
        }
        
        Ok(result)
    }
    
    fn inv(&self) -> Result<Self, RustNumError> {
        // TODO: 实现矩阵求逆
        // 可以使用LU分解或其他方法
        unimplemented!("Matrix inversion not implemented yet");
    }
}

impl<T> RustArray<T>
where
    T: Copy + Mul<Output = T> + std::iter::Sum + Default,
{
    /// 行主序矩阵乘法实现（优化版本）
    unsafe fn matmul_row_major(&self, other: &Self, result: &mut Self) -> Result<(), RustNumError> {
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let n = other.shape()[1];
        
        // 使用分块技术提高缓存效率
        const BLOCK_SIZE: usize = 32;
        
        for i0 in (0..m).step_by(BLOCK_SIZE) {
            let imax = (i0 + BLOCK_SIZE).min(m);
            for j0 in (0..n).step_by(BLOCK_SIZE) {
                let jmax = (j0 + BLOCK_SIZE).min(n);
                for k0 in (0..k).step_by(BLOCK_SIZE) {
                    let kmax = (k0 + BLOCK_SIZE).min(k);
                    
                    // 计算当前块
                    for i in i0..imax {
                        for j in j0..jmax {
                            let mut sum = T::default();
                            for k in k0..kmax {
                                sum = sum + (*self.get(&[i, k]).unwrap() * 
                                           *other.get(&[k, j]).unwrap());
                            }
                            *result.get_mut(&[i, j]).unwrap() = 
                                *result.get(&[i, j]).unwrap() + sum;
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 列主序矩阵乘法实现（优化版本）
    unsafe fn matmul_col_major(&self, other: &Self, result: &mut Self) -> Result<(), RustNumError> {
        // TODO: 实现列主序优化版本
        self.matmul_generic(other, result)
    }
    
    /// 通用矩阵乘法实现
    fn matmul_generic(&self, other: &Self, result: &mut Self) -> Result<(), RustNumError> {
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let n = other.shape()[1];
        
        for i in 0..m {
            for j in 0..n {
                let mut sum = T::default();
                for kk in 0..k {
                    sum = sum + (*self.get(&[i, kk]).unwrap() * 
                               *other.get(&[kk, j]).unwrap());
                }
                result.set(&[i, j], sum)?;
            }
        }
        
        Ok(())
    }
    
    /// 连续存储的转置实现
    unsafe fn transpose_contiguous(&self, result: &mut Self) -> Result<(), RustNumError> {
        let (m, n) = (self.shape()[0], self.shape()[1]);
        
        // 使用分块技术提高缓存效率
        const BLOCK_SIZE: usize = 32;
        
        for i0 in (0..m).step_by(BLOCK_SIZE) {
            let imax = (i0 + BLOCK_SIZE).min(m);
            for j0 in (0..n).step_by(BLOCK_SIZE) {
                let jmax = (j0 + BLOCK_SIZE).min(n);
                
                for i in i0..imax {
                    for j in j0..jmax {
                        *result.get_mut(&[j, i]).unwrap() = *self.get(&[i, j]).unwrap();
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 通用转置实现
    fn transpose_generic(&self, result: &mut Self) -> Result<(), RustNumError> {
        let (m, n) = (self.shape()[0], self.shape()[1]);
        
        for i in 0..m {
            for j in 0..n {
                result.set(&[j, i], *self.get(&[i, j]).unwrap())?;
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_matrix_multiply() {
        // 创建两个2x2矩阵
        let mut a = RustArray::<f64>::new(vec![2, 2]).unwrap();
        let mut b = RustArray::<f64>::new(vec![2, 2]).unwrap();
        
        // 设置测试数据
        a.set(&[0, 0], 1.0).unwrap();
        a.set(&[0, 1], 2.0).unwrap();
        a.set(&[1, 0], 3.0).unwrap();
        a.set(&[1, 1], 4.0).unwrap();
        
        b.set(&[0, 0], 1.0).unwrap();
        b.set(&[0, 1], 0.0).unwrap();
        b.set(&[1, 0], 0.0).unwrap();
        b.set(&[1, 1], 1.0).unwrap();
        
        let result = a.matmul(&b).unwrap();
        
        // 验证结果
        assert_eq!(*result.get(&[0, 0]).unwrap(), 1.0);
        assert_eq!(*result.get(&[0, 1]).unwrap(), 2.0);
        assert_eq!(*result.get(&[1, 0]).unwrap(), 3.0);
        assert_eq!(*result.get(&[1, 1]).unwrap(), 4.0);
    }
    
    #[test]
    fn test_matrix_transpose() {
        let mut a = RustArray::<f64>::new(vec![2, 3]).unwrap();
        
        // 设置测试数据
        for i in 0..2 {
            for j in 0..3 {
                a.set(&[i, j], (i * 3 + j) as f64).unwrap();
            }
        }
        
        let result = a.transpose().unwrap();
        
        // 验证结果
        for i in 0..3 {
            for j in 0..2 {
                assert_eq!(*result.get(&[i, j]).unwrap(), *a.get(&[j, i]).unwrap());
            }
        }
    }
}
