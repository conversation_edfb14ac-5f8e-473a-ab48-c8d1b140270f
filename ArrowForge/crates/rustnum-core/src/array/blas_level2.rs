use std::ops::{Add, Mul};
use blas::*;
use crate::error::RustNumError;
use super::array_impl::{RustArray, StorageOrder};
use super::blas::BLAS_THRESHOLD;

/// BLAS Level 2 操作特征
pub trait BlasLevel2<T> {
    /// 矩阵-向量乘法 (gemv: y = alpha * A * x + beta * y)
    fn gemv(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError>;
    
    /// 矩阵-向量乘法（带转置）
    fn gemv_t(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError>;
    
    /// 对称矩阵-向量乘法
    fn symv(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError>;
}

impl<T: Copy + Default + Add<Output = T> + Mul<Output = T>> BlasLevel2<T> for RustArray<T> {
    fn gemv(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError> {
        // 检查维度
        if self.shape().len() != 2 || x.shape().len() != 1 || y.shape().len() != 1 {
            return Err(RustNumError::OperationNotSupported(
                "GEMV requires matrix and vectors".into()
            ));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        if n != x.shape()[0] || m != y.shape()[0] {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n],
                got: x.shape().to_vec(),
            });
        }
        
        // 对于较大的矩阵使用BLAS
        if m * n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                     std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_gemv(x, alpha, y, beta, false);
            }
        }
        
        // 原生实现
        let mut temp = vec![T::default(); m];
        for i in 0..m {
            for j in 0..n {
                temp[i] = temp[i] + *self.get(&[i, j]).unwrap() * *x.get(&[j]).unwrap();
            }
        }
        
        for i in 0..m {
            y.set(&[i], alpha * temp[i] + beta * *y.get(&[i]).unwrap())?;
        }
        
        Ok(())
    }
    
    fn gemv_t(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError> {
        // 检查维度
        if self.shape().len() != 2 || x.shape().len() != 1 || y.shape().len() != 1 {
            return Err(RustNumError::OperationNotSupported(
                "GEMV requires matrix and vectors".into()
            ));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        if m != x.shape()[0] || n != y.shape()[0] {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![m],
                got: x.shape().to_vec(),
            });
        }
        
        // 对于较大的矩阵使用BLAS
        if m * n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                     std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_gemv(x, alpha, y, beta, true);
            }
        }
        
        // 原生实现
        let mut temp = vec![T::default(); n];
        for j in 0..n {
            for i in 0..m {
                temp[j] = temp[j] + *self.get(&[i, j]).unwrap() * *x.get(&[i]).unwrap();
            }
        }
        
        for j in 0..n {
            y.set(&[j], alpha * temp[j] + beta * *y.get(&[j]).unwrap())?;
        }
        
        Ok(())
    }
    
    fn symv(&self, x: &Self, alpha: T, y: &mut Self, beta: T) -> Result<(), RustNumError> {
        // 检查维度
        if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
            return Err(RustNumError::OperationNotSupported(
                "SYMV requires square matrix".into()
            ));
        }
        
        let n = self.shape()[0];
        if n != x.shape()[0] || n != y.shape()[0] {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n],
                got: x.shape().to_vec(),
            });
        }
        
        // 对于较大的矩阵使用BLAS
        if n * n >= BLAS_THRESHOLD && (std::any::TypeId::of::<T>() == std::any::TypeId::of::<f32>() ||
                                     std::any::TypeId::of::<T>() == std::any::TypeId::of::<f64>()) {
            unsafe {
                return self.blas_symv(x, alpha, y, beta);
            }
        }
        
        // 原生实现
        let mut temp = vec![T::default(); n];
        for i in 0..n {
            for j in 0..n {
                temp[i] = temp[i] + *self.get(&[i, j]).unwrap() * *x.get(&[j]).unwrap();
            }
        }
        
        for i in 0..n {
            y.set(&[i], alpha * temp[i] + beta * *y.get(&[i]).unwrap())?;
        }
        
        Ok(())
    }
}

/// BLAS Level 2 内部实现
impl<T> RustArray<T> {
    /// BLAS gemv实现
    unsafe fn blas_gemv(
        &self,
        x: &Self,
        alpha: T,
        y: &mut Self,
        beta: T,
        trans: bool,
    ) -> Result<(), RustNumError> {
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        let trans = if trans { b'T' } else { b'N' };
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                sgemv(
                    trans,
                    m,
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f32,
                    m,
                    x.as_ptr() as *const f32,
                    1,
                    std::mem::transmute_copy(&beta),
                    y.as_mut_ptr() as *mut f32,
                    1,
                );
                Ok(())
            }
            t if t == std::any::TypeId::of::<f64>() => {
                dgemv(
                    trans,
                    m,
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f64,
                    m,
                    x.as_ptr() as *const f64,
                    1,
                    std::mem::transmute_copy(&beta),
                    y.as_mut_ptr() as *mut f64,
                    1,
                );
                Ok(())
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
    
    /// BLAS symv实现
    unsafe fn blas_symv(
        &self,
        x: &Self,
        alpha: T,
        y: &mut Self,
        beta: T,
    ) -> Result<(), RustNumError> {
        let n = self.shape()[0] as i32;
        
        match std::any::TypeId::of::<T>() {
            t if t == std::any::TypeId::of::<f32>() => {
                ssymv(
                    b'U',
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f32,
                    n,
                    x.as_ptr() as *const f32,
                    1,
                    std::mem::transmute_copy(&beta),
                    y.as_mut_ptr() as *mut f32,
                    1,
                );
                Ok(())
            }
            t if t == std::any::TypeId::of::<f64>() => {
                dsymv(
                    b'U',
                    n,
                    std::mem::transmute_copy(&alpha),
                    self.as_ptr() as *const f64,
                    n,
                    x.as_ptr() as *const f64,
                    1,
                    std::mem::transmute_copy(&beta),
                    y.as_mut_ptr() as *mut f64,
                    1,
                );
                Ok(())
            }
            _ => Err(RustNumError::OperationNotSupported(
                "BLAS operations only support f32 and f64".into()
            )),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_matrix_vector_operations() {
        // 创建测试矩阵和向量
        let mut a = RustArray::<f64>::new(vec![2, 3]).unwrap();
        let mut x = RustArray::<f64>::new(vec![3]).unwrap();
        let mut y = RustArray::<f64>::new(vec![2]).unwrap();
        
        // 设置测试数据
        a.set(&[0, 0], 1.0).unwrap();
        a.set(&[0, 1], 2.0).unwrap();
        a.set(&[0, 2], 3.0).unwrap();
        a.set(&[1, 0], 4.0).unwrap();
        a.set(&[1, 1], 5.0).unwrap();
        a.set(&[1, 2], 6.0).unwrap();
        
        x.set(&[0], 1.0).unwrap();
        x.set(&[1], 2.0).unwrap();
        x.set(&[2], 3.0).unwrap();
        
        y.set(&[0], 0.0).unwrap();
        y.set(&[1], 0.0).unwrap();
        
        // 测试矩阵-向量乘法
        a.gemv(&x, 1.0, &mut y, 0.0).unwrap();
        
        // 验证结果
        assert_eq!(*y.get(&[0]).unwrap(), 14.0);  // 1*1 + 2*2 + 3*3
        assert_eq!(*y.get(&[1]).unwrap(), 32.0);  // 4*1 + 5*2 + 6*3
    }
}
