use super::*;
use crate::error::Result;
use num_traits::Float;


/// 矩阵分块优化器
pub struct BlockOptimizer {
    /// 最小分块大小（根据CPU缓存大小自动调整）
    min_block_size: usize,
    /// 最大分块大小
    max_block_size: usize,
    /// L1缓存大小（字节）
    l1_cache_size: usize,
    /// L2缓存大小（字节）
    l2_cache_size: usize,
}

impl Default for BlockOptimizer {
    fn default() -> Self {
        // 默认配置基于典型的现代CPU架构
        Self {
            min_block_size: 32,
            max_block_size: 256,
            l1_cache_size: 32 * 1024,     // 32KB
            l2_cache_size: 256 * 1024,    // 256KB
        }
    }
}

impl BlockOptimizer {
    /// 创建新的优化器实例
    pub fn new(min_size: usize, max_size: usize, l1_size: usize, l2_size: usize) -> Self {
        Self {
            min_block_size: min_size,
            max_block_size: max_size,
            l1_cache_size: l1_size,
            l2_cache_size: l2_size,
        }
    }

    /// 自动检测CPU缓存配置
    pub fn auto_detect() -> Self {
        // TODO: 使用hwloc或类似库检测实际CPU缓存配置
        Self::default()
    }

    /// 计算最优分块大小
    pub fn optimal_block_size(&self, matrix_size: usize, dtype_size: usize) -> usize {
        // 考虑数据类型大小和缓存行对齐
        let cache_line_size = 64; // 典型值
        let elements_per_line = cache_line_size / dtype_size;
        
        // 确保分块大小是缓存行的整数倍
        let block_size = (self.l1_cache_size / (3 * dtype_size))
            .sqrt()
            .max(self.min_block_size)
            .min(self.max_block_size);
            
        (block_size / elements_per_line) * elements_per_line
    }

    /// 分块矩阵乘法
    pub fn blocked_gemm<T: Float + Send + Sync>(
        &self,
        a: &RustArray<T>,
        b: &RustArray<T>,
        c: &mut RustArray<T>,
    ) -> Result<()> {
        let (m, k) = a.shape().dims_2d()?;
        let (k2, n) = b.shape().dims_2d()?;
        
        if k != k2 {
            return Err(Error::DimensionError("Matrix dimensions do not match".into()));
        }

        // 计算最优分块大小
        let block_size = self.optimal_block_size(m.max(n).max(k), std::mem::size_of::<T>());

        // 分块计算
        for i in (0..m).step_by(block_size) {
            let i_end = (i + block_size).min(m);
            
            for j in (0..n).step_by(block_size) {
                let j_end = (j + block_size).min(n);
                
                // 初始化结果块
                let mut c_block = T::zero();
                
                // 按块累加结果
                for l in (0..k).step_by(block_size) {
                    let l_end = (l + block_size).min(k);
                    
                    // 提取子块
                    let a_block = a.slice((i..i_end, l..l_end))?;
                    let b_block = b.slice((l..l_end, j..j_end))?;
                    
                    // 计算子块乘法
                    self.multiply_blocks(
                        &a_block,
                        &b_block,
                        &mut c.slice_mut((i..i_end, j..j_end))?,
                        c_block,
                    )?;
                }
            }
        }

        Ok(())
    }

    /// 子块矩阵乘法
    fn multiply_blocks<T: Float + Send + Sync>(
        &self,
        a: &RustArray<T>,
        b: &RustArray<T>,
        c: &mut RustArray<T>,
        beta: T,
    ) -> Result<()> {
        let (m, k) = a.shape().dims_2d()?;
        let (_, n) = b.shape().dims_2d()?;

        // 使用BLAS实现小矩阵乘法
        if m <= self.min_block_size && n <= self.min_block_size && k <= self.min_block_size {
            return BlasWrapper::gemm(
                false, false,
                m, n, k,
                T::one(),
                a.as_slice(),
                m,
                b.as_slice(),
                k,
                beta,
                c.as_mut_slice(),
                m,
            );
        }

        // 对大块使用递归分治
        if m > n && m > k {
            let mid = m / 2;
            self.multiply_blocks(
                &a.slice((0..mid, ..))?,
                b,
                &mut c.slice_mut((0..mid, ..))?,
                beta,
            )?;
            self.multiply_blocks(
                &a.slice((mid..m, ..))?,
                b,
                &mut c.slice_mut((mid..m, ..))?,
                beta,
            )?;
        } else if n > m && n > k {
            let mid = n / 2;
            self.multiply_blocks(
                a,
                &b.slice((.., 0..mid))?,
                &mut c.slice_mut((.., 0..mid))?,
                beta,
            )?;
            self.multiply_blocks(
                a,
                &b.slice((.., mid..n))?,
                &mut c.slice_mut((.., mid..n))?,
                beta,
            )?;
        } else {
            let mid = k / 2;
            self.multiply_blocks_add(
                &a.slice((.., 0..mid))?,
                &b.slice((0..mid, ..))?,
                c,
                beta,
            )?;
            self.multiply_blocks_add(
                &a.slice((.., mid..k))?,
                &b.slice((mid..k, ..))?,
                c,
                T::one(),
            )?;
        }

        Ok(())
    }

    /// 子块矩阵乘法（累加模式）
    fn multiply_blocks_add<T: Float + Send + Sync>(
        &self,
        a: &RustArray<T>,
        b: &RustArray<T>,
        c: &mut RustArray<T>,
        beta: T,
    ) -> Result<()> {
        let (m, k) = a.shape().dims_2d()?;
        let (_, n) = b.shape().dims_2d()?;

        // 并行化处理大矩阵
        if m * n * k > 1000000 {
            let chunks = rayon::current_num_threads().min(m);
            let chunk_size = (m + chunks - 1) / chunks;

            c.par_chunks_mut(chunk_size).enumerate().try_for_each(|(i, chunk)| {
                let start = i * chunk_size;
                let end = (start + chunk_size).min(m);
                
                BlasWrapper::gemm(
                    false, false,
                    end - start, n, k,
                    T::one(),
                    &a.slice((start..end, ..))?.as_slice(),
                    end - start,
                    b.as_slice(),
                    k,
                    beta,
                    chunk,
                    end - start,
                )
            })?;
        } else {
            BlasWrapper::gemm(
                false, false,
                m, n, k,
                T::one(),
                a.as_slice(),
                m,
                b.as_slice(),
                k,
                beta,
                c.as_mut_slice(),
                m,
            )?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_optimal_block_size() {
        let optimizer = BlockOptimizer::default();
        let block_size = optimizer.optimal_block_size(1000, 8);
        
        // 验证分块大小在合理范围内
        assert!(block_size >= optimizer.min_block_size);
        assert!(block_size <= optimizer.max_block_size);
        
        // 验证缓存行对齐
        assert_eq!(block_size % (64 / 8), 0);
    }

    #[test]
    fn test_blocked_matrix_multiplication() {
        let optimizer = BlockOptimizer::default();
        
        // 创建测试矩阵
        let a = RustArray::from_vec(
            vec![1.0f64, 2.0, 3.0, 4.0],
            (2, 2),
        );
        let b = RustArray::from_vec(
            vec![5.0f64, 6.0, 7.0, 8.0],
            (2, 2),
        );
        let mut c = RustArray::zeros((2, 2));
        
        // 执行分块乘法
        optimizer.blocked_gemm(&a, &b, &mut c).unwrap();
        
        // 验证结果
        assert_relative_eq!(c.get(&[0, 0]).unwrap(), 19.0); // 1*5 + 2*7
        assert_relative_eq!(c.get(&[0, 1]).unwrap(), 22.0); // 1*6 + 2*8
        assert_relative_eq!(c.get(&[1, 0]).unwrap(), 43.0); // 3*5 + 4*7
        assert_relative_eq!(c.get(&[1, 1]).unwrap(), 50.0); // 3*6 + 4*8
    }

    #[test]
    fn test_large_matrix_multiplication() {
        let optimizer = BlockOptimizer::default();
        
        // 创建大型测试矩阵
        let size = 512;
        let a = RustArray::ones((size, size));
        let b = RustArray::ones((size, size));
        let mut c = RustArray::zeros((size, size));
        
        // 执行分块乘法
        optimizer.blocked_gemm(&a, &b, &mut c).unwrap();
        
        // 验证结果：每个元素应该等于size
        for i in 0..size {
            for j in 0..size {
                assert_relative_eq!(
                    c.get(&[i, j]).unwrap(),
                    size as f64,
                    epsilon = 1e-10
                );
            }
        }
    }
}
