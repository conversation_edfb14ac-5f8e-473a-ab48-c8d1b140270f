use std::cmp::max;
use super::array::RustArray;
use super::shape::Shape;

/// 广播结果
#[derive(Debug)]
pub struct BroadcastResult {
    /// 广播后的目标形状
    pub shape: Vec<usize>,
    /// 第一个数组的步长
    pub strides1: Vec<isize>,
    /// 第二个数组的步长
    pub strides2: Vec<isize>,
}

/// 广播规则实现
pub struct Broadcast;

impl Broadcast {
    /// 检查形状是否可以广播
    pub fn check_shapes(shape1: &[usize], shape2: &[usize]) -> Option<Vec<usize>> {
        let n1 = shape1.len();
        let n2 = shape2.len();
        let n = max(n1, n2);
        
        let mut result = Vec::with_capacity(n);
        
        // 从最右边的维度开始比较
        for i in 0..n {
            let d1 = if i < n1 { shape1[n1 - 1 - i] } else { 1 };
            let d2 = if i < n2 { shape2[n2 - 1 - i] } else { 1 };
            
            if d1 == d2 {
                result.push(d1);
            } else if d1 == 1 {
                result.push(d2);
            } else if d2 == 1 {
                result.push(d1);
            } else {
                return None; // 无法广播
            }
        }
        
        result.reverse();
        Some(result)
    }

    /// 计算广播后的步长
    fn compute_broadcast_strides(
        shape: &[usize],
        orig_shape: &[usize],
        orig_strides: &[isize]
    ) -> Vec<isize> {
        let n = shape.len();
        let n_orig = orig_shape.len();
        let mut strides = vec![0; n];
        
        // 从右到左计算步长
        for i in 0..n {
            let j = n_orig as isize - (n as isize - i as isize);
            if j >= 0 && shape[i] == orig_shape[j as usize] {
                strides[i] = orig_strides[j as usize];
            } else {
                strides[i] = 0; // 广播维度的步长为0
            }
        }
        
        strides
    }

    /// 计算广播结果
    pub fn compute_broadcast(shape1: &[usize], strides1: &[isize],
                           shape2: &[usize], strides2: &[isize]) -> Option<BroadcastResult> {
        let shape = Self::check_shapes(shape1, shape2)?;
        
        let new_strides1 = Self::compute_broadcast_strides(&shape, shape1, strides1);
        let new_strides2 = Self::compute_broadcast_strides(&shape, shape2, strides2);
        
        Some(BroadcastResult {
            shape,
            strides1: new_strides1,
            strides2: new_strides2,
        })
    }
}

impl<T: Copy + Default> RustArray<T> {
    /// 广播数组到指定形状
    pub fn broadcast_to(&self, shape: &[usize]) -> Option<Self> {
        // 检查是否可以广播
        let broadcast = Broadcast::compute_broadcast(
            self.dims(),
            self.shape().strides(),
            shape,
            &vec![0; shape.len()]
        )?;

        // 创建新的形状
        let new_shape = Shape::new(broadcast.shape.clone(), self.shape().layout());
        let mut result = Self::zeros(broadcast.shape);

        // 应用广播
        for i in 0..result.size() {
            let target_indices = result.shape().get_indices(i).unwrap();
            let mut source_offset = 0isize;

            for (j, &stride) in broadcast.strides1.iter().enumerate() {
                source_offset += target_indices[j] as isize * stride;
            }

            unsafe {
                *result.get_ptr_at_indices(&target_indices) = 
                    *self.as_ptr().offset(source_offset);
            }
        }

        Some(result)
    }

    /// 执行广播操作
    pub fn broadcast_op<F>(&self, other: &Self, mut op: F) -> Option<Self>
    where
        F: FnMut(T, T) -> T
    {
        // 计算广播形状
        let broadcast = Broadcast::compute_broadcast(
            self.dims(),
            self.shape().strides(),
            other.dims(),
            other.shape().strides()
        )?;

        // 创建结果数组
        let mut result = Self::zeros(broadcast.shape.clone());

        // 执行广播操作
        for i in 0..result.size() {
            let indices = result.shape().get_indices(i).unwrap();
            
            let mut offset1 = 0isize;
            let mut offset2 = 0isize;

            for (j, (&s1, &s2)) in broadcast.strides1.iter()
                .zip(broadcast.strides2.iter())
                .enumerate() {
                offset1 += indices[j] as isize * s1;
                offset2 += indices[j] as isize * s2;
            }

            unsafe {
                *result.get_ptr_at_indices(&indices) = op(
                    *self.as_ptr().offset(offset1),
                    *other.as_ptr().offset(offset2)
                );
            }
        }

        Some(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_shape_broadcasting() {
        // 测试兼容形状
        let shape1 = vec![2, 1, 4];
        let shape2 = vec![3, 1, 4];
        let result = Broadcast::check_shapes(&shape1, &shape2);
        assert_eq!(result, Some(vec![3, 2, 4]));

        // 测试不兼容形状
        let shape1 = vec![2, 3];
        let shape2 = vec![2, 2];
        let result = Broadcast::check_shapes(&shape1, &shape2);
        assert_eq!(result, None);
    }

    #[test]
    fn test_array_broadcasting() {
        // 创建测试数组
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0],
            vec![2, 1]
        ).unwrap();
        let b = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0],
            vec![3]
        ).unwrap();

        // 测试广播
        let c = a.broadcast_op(&b, |x, y| x * y).unwrap();
        assert_eq!(c.dims(), &[3, 2]);
        assert_eq!(c[&[0, 0]], 1.0);
        assert_eq!(c[&[1, 0]], 2.0);
        assert_eq!(c[&[2, 0]], 3.0);
        assert_eq!(c[&[0, 1]], 2.0);
        assert_eq!(c[&[1, 1]], 4.0);
        assert_eq!(c[&[2, 1]], 6.0);
    }

    #[test]
    fn test_broadcast_to() {
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0],
            vec![2, 1]
        ).unwrap();

        let b = a.broadcast_to(&[2, 2]).unwrap();
        assert_eq!(b.dims(), &[2, 2]);
        assert_eq!(b[&[0, 0]], 1.0);
        assert_eq!(b[&[0, 1]], 1.0);
        assert_eq!(b[&[1, 0]], 2.0);
        assert_eq!(b[&[1, 1]], 2.0);
    }

    #[test]
    fn test_broadcast_arithmetic() {
        let a = RustArray::from_vec(
            vec![1.0f32, 2.0],
            vec![2, 1]
        ).unwrap();
        let b = RustArray::from_vec(
            vec![1.0f32, 2.0, 3.0],
            vec![3]
        ).unwrap();

        // 测试加法
        let c = a.broadcast_op(&b, |x, y| x + y).unwrap();
        assert_eq!(c[&[0, 0]], 2.0);
        assert_eq!(c[&[1, 0]], 3.0);
        assert_eq!(c[&[2, 0]], 4.0);
        assert_eq!(c[&[0, 1]], 3.0);
        assert_eq!(c[&[1, 1]], 4.0);
        assert_eq!(c[&[2, 1]], 5.0);

        // 测试乘法
        let c = a.broadcast_op(&b, |x, y| x * y).unwrap();
        assert_eq!(c[&[0, 0]], 1.0);
        assert_eq!(c[&[1, 0]], 2.0);
        assert_eq!(c[&[2, 0]], 3.0);
        assert_eq!(c[&[0, 1]], 2.0);
        assert_eq!(c[&[1, 1]], 4.0);
        assert_eq!(c[&[2, 1]], 6.0);
    }
}
