use crate::error::RustNumError;
use crate::array::lapack::sys;
use crate::array::array_impl::Layout;
use crate::array::lapack_utils::{validate_square_matrix, validate_symmetric_matrix};
use crate::RustArray;
use std::os::raw::*;

/// 线性方程组求解特征
pub trait LinearSolver<T> {
    /// 求解线性方程组 Ax = b
    fn solve(&self, b: &RustArray<T>) -> Result<RustArray<T>, RustNumError>;
    
    /// 求解具有多个右端项的线性方程组 AX = B
    fn solve_multi(&self, b: &RustArray<T>) -> Result<RustArray<T>, RustNumError>;
}

impl LinearSolver<f64> for RustArray<f64> {
    fn solve(&self, b: &RustArray<f64>) -> Result<RustArray<f64>, RustNumError> {
        // 使用DGESV求解通用线性方程组
        let n = validate_square_matrix(self)?;
        
        if b.ndim() != 1 {
            return Err(RustNumError::DimensionError(
                "右端项必须是一维向量".to_string()
            ));
        }
        
        if b.len() != n as usize {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n as usize],
                got: vec![b.len()],
            });
        }
        
        // 准备输入数据
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            a = a.as_column_major();
        }
        
        let mut b_copy = b.clone();
        let mut ipiv = vec![0; n as usize];
        let mut info = 0;
        
        unsafe {
            sys::dgesv_(
                &n,
                &1, // nrhs = 1
                a.as_mut_ptr(),
                &n,
                ipiv.as_mut_ptr(),
                b_copy.as_mut_ptr(),
                &n,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGESV的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                format!("U({},{})为0,方程组无解", info, info)
            ));
        }
        
        Ok(b_copy)
    }
    
    fn solve_multi(&self, b: &RustArray<f64>) -> Result<RustArray<f64>, RustNumError> {
        // 使用DGESV求解多个右端项的线性方程组
        let n = validate_square_matrix(self)?;
        
        if b.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "右端项必须是二维矩阵".to_string()
            ));
        }
        
        if b.shape()[0] != n as usize {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n as usize, b.shape()[1]],
                got: b.shape().to_vec(),
            });
        }
        
        let nrhs = b.shape()[1] as i32;
        
        // 准备输入数据
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            a = a.as_column_major();
        }
        
        let mut b_copy = b.clone();
        let mut ipiv = vec![0; n as usize];
        let mut info = 0;
        
        unsafe {
            sys::dgesv_(
                &n,
                &nrhs,
                a.as_mut_ptr(),
                &n,
                ipiv.as_mut_ptr(),
                b_copy.as_mut_ptr(),
                &n,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGESV的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                format!("U({},{})为0,方程组无解", info, info)
            ));
        }
        
        Ok(b_copy)
    }
}

/// 对称正定矩阵专用求解器
pub trait SymmetricPositiveDefiniteSolver<T>: LinearSolver<T> {
    /// 使用Cholesky分解求解对称正定线性方程组
    fn solve_spd(&self, b: &RustArray<T>) -> Result<RustArray<T>, RustNumError>;
}

impl SymmetricPositiveDefiniteSolver<f64> for RustArray<f64> {
    fn solve_spd(&self, b: &RustArray<f64>) -> Result<RustArray<f64>, RustNumError> {
        // 验证矩阵是对称的
        validate_symmetric_matrix(self)?;
        
        let n = self.shape()[0] as i32;
        let nrhs = if b.ndim() == 1 { 1 } else { b.shape()[1] as i32 };
        
        // 准备输入数据
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            a = a.as_column_major();
        }
        
        let mut b_copy = b.clone();
        let uplo = b'U' as c_char;
        let mut info = 0;
        
        unsafe {
            sys::dposv_(
                &uplo,
                &n,
                &nrhs,
                a.as_mut_ptr(),
                &n,
                b_copy.as_mut_ptr(),
                &n,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DPOSV的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                "矩阵不是正定的".to_string()
            ));
        }
        
        Ok(b_copy)
    }
}

/// 最小二乘求解器
pub trait LeastSquaresSolver<T> {
    /// 求解最小二乘问题：min ||Ax - b||_2
    fn solve_lstsq(&self, b: &RustArray<T>) -> Result<RustArray<T>, RustNumError>;
}

impl LeastSquaresSolver<f64> for RustArray<f64> {
    fn solve_lstsq(&self, b: &RustArray<f64>) -> Result<RustArray<f64>, RustNumError> {
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "系数矩阵必须是二维的".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        let nrhs = if b.ndim() == 1 { 1 } else { b.shape()[1] as i32 };
        
        // 准备输入数据
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            a = a.as_column_major();
        }
        
        let mut b_copy = if b.ndim() == 1 {
            b.clone().reshape(&[b.len(), 1])
        } else {
            b.clone()
        };
        
        let mut s = vec![0.0; n.min(m) as usize];
        let mut work = vec![0.0; 1];
        let mut info = 0;
        
        // 查询最优工作空间
        unsafe {
            sys::dgelsd_(
                &m,
                &n,
                &nrhs,
                a.as_mut_ptr(),
                &m,
                b_copy.as_mut_ptr(),
                &m,
                s.as_mut_ptr(),
                &(-1.0), // rcond = -1.0 使用机器精度
                &mut info,
                work.as_mut_ptr(),
                &(-1),
                &mut info
            );
        }
        
        let lwork = work[0] as i32;
        let mut work = vec![0.0; lwork as usize];
        
        // 求解最小二乘问题
        unsafe {
            sys::dgelsd_(
                &m,
                &n,
                &nrhs,
                a.as_mut_ptr(),
                &m,
                b_copy.as_mut_ptr(),
                &m,
                s.as_mut_ptr(),
                &(-1.0), // rcond = -1.0 使用机器精度
                &mut info,
                work.as_mut_ptr(),
                &lwork,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGELSD的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                "SVD未收敛".to_string()
            ));
        }
        
        // 只返回解向量部分
        let result = if b.ndim() == 1 {
            b_copy.slice(0..n as usize, 0..1).reshape(&[n as usize])
        } else {
            b_copy.slice(0..n as usize, 0..nrhs as usize)
        };
        
        Ok(result)
    }
}
