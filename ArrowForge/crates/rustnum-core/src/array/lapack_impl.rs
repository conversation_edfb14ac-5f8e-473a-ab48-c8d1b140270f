use std::os::raw::*;
use crate::array::array_impl::{RustArray, Layout};
use crate::error::RustNumError;
use super::lapack::{LapackOps, sys};

impl LapackOps<f64> for RustArray<f64> {
    fn lu_decomposition(&self) -> Result<(crate::array::array::RustArray<f64>, crate::array::array::RustArray<f64>, crate::array::array::RustArray<i32>), RustNumError> {
        // 验证输入是2D矩阵
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "LU分解要求输入为2D矩阵".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        
        // 创建工作数组
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            // LAPACK需要列优先格式
            a = a.as_column_major();
        }
        
        let mut ipiv = vec![0; m.min(n) as usize];
        let mut info = 0;
        
        unsafe {
            // 调用LAPACK的dgetrf_
            // sys::dgetrf_(
            //     &m,
            //     &n,
            //     a.as_mut_ptr(),
            //     &m, // lda
            //     ipiv.as_mut_ptr(),
            //     &mut info
            // );
            todo!("FFI stub: dgetrf_");
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGETRF的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                format!("U({},{})为0,分解失败", info, info)
            ));
        }
        
        // 从结果矩阵中提取L和U
        let mut l = crate::array::array::RustArray::zeros((m as usize, m.min(n) as usize));
        let mut u = crate::array::array::RustArray::zeros((m.min(n) as usize, n as usize));
        
        // 构造L矩阵（单位下三角）
        for i in 0..m as usize {
            for j in 0..m.min(n) as usize {
                if i > j {
                    l[[i, j]] = a[[i, j]];
                } else if i == j {
                    l[[i, j]] = 1.0;
                }
            }
        }
        
        // 构造U矩阵（上三角）
        for i in 0..m.min(n) as usize {
            for j in i..n as usize {
                u[[i, j]] = a[[i, j]];
            }
        }
        
        // 转换ipiv为RustArray
        let p = crate::array::array::RustArray::from_vec(ipiv);
        
        Ok((l, u, p))
    }
    
    fn qr_decomposition(&self) -> Result<(crate::array::array::RustArray<f64>, crate::array::array::RustArray<f64>), RustNumError> {
        // 验证输入是2D矩阵
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "QR分解要求输入为2D矩阵".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        
        // 创建工作数组
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            // LAPACK需要列优先格式
            a = a.as_column_major();
        }
        
        // TAU数组用于存储elementary reflectors
        let mut tau = vec![0.0; m.min(n) as usize];
        
        // 查询最佳工作空间大小
        let mut work = vec![0.0; 1];
        let mut info = 0;
        
        unsafe {
            // 工作空间查询
            sys::dgeqrf_(
                &m,
                &n,
                a.as_mut_ptr(),
                &m, // lda
                tau.as_mut_ptr(),
                work.as_mut_ptr(),
                &(-1), // lwork = -1 表示查询最佳大小
                &mut info
            );
        }
        
        let lwork = work[0] as i32;
        let mut work = vec![0.0; lwork as usize];
        
        unsafe {
            // 执行QR分解
            sys::dgeqrf_(
                &m,
                &n,
                a.as_mut_ptr(),
                &m, // lda
                tau.as_mut_ptr(),
                work.as_mut_ptr(),
                &lwork,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGEQRF的第{}个参数非法", -info)
            ));
        }
        
        // 从结果矩阵中提取R（上三角部分）
        let mut r = RustArray::zeros((n as usize, n as usize));
        for i in 0..n as usize {
            for j in i..n as usize {
                r[[i, j]] = a[[i, j]];
            }
        }
        
        // 构造Q矩阵
        let mut q = RustArray::zeros((m as usize, m as usize));
        // 先将Q初始化为单位矩阵
        for i in 0..m as usize {
            q[[i, i]] = 1.0;
        }
        
        // 查询orgqr的最佳工作空间
        unsafe {
            sys::dorgqr_(
                &m,
                &m,
                &(m.min(n)),
                q.as_mut_ptr(),
                &m,
                tau.as_ptr(),
                work.as_mut_ptr(),
                &(-1),
                &mut info
            );
        }
        
        let lwork = work[0] as i32;
        let mut work = vec![0.0; lwork as usize];
        
        // 计算Q矩阵
        unsafe {
            sys::dorgqr_(
                &m,
                &m,
                &(m.min(n)),
                q.as_mut_ptr(),
                &m,
                tau.as_ptr(),
                work.as_mut_ptr(),
                &lwork,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DORGQR的第{}个参数非法", -info)
            ));
        }
        
        Ok((q, r))
    }
    
    fn svd(&self) -> Result<(crate::array::array::RustArray<f64>, crate::array::array::RustArray<f64>, crate::array::array::RustArray<f64>), RustNumError> {
        // 验证输入是2D矩阵
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "SVD分解要求输入为2D矩阵".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        
        // 创建工作数组
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            // LAPACK需要列优先格式
            a = a.as_column_major();
        }
        
        // 分配结果数组
        let mut s = vec![0.0; m.min(n) as usize]; // 奇异值
        let mut u = RustArray::zeros((m as usize, m as usize)); // 左奇异向量
        let mut vt = RustArray::zeros((n as usize, n as usize)); // 右奇异向量的转置
        
        // 设置DGESVD参数
        let jobu = b'A' as c_char; // 计算完整的U矩阵
        let jobvt = b'A' as c_char; // 计算完整的V^T矩阵
        
        // 查询最佳工作空间
        let mut work = vec![0.0; 1];
        let mut info = 0;
        
        unsafe {
            sys::dgesvd_(
                &jobu,
                &jobvt,
                &m,
                &n,
                a.as_mut_ptr(),
                &m,
                s.as_mut_ptr(),
                u.as_mut_ptr(),
                &m,
                vt.as_mut_ptr(),
                &n,
                work.as_mut_ptr(),
                &(-1),
                &mut info
            );
        }
        
        let lwork = work[0] as i32;
        let mut work = vec![0.0; lwork as usize];
        
        // 执行SVD分解
        unsafe {
            sys::dgesvd_(
                &jobu,
                &jobvt,
                &m,
                &n,
                a.as_mut_ptr(),
                &m,
                s.as_mut_ptr(),
                u.as_mut_ptr(),
                &m,
                vt.as_mut_ptr(),
                &n,
                work.as_mut_ptr(),
                &lwork,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DGESVD的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                "DBDSQR未收敛".to_string()
            ));
        }
        
        // 将奇异值转换为对角矩阵
        let mut s_mat = RustArray::zeros((m.min(n) as usize, m.min(n) as usize));
        for i in 0..m.min(n) as usize {
            s_mat[[i, i]] = s[i];
        }
        
        Ok((u, s_mat, vt))
    }
    
    fn solve(&self, b: &crate::array::array::RustArray<f64>) -> Result<crate::array::array::RustArray<f64>, RustNumError> {
        // 线性方程组求解实现(TODO)
        unimplemented!("线性方程组求解待实现")
    }
    
    fn eigh(&self) -> Result<(crate::array::array::RustArray<f64>, crate::array::array::RustArray<f64>), RustNumError> {
        // 验证输入是2D方阵
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "特征值分解要求输入为2D方阵".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        if m != n {
            return Err(RustNumError::DimensionError(
                "特征值分解要求输入为方阵".to_string()
            ));
        }
        
        // 创建工作数组
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            // LAPACK需要列优先格式
            a = a.as_column_major();
        }
        
        // 分配特征值数组
        let mut w = vec![0.0; n as usize];
        
        // 设置DSYEV参数
        let jobz = b'V' as c_char; // 计算特征值和特征向量
        let uplo = b'U' as c_char; // 使用上三角部分
        
        // 查询最佳工作空间
        let mut work = vec![0.0; 1];
        let mut info = 0;
        
        unsafe {
            sys::dsyev_(
                &jobz,
                &uplo,
                &n,
                a.as_mut_ptr(),
                &n,
                w.as_mut_ptr(),
                work.as_mut_ptr(),
                &(-1),
                &mut info
            );
        }
        
        let lwork = work[0] as i32;
        let mut work = vec![0.0; lwork as usize];
        
        // 执行特征值分解
        unsafe {
            sys::dsyev_(
                &jobz,
                &uplo,
                &n,
                a.as_mut_ptr(),
                &n,
                w.as_mut_ptr(),
                work.as_mut_ptr(),
                &lwork,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DSYEV的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                "DSYEV算法未收敛".to_string()
            ));
        }
        
        // 特征值已经按升序排列，转换为RustArray
        let eigenvalues = RustArray::from_vec(w).reshape(&[n as usize]);
        
        // a现在包含了特征向量（按列存储）
        let eigenvectors = a;
        
        Ok((eigenvalues, eigenvectors))
    }
    
    fn cholesky(&self) -> Result<crate::array::array::RustArray<f64>, RustNumError> {
        // 验证输入是2D对称正定矩阵
        if self.ndim() != 2 {
            return Err(RustNumError::DimensionError(
                "Cholesky分解要求输入为2D方阵".to_string()
            ));
        }
        
        let (m, n) = (self.shape()[0] as i32, self.shape()[1] as i32);
        if m != n {
            return Err(RustNumError::DimensionError(
                "Cholesky分解要求输入为方阵".to_string()
            ));
        }
        
        // 创建工作数组
        let mut a = self.clone();
        if !matches!(a.layout(), Layout::ColumnMajor { .. }) {
            // LAPACK需要列优先格式
            a = a.as_column_major();
        }
        
        // 设置DPOTRF参数
        let uplo = b'L' as c_char; // 使用下三角部分
        let mut info = 0;
        
        // 执行Cholesky分解
        unsafe {
            sys::dpotrf_(
                &uplo,
                &n,
                a.as_mut_ptr(),
                &n,
                &mut info
            );
        }
        
        if info < 0 {
            return Err(RustNumError::LapackError(
                format!("DPOTRF的第{}个参数非法", -info)
            ));
        } else if info > 0 {
            return Err(RustNumError::LapackError(
                format!("矩阵不是正定的，前导主子式{}不正定", info)
            ));
        }
        
        // 清零上三角部分
        for i in 0..n as usize {
            for j in (i + 1)..n as usize {
                a[[i, j]] = 0.0;
            }
        }
        
        Ok(a)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_lu_decomposition() {
        // 创建一个测试矩阵
        let a = RustArray::from_slice(&[
            4.0, 3.0,
            6.0, 3.0
        ], &[2, 2]);
        
        let (l, u, p) = a.lu_decomposition().unwrap();
        
        // 验证 L 是单位下三角矩阵
        assert!(l[[0, 0]].abs() - 1.0 < 1e-10);
        assert!(l[[1, 0]].abs() - 1.5 < 1e-10);
        assert!(l[[1, 1]].abs() - 1.0 < 1e-10);
        
        // 验证 U 是上三角矩阵
        assert!(u[[0, 0]].abs() - 4.0 < 1e-10);
        assert!(u[[0, 1]].abs() - 3.0 < 1e-10);
        assert!(u[[1, 1]].abs() - (-1.5) < 1e-10);
        
        // 验证 L * U = P * A
        let mut pa = a.clone();
        // 应用置换
        for (i, &pi) in p.as_slice().iter().enumerate() {
            if (i + 1) as i32 != pi {
                pa.swap_rows(i, (pi - 1) as usize);
            }
        }
        
        let lu = l.matmul(&u);
        assert!(lu.allclose(&pa, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_qr_decomposition() {
        // 创建一个测试矩阵
        let a = RustArray::from_slice(&[
            12.0, -51.0, 4.0,
            6.0, 167.0, -68.0,
            -4.0, 24.0, -41.0
        ], &[3, 3]);
        
        let (q, r) = a.qr_decomposition().unwrap();
        
        // 验证Q是正交矩阵
        let qt = q.transpose();
        let qtq = qt.matmul(&q);
        let eye = RustArray::eye(3);
        assert!(qtq.allclose(&eye, 1e-10, 1e-10));
        
        // 验证R是上三角矩阵
        for i in 1..3 {
            for j in 0..i {
                assert!(r[[i, j]].abs() < 1e-10);
            }
        }
        
        // 验证A = Q * R
        let qr = q.matmul(&r);
        assert!(qr.allclose(&a, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_svd() {
        // 创建一个测试矩阵
        let a = RustArray::from_slice(&[
            1.0, 2.0,
            3.0, 4.0,
            5.0, 6.0
        ], &[3, 2]);
        
        let (u, s, vt) = a.svd().unwrap();
        
        // 验证U是正交矩阵
        let ut = u.transpose();
        let utu = ut.matmul(&u);
        let eye_m = RustArray::eye(3);
        assert!(utu.allclose(&eye_m, 1e-10, 1e-10));
        
        // 验证V是正交矩阵
        let v = vt.transpose();
        let vtv = vt.matmul(&v);
        let eye_n = RustArray::eye(2);
        assert!(vtv.allclose(&eye_n, 1e-10, 1e-10));
        
        // 验证奇异值是降序排列的
        let s_diag = (0..s.shape()[0].min(s.shape()[1]))
            .map(|i| s[[i, i]])
            .collect::<Vec<_>>();
        for i in 1..s_diag.len() {
            assert!(s_diag[i-1] >= s_diag[i]);
        }
        
        // 验证A = U * S * V^T
        let us = u.matmul(&s);
        let usvt = us.matmul(&vt);
        assert!(usvt.allclose(&a, 1e-10, 1e-10));
    }
    
    #[test]
    fn test_eigh() {
        // 创建一个对称矩阵
        let a = RustArray::from_slice(&[
            2.0, 1.0, 1.0,
            1.0, 2.0, 1.0,
            1.0, 1.0, 2.0
        ], &[3, 3]);
        
        let (eigenvalues, eigenvectors) = a.eigh().unwrap();
        
        // 验证特征值是实数且升序
        for i in 1..eigenvalues.len() {
            assert!(eigenvalues[i] >= eigenvalues[i-1]);
        }
        
        // 验证特征向量是正交的
        let vt = eigenvectors.transpose();
        let vtv = vt.matmul(&eigenvectors);
        let eye = RustArray::eye(3);
        assert!(vtv.allclose(&eye, 1e-10, 1e-10));
        
        // 验证 A * v = lambda * v
        for i in 0..3 {
            let v = eigenvectors.slice_column(i);
            let av = a.matmul(&v);
            let lambda_v = v.scale(eigenvalues[i]);
            assert!(av.allclose(&lambda_v, 1e-10, 1e-10));
        }
    }
    
    #[test]
    fn test_cholesky() {
        // 创建一个对称正定矩阵
        let a = RustArray::from_slice(&[
            4.0, 12.0, -16.0,
            12.0, 37.0, -43.0,
            -16.0, -43.0, 98.0
        ], &[3, 3]);
        
        let l = a.cholesky().unwrap();
        
        // 验证L是下三角矩阵
        for i in 0..3 {
            for j in (i+1)..3 {
                assert!(l[[i, j]].abs() < 1e-10);
            }
        }
        
        // 验证 A = L * L^T
        let lt = l.transpose();
        let llt = l.matmul(&lt);
        assert!(llt.allclose(&a, 1e-10, 1e-10));
    }
    
    #[test]
    #[should_panic(expected = "矩阵不是正定的")]
    fn test_cholesky_non_positive_definite() {
        // 创建一个非正定矩阵
        let a = RustArray::from_slice(&[
            1.0, 2.0,
            2.0, 1.0
        ], &[2, 2]);
        
        // 这应该会失败，因为矩阵不是正定的
        a.cholesky().unwrap();
    }
}
