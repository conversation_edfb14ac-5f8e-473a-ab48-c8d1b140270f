use crate::error::RustNumError;
use crate::traits::Numeric;
use crate::array::svd::{SvdOps, SvdResult, SvdConfig};
use crate::array::RustArray;
use std::fmt;

/// 简化的数组实现，使用标准 Vec 作为底层存储
#[derive(Clone)]
pub struct SimpleArray<T> {
    pub(crate) data: Vec<T>,
    shape: Vec<usize>,
    strides: Vec<usize>,
}

impl<T: Numeric> SimpleArray<T> {
    /// 创建新的数组
    pub fn new(data: Vec<T>, shape: Vec<usize>) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        if data.len() != total_elements {
            return Err(RustNumError::ShapeError(format!(
                "Data length {} doesn't match shape {:?} (expected {})",
                data.len(), shape, total_elements
            )));
        }

        let strides = Self::compute_strides(&shape);
        Ok(SimpleArray { data, shape, strides })
    }

    /// 创建零数组
    pub fn zeros(shape: &[usize]) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        let data = vec![T::zero(); total_elements];
        Self::new(data, shape.to_vec())
    }

    /// 创建单位数组
    pub fn ones(shape: &[usize]) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        let data = vec![T::one(); total_elements];
        Self::new(data, shape.to_vec())
    }

    /// 创建单位矩阵
    pub fn eye(n: usize) -> Result<Self, RustNumError> {
        let mut data = vec![T::zero(); n * n];
        for i in 0..n {
            data[i * n + i] = T::one();
        }
        Self::new(data, vec![n, n])
    }

    /// 从向量创建一维数组
    pub fn from_vec(data: Vec<T>) -> Result<Self, RustNumError> {
        let len = data.len();
        Self::new(data, vec![len])
    }

    /// 获取形状
    pub fn shape(&self) -> &[usize] {
        &self.shape
    }

    /// 获取元素数量
    pub fn len(&self) -> usize {
        self.data.len()
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }

    /// 计算步长
    fn compute_strides(shape: &[usize]) -> Vec<usize> {
        let mut strides = vec![1; shape.len()];
        for i in (0..shape.len().saturating_sub(1)).rev() {
            strides[i] = strides[i + 1] * shape[i + 1];
        }
        strides
    }

    /// 多维索引转换为一维索引
    fn multi_index_to_flat(&self, indices: &[usize]) -> Result<usize, RustNumError> {
        if indices.len() != self.shape.len() {
            return Err(RustNumError::IndexError(format!(
                "Index dimension {} doesn't match array dimension {}",
                indices.len(), self.shape.len()
            )));
        }

        let mut flat_index = 0;
        for (i, &idx) in indices.iter().enumerate() {
            if idx >= self.shape[i] {
                return Err(RustNumError::IndexError(format!(
                    "Index {} is out of bounds for axis {} with size {}",
                    idx, i, self.shape[i]
                )));
            }
            flat_index += idx * self.strides[i];
        }
        Ok(flat_index)
    }

    /// 获取元素
    pub fn get(&self, indices: &[usize]) -> Result<T, RustNumError> {
        let flat_index = self.multi_index_to_flat(indices)?;
        Ok(self.data[flat_index])
    }

    /// 设置元素
    pub fn set(&mut self, indices: &[usize], value: T) -> Result<(), RustNumError> {
        let flat_index = self.multi_index_to_flat(indices)?;
        self.data[flat_index] = value;
        Ok(())
    }

    /// 数组加法
    pub fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape != other.shape {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape, other.shape
            )));
        }

        let result_data: Vec<T> = self.data.iter()
            .zip(other.data.iter())
            .map(|(&a, &b)| a + b)
            .collect();

        Self::new(result_data, self.shape.clone())
    }

    /// 数组减法
    pub fn sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape != other.shape {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape, other.shape
            )));
        }

        let result_data: Vec<T> = self.data.iter()
            .zip(other.data.iter())
            .map(|(&a, &b)| a - b)
            .collect();

        Self::new(result_data, self.shape.clone())
    }

    /// 标量乘法
    pub fn scalar_mul(&self, scalar: T) -> Result<Self, RustNumError> {
        let result_data: Vec<T> = self.data.iter()
            .map(|&x| x * scalar)
            .collect();

        Self::new(result_data, self.shape.clone())
    }

    /// 矩阵乘法（仅支持2D矩阵）
    pub fn matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape.len() != 2 || other.shape.len() != 2 {
            return Err(RustNumError::ShapeError(
                "Matrix multiplication requires 2D arrays".to_string()
            ));
        }

        let (m, k) = (self.shape[0], self.shape[1]);
        let (k2, n) = (other.shape[0], other.shape[1]);

        if k != k2 {
            return Err(RustNumError::ShapeError(format!(
                "Cannot multiply matrices with shapes {:?} and {:?}", 
                self.shape, other.shape
            )));
        }

        let mut result_data = vec![T::zero(); m * n];

        for i in 0..m {
            for j in 0..n {
                let mut sum = T::zero();
                for l in 0..k {
                    let a_val = self.data[i * k + l];
                    let b_val = other.data[l * n + j];
                    sum = sum + a_val * b_val;
                }
                result_data[i * n + j] = sum;
            }
        }

        Self::new(result_data, vec![m, n])
    }

    /// 转置（仅支持2D矩阵）
    pub fn transpose(&self) -> Result<Self, RustNumError> {
        if self.shape.len() != 2 {
            return Err(RustNumError::ShapeError(
                "Transpose requires 2D array".to_string()
            ));
        }

        let (m, n) = (self.shape[0], self.shape[1]);
        let mut result_data = vec![T::zero(); m * n];

        for i in 0..m {
            for j in 0..n {
                result_data[j * m + i] = self.data[i * n + j];
            }
        }

        Self::new(result_data, vec![n, m])
    }

    /// 求和
    pub fn sum(&self) -> T {
        self.data.iter().fold(T::zero(), |acc, &x| acc + x)
    }

    /// 求平均值
    pub fn mean(&self) -> T {
        if self.data.is_empty() {
            return T::zero();
        }
        let sum = self.sum();
        let len = T::from_usize(self.data.len()).unwrap_or(T::one());
        sum / len
    }
}

impl<T: Numeric + fmt::Display> fmt::Debug for SimpleArray<T> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "SimpleArray {{ shape: {:?}, data: [", self.shape)?;
        for (i, item) in self.data.iter().enumerate() {
            if i > 0 { write!(f, ", ")?; }
            if i >= 10 {
                write!(f, "...")?;
                break;
            }
            write!(f, "{}", item)?;
        }
        write!(f, "] }}")
    }
}

impl<T: Numeric + fmt::Display> fmt::Display for SimpleArray<T> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self.shape.len() {
            1 => {
                write!(f, "[")?;
                for (i, &val) in self.data.iter().enumerate() {
                    if i > 0 { write!(f, ", ")?; }
                    write!(f, "{}", val)?;
                }
                write!(f, "]")
            }
            2 => {
                let (rows, cols) = (self.shape[0], self.shape[1]);
                write!(f, "[")?;
                for i in 0..rows {
                    if i > 0 { write!(f, ",\n ")?; }
                    write!(f, "[")?;
                    for j in 0..cols {
                        if j > 0 { write!(f, ", ")?; }
                        write!(f, "{}", self.data[i * cols + j])?;
                    }
                    write!(f, "]")?;
                }
                write!(f, "]")
            }
            _ => write!(f, "SimpleArray(shape: {:?})", self.shape)
        }
    }
}

// 为 SimpleArray 实现 SvdOps trait
impl<T: Numeric + Default + PartialOrd> SvdOps<T> for SimpleArray<T>
where
    T: Copy + Clone + std::fmt::Debug,
{
    fn svd(&self, config: &SvdConfig) -> Result<SvdResult<T>, RustNumError> {
        // 简化的 SVD 实现，仅用于演示
        if self.shape.len() != 2 {
            return Err(RustNumError::ShapeError(
                "SVD requires 2D matrix".to_string()
            ));
        }

        let (m, n) = (self.shape[0], self.shape[1]);
        let min_mn = m.min(n);

        // 创建单位矩阵作为 U 和 V^T
        let mut u_data = vec![T::zero(); m * min_mn];
        let mut vt_data = vec![T::zero(); min_mn * n];
        let mut s_data = vec![T::zero(); min_mn];

        // 简化实现：对角线元素作为奇异值
        for i in 0..min_mn {
            // U 矩阵的对角线设为 1
            u_data[i * min_mn + i] = T::one();
            // V^T 矩阵的对角线设为 1
            vt_data[i * n + i] = T::one();
            // 奇异值设为矩阵对角线元素的绝对值
            if i < m && i < n {
                s_data[i] = self.data[i * n + i].abs();
            } else {
                s_data[i] = T::one();
            }
        }

        let u = SimpleArray::new(u_data, vec![m, min_mn])?;
        let s = SimpleArray::new(s_data, vec![min_mn])?;
        let vt = SimpleArray::new(vt_data, vec![min_mn, n])?;

        // 转换为 RustArray 格式
        let u_rust = u.to_rust_array()?;
        let s_rust = s.to_rust_array()?;
        let vt_rust = vt.to_rust_array()?;

        Ok(SvdResult {
            u: u_rust,
            s: s_rust,
            vt: vt_rust,
        })
    }

    fn condition_number(&self) -> Result<T, RustNumError> {
        let svd_result = self.svd(&SvdConfig::default())?;
        let singular_values = svd_result.s.as_slice();

        if singular_values.is_empty() {
            return Ok(T::one());
        }

        let max_sv = singular_values[0];
        let min_sv = singular_values[singular_values.len() - 1];

        if min_sv.abs() < T::from_f64(1e-15).unwrap_or(T::zero()) {
            // 矩阵接近奇异
            Ok(T::from_f64(1e15).unwrap_or(T::one()))
        } else {
            Ok(max_sv / min_sv)
        }
    }

    fn rank(&self, tolerance: Option<T>) -> Result<usize, RustNumError> {
        let svd_result = self.svd(&SvdConfig::default())?;
        let singular_values = svd_result.s.as_slice();

        let tol = tolerance.unwrap_or_else(|| T::from_f64(1e-12).unwrap_or(T::zero()));

        let rank = singular_values.iter()
            .filter(|&&sv| sv.abs() > tol)
            .count();

        Ok(rank)
    }
}

impl<T: Numeric + Default + Copy> SimpleArray<T> {
    /// 转换为 RustArray 以便使用高级算法
    fn to_rust_array(&self) -> Result<RustArray<T>, RustNumError> {
        use crate::array::StorageOrder;
        use crate::memory::MemoryPool;
        use std::sync::Arc;
        use parking_lot::RwLock;

        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut rust_array = RustArray::new(self.shape.clone(), StorageOrder::RowMajor, pool)?;

        // 复制数据
        let slice = rust_array.as_mut_slice();
        slice.copy_from_slice(&self.data);

        Ok(rust_array)
    }

    /// 获取底层数据的引用（用于测试）
    pub fn as_slice(&self) -> &[T] {
        &self.data
    }

    /// 获取底层数据的可变引用
    pub fn as_mut_slice(&mut self) -> &mut [T] {
        &mut self.data
    }

    /// 将数组转换为向量（消费 self）
    pub fn into_vec(self) -> Vec<T> {
        self.data
    }
}
