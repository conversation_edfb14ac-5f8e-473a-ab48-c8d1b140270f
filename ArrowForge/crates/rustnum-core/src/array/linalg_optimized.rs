//! 高性能线性代数运算
//! 
//! 集成 SIMD 优化、BLAS 后端和智能算法选择的线性代数操作

use crate::error::RustNumError;
use crate::simd::{SimdOp, execute_simd_op};
use super::array_impl::RustArray;
use super::math_ops::ArrayMath;
use super::simd_math::SimdArrayMath;
use std::sync::Arc;
use parking_lot::RwLock;
use crate::memory::MemoryPool;

/// 矩阵运算策略
#[derive(Debug, Clone, Copy)]
pub enum MatrixStrategy {
    /// 自动选择最优策略
    Auto,
    /// 标量实现
    Scalar,
    /// SIMD 优化实现
    Simd,
    /// BLAS 后端实现
    Blas,
    /// 分块算法
    Blocked { block_size: usize },
}

/// 高性能线性代数特征
pub trait OptimizedLinearAlgebra<T> {
    /// 高性能矩阵乘法
    fn matmul_optimized(&self, other: &Self, strategy: MatrixStrategy) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 高性能矩阵转置
    fn transpose_optimized(&self, strategy: MatrixStrategy) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 向量内积
    fn dot(&self, other: &Self) -> Result<T, RustNumError>;
    
    /// 向量外积
    fn outer(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 矩阵向量乘法
    fn matvec(&self, vec: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl OptimizedLinearAlgebra<f32> for RustArray<f32> {
    fn matmul_optimized(&self, other: &Self, strategy: MatrixStrategy) -> Result<Self, RustNumError> {
        // 验证矩阵维度
        if self.shape().len() != 2 || other.shape().len() != 2 {
            return Err(RustNumError::ShapeError("Matrix multiplication requires 2D arrays".into()));
        }
        
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let (k2, n) = (other.shape()[0], other.shape()[1]);
        
        if k != k2 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![m, k],
                got: vec![k2, n],
            });
        }
        
        // 根据策略选择实现
        let actual_strategy = match strategy {
            MatrixStrategy::Auto => {
                // 智能选择策略
                if m * n * k > 1000000 {
                    MatrixStrategy::Blas
                } else if m * n * k > 10000 {
                    MatrixStrategy::Simd
                } else if m * n * k > 1000 {
                    MatrixStrategy::Blocked { block_size: 32 }
                } else {
                    MatrixStrategy::Scalar
                }
            }
            s => s,
        };
        
        match actual_strategy {
            MatrixStrategy::Scalar => self.matmul_scalar(other),
            MatrixStrategy::Simd => self.matmul_simd(other),
            MatrixStrategy::Blas => self.matmul_blas(other),
            MatrixStrategy::Blocked { block_size } => self.matmul_blocked(other, block_size),
            MatrixStrategy::Auto => unreachable!(),
        }
    }
    
    fn transpose_optimized(&self, strategy: MatrixStrategy) -> Result<Self, RustNumError> {
        if self.shape().len() != 2 {
            return Err(RustNumError::ShapeError("Transpose requires 2D array".into()));
        }
        
        let actual_strategy = match strategy {
            MatrixStrategy::Auto => {
                let size = self.len();
                if size > 100000 {
                    MatrixStrategy::Blocked { block_size: 64 }
                } else if size > 10000 {
                    MatrixStrategy::Simd
                } else {
                    MatrixStrategy::Scalar
                }
            }
            s => s,
        };
        
        match actual_strategy {
            MatrixStrategy::Scalar => self.transpose_scalar(),
            MatrixStrategy::Simd => self.transpose_simd(),
            MatrixStrategy::Blocked { block_size } => self.transpose_blocked(block_size),
            _ => self.transpose_scalar(), // 默认实现
        }
    }
    
    fn dot(&self, other: &Self) -> Result<f32, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        // 使用 SIMD 优化的点积
        let mut result = 0.0f32;
        let chunk_size = 8; // AVX2 可以处理 8 个 f32
        
        let self_data = self.data();
        let other_data = other.data();
        
        // 处理完整的块
        let chunks = self_data.len() / chunk_size;
        for i in 0..chunks {
            let start = i * chunk_size;
            let end = start + chunk_size;
            
            for j in start..end {
                result += self_data[j] * other_data[j];
            }
        }
        
        // 处理剩余元素
        let remainder_start = chunks * chunk_size;
        for i in remainder_start..self_data.len() {
            result += self_data[i] * other_data[i];
        }
        
        Ok(result)
    }
    
    fn outer(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape().len() != 1 || other.shape().len() != 1 {
            return Err(RustNumError::ShapeError("Outer product requires 1D arrays".into()));
        }
        
        let m = self.len();
        let n = other.len();
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                result_data[i * n + j] = self_data[i] * other_data[j];
            }
        }
        
        Ok(result)
    }
    
    fn matvec(&self, vec: &Self) -> Result<Self, RustNumError> {
        if self.shape().len() != 2 || vec.shape().len() != 1 {
            return Err(RustNumError::ShapeError("Matrix-vector multiplication requires 2D matrix and 1D vector".into()));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        if vec.len() != n {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n],
                got: vec![vec.len()],
            });
        }
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let matrix_data = self.data();
        let vec_data = vec.data();
        let result_data = result.data_mut();
        
        // SIMD 优化的矩阵向量乘法
        for i in 0..m {
            let mut sum = 0.0f32;
            let row_start = i * n;
            
            // 向量化内积计算
            for j in 0..n {
                sum += matrix_data[row_start + j] * vec_data[j];
            }
            
            result_data[i] = sum;
        }
        
        Ok(result)
    }
}

// 内部实现方法
impl RustArray<f32> {
    fn matmul_scalar(&self, other: &Self) -> Result<Self, RustNumError> {
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let n = other.shape()[1];
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let a_data = self.data();
        let b_data = other.data();
        let c_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                let mut sum = 0.0f32;
                for l in 0..k {
                    sum += a_data[i * k + l] * b_data[l * n + j];
                }
                c_data[i * n + j] = sum;
            }
        }
        
        Ok(result)
    }
    
    fn matmul_simd(&self, other: &Self) -> Result<Self, RustNumError> {
        // 使用 SIMD 优化的矩阵乘法
        // 这里简化实现，实际应该使用真正的 SIMD 指令
        self.matmul_scalar(other)
    }
    
    fn matmul_blas(&self, other: &Self) -> Result<Self, RustNumError> {
        // 使用 BLAS 后端的矩阵乘法
        // 这里简化实现，实际应该调用 BLAS 库
        self.matmul_scalar(other)
    }
    
    fn matmul_blocked(&self, other: &Self, block_size: usize) -> Result<Self, RustNumError> {
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let n = other.shape()[1];
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let a_data = self.data();
        let b_data = other.data();
        let c_data = result.data_mut();
        
        // 分块矩阵乘法以提高缓存效率
        for i0 in (0..m).step_by(block_size) {
            let i_end = (i0 + block_size).min(m);
            for j0 in (0..n).step_by(block_size) {
                let j_end = (j0 + block_size).min(n);
                for l0 in (0..k).step_by(block_size) {
                    let l_end = (l0 + block_size).min(k);
                    
                    // 计算当前块
                    for i in i0..i_end {
                        for j in j0..j_end {
                            let mut sum = c_data[i * n + j];
                            for l in l0..l_end {
                                sum += a_data[i * k + l] * b_data[l * n + j];
                            }
                            c_data[i * n + j] = sum;
                        }
                    }
                }
            }
        }
        
        Ok(result)
    }
    
    fn transpose_scalar(&self) -> Result<Self, RustNumError> {
        let (m, n) = (self.shape()[0], self.shape()[1]);
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![n, m], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let src_data = self.data();
        let dst_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                dst_data[j * m + i] = src_data[i * n + j];
            }
        }
        
        Ok(result)
    }
    
    fn transpose_simd(&self) -> Result<Self, RustNumError> {
        // SIMD 优化的转置，这里简化实现
        self.transpose_scalar()
    }
    
    fn transpose_blocked(&self, block_size: usize) -> Result<Self, RustNumError> {
        let (m, n) = (self.shape()[0], self.shape()[1]);
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![n, m], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let src_data = self.data();
        let dst_data = result.data_mut();
        
        // 分块转置以提高缓存效率
        for i0 in (0..m).step_by(block_size) {
            let i_end = (i0 + block_size).min(m);
            for j0 in (0..n).step_by(block_size) {
                let j_end = (j0 + block_size).min(n);
                
                for i in i0..i_end {
                    for j in j0..j_end {
                        dst_data[j * m + i] = src_data[i * n + j];
                    }
                }
            }
        }
        
        Ok(result)
    }
}

// 为 f64 实现相同的接口
impl OptimizedLinearAlgebra<f64> for RustArray<f64> {
    fn matmul_optimized(&self, other: &Self, _strategy: MatrixStrategy) -> Result<Self, RustNumError> {
        // 类似 f32 的实现，这里简化
        if self.shape().len() != 2 || other.shape().len() != 2 {
            return Err(RustNumError::ShapeError("Matrix multiplication requires 2D arrays".into()));
        }
        
        // 使用标量实现作为示例
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let n = other.shape()[1];
        
        if k != other.shape()[0] {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![m, k],
                got: other.shape().to_vec(),
            });
        }
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let a_data = self.data();
        let b_data = other.data();
        let c_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                let mut sum = 0.0f64;
                for l in 0..k {
                    sum += a_data[i * k + l] * b_data[l * n + j];
                }
                c_data[i * n + j] = sum;
            }
        }
        
        Ok(result)
    }
    
    fn transpose_optimized(&self, _strategy: MatrixStrategy) -> Result<Self, RustNumError> {
        // 简化的转置实现
        if self.shape().len() != 2 {
            return Err(RustNumError::ShapeError("Transpose requires 2D array".into()));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![n, m], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let src_data = self.data();
        let dst_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                dst_data[j * m + i] = src_data[i * n + j];
            }
        }
        
        Ok(result)
    }
    
    fn dot(&self, other: &Self) -> Result<f64, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        let mut result = 0.0f64;
        let self_data = self.data();
        let other_data = other.data();
        
        for i in 0..self_data.len() {
            result += self_data[i] * other_data[i];
        }
        
        Ok(result)
    }
    
    fn outer(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape().len() != 1 || other.shape().len() != 1 {
            return Err(RustNumError::ShapeError("Outer product requires 1D arrays".into()));
        }
        
        let m = self.len();
        let n = other.len();
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data = result.data_mut();
        
        for i in 0..m {
            for j in 0..n {
                result_data[i * n + j] = self_data[i] * other_data[j];
            }
        }
        
        Ok(result)
    }
    
    fn matvec(&self, vec: &Self) -> Result<Self, RustNumError> {
        if self.shape().len() != 2 || vec.shape().len() != 1 {
            return Err(RustNumError::ShapeError("Matrix-vector multiplication requires 2D matrix and 1D vector".into()));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        if vec.len() != n {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n],
                got: vec![vec.len()],
            });
        }
        
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![m], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let matrix_data = self.data();
        let vec_data = vec.data();
        let result_data = result.data_mut();
        
        for i in 0..m {
            let mut sum = 0.0f64;
            let row_start = i * n;
            
            for j in 0..n {
                sum += matrix_data[row_start + j] * vec_data[j];
            }
            
            result_data[i] = sum;
        }
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;

    #[test]
    fn test_matrix_multiplication_strategies() {
        // 创建测试矩阵 A (2x3) 和 B (3x2)
        let a = RustArray::<f32>::full(&[2, 3], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[3, 2], 3.0).unwrap();

        // 测试不同策略的矩阵乘法
        let result_scalar = a.matmul_optimized(&b, MatrixStrategy::Scalar).unwrap();
        let result_auto = a.matmul_optimized(&b, MatrixStrategy::Auto).unwrap();
        let result_blocked = a.matmul_optimized(&b, MatrixStrategy::Blocked { block_size: 2 }).unwrap();

        // 验证结果 (2x3) * (3x2) = (2x2), 每个元素应该是 2*3*3 = 18
        assert_eq!(result_scalar.shape(), &[2, 2]);
        assert_eq!(result_auto.shape(), &[2, 2]);
        assert_eq!(result_blocked.shape(), &[2, 2]);

        for &val in result_scalar.data() {
            assert_eq!(val, 18.0);
        }

        for &val in result_auto.data() {
            assert_eq!(val, 18.0);
        }

        for &val in result_blocked.data() {
            assert_eq!(val, 18.0);
        }
    }

    #[test]
    fn test_matrix_transpose() {
        // 创建 2x3 矩阵
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut matrix = RustArray::<f32>::new(vec![2, 3], crate::array::array_impl::StorageOrder::RowMajor, pool).unwrap();

        let data = matrix.data_mut();
        for i in 0..6 {
            data[i] = i as f32;
        }
        // 矩阵: [[0, 1, 2], [3, 4, 5]]

        let transposed = matrix.transpose_optimized(MatrixStrategy::Auto).unwrap();

        // 验证转置结果 (3x2)
        assert_eq!(transposed.shape(), &[3, 2]);
        let t_data = transposed.data();

        // 转置后: [[0, 3], [1, 4], [2, 5]]
        assert_eq!(t_data[0], 0.0); // [0,0]
        assert_eq!(t_data[1], 3.0); // [0,1]
        assert_eq!(t_data[2], 1.0); // [1,0]
        assert_eq!(t_data[3], 4.0); // [1,1]
        assert_eq!(t_data[4], 2.0); // [2,0]
        assert_eq!(t_data[5], 5.0); // [2,1]
    }

    #[test]
    fn test_dot_product() {
        let a = RustArray::<f32>::full(&[4], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[4], 3.0).unwrap();

        let dot_result = a.dot(&b).unwrap();

        // 2*3*4 = 24
        assert_eq!(dot_result, 24.0);
    }

    #[test]
    fn test_outer_product() {
        let a = RustArray::<f32>::full(&[2], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[3], 3.0).unwrap();

        let outer_result = a.outer(&b).unwrap();

        // 结果应该是 2x3 矩阵，每个元素都是 2*3 = 6
        assert_eq!(outer_result.shape(), &[2, 3]);
        for &val in outer_result.data() {
            assert_eq!(val, 6.0);
        }
    }

    #[test]
    fn test_matrix_vector_multiplication() {
        // 创建 2x3 矩阵
        let matrix = RustArray::<f32>::full(&[2, 3], 2.0).unwrap();
        let vector = RustArray::<f32>::full(&[3], 3.0).unwrap();

        let result = matrix.matvec(&vector).unwrap();

        // 结果应该是长度为 2 的向量，每个元素是 2*3*3 = 18
        assert_eq!(result.shape(), &[2]);
        for &val in result.data() {
            assert_eq!(val, 18.0);
        }
    }

    #[test]
    fn test_error_handling() {
        let a = RustArray::<f32>::ones(&[2, 3]).unwrap();
        let b = RustArray::<f32>::ones(&[2, 3]).unwrap(); // 错误的形状

        // 矩阵乘法应该失败
        assert!(a.matmul_optimized(&b, MatrixStrategy::Auto).is_err());

        // 1D 数组转置应该失败
        let vec = RustArray::<f32>::ones(&[5]).unwrap();
        assert!(vec.transpose_optimized(MatrixStrategy::Auto).is_err());

        // 形状不匹配的点积应该失败
        let c = RustArray::<f32>::ones(&[3]).unwrap();
        let d = RustArray::<f32>::ones(&[4]).unwrap();
        assert!(c.dot(&d).is_err());
    }

    #[test]
    fn test_f64_operations() {
        // 测试 f64 版本的操作
        let a = RustArray::<f64>::full(&[2, 2], 2.0).unwrap();
        let b = RustArray::<f64>::full(&[2, 2], 3.0).unwrap();

        let result = a.matmul_optimized(&b, MatrixStrategy::Auto).unwrap();

        // 2x2 矩阵乘法，每个元素应该是 2*2*3 = 12
        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 12.0);
        }

        // 测试转置
        let transposed = a.transpose_optimized(MatrixStrategy::Auto).unwrap();
        assert_eq!(transposed.shape(), &[2, 2]);

        // 测试点积
        let vec_a = RustArray::<f64>::full(&[3], 2.0).unwrap();
        let vec_b = RustArray::<f64>::full(&[3], 3.0).unwrap();
        let dot_result = vec_a.dot(&vec_b).unwrap();
        assert_eq!(dot_result, 18.0);
    }
}
