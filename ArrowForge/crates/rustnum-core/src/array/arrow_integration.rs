//! Arrow 生态深度集成
//! 
//! 实现与 Apache Arrow 数据格式的原生集成，支持零拷贝数据转换

use crate::error::RustNumError;
use super::array_impl::RustArray;
use std::sync::Arc;
use parking_lot::RwLock;
use crate::memory::MemoryPool;

#[cfg(feature = "arrow")]
use arrow::{
    array::{Array as ArrowArray, ArrayRef, Float32Array, Float64Array, PrimitiveArray, Int32Array, Int64Array},
    buffer::Buffer as ArrowBuffer,
    datatypes::{DataType as ArrowDataType, Field, Schema, SchemaRef},
    record_batch::RecordBatch,
    compute,
};

#[cfg(feature = "arrow")]
use arrow::compute::{sum, min, max, filter, take};

// 使用Arrow 55.1.0版本的正确导入路径
#[cfg(feature = "arrow")]
use arrow::compute::{add, subtract, multiply, divide};

#[cfg(feature = "arrow")]
use arrow::compute::{eq, neq, gt, gt_eq, lt, lt_eq};

/// Arrow 集成特征
pub trait ArrowIntegration<T> {
    /// 从 Arrow Array 创建 RustArray（零拷贝）
    fn from_arrow(arrow_array: ArrayRef) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 转换为 Arrow Array（零拷贝）
    fn to_arrow(&self) -> Result<ArrayRef, RustNumError>;
    
    /// 从 Arrow RecordBatch 创建
    fn from_record_batch(batch: &RecordBatch, column: usize) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 转换为 Arrow RecordBatch
    fn to_record_batch(&self, field_name: &str) -> Result<RecordBatch, RustNumError>;
}

/// Arrow 数据类型映射
pub trait ArrowTypeMapping {
    fn arrow_data_type() -> ArrowDataType;
    fn from_arrow_buffer(buffer: ArrowBuffer, len: usize) -> Result<Vec<Self>, RustNumError>
    where
        Self: Sized;
    fn to_arrow_buffer(data: &[Self]) -> Result<ArrowBuffer, RustNumError>
    where
        Self: Sized;
}

impl ArrowTypeMapping for f32 {
    fn arrow_data_type() -> ArrowDataType {
        ArrowDataType::Float32
    }
    
    fn from_arrow_buffer(buffer: ArrowBuffer, len: usize) -> Result<Vec<Self>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            // 零拷贝转换：直接使用 Arrow 的内存布局
            let slice = unsafe {
                std::slice::from_raw_parts(buffer.as_ptr() as *const f32, len)
            };
            Ok(slice.to_vec())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_arrow_buffer(data: &[Self]) -> Result<ArrowBuffer, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            // 零拷贝转换：直接使用现有内存
            let byte_slice = unsafe {
                std::slice::from_raw_parts(
                    data.as_ptr() as *const u8,
                    data.len() * std::mem::size_of::<f32>()
                )
            };
            Ok(ArrowBuffer::from(byte_slice))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
}

impl ArrowTypeMapping for f64 {
    fn arrow_data_type() -> ArrowDataType {
        ArrowDataType::Float64
    }
    
    fn from_arrow_buffer(buffer: ArrowBuffer, len: usize) -> Result<Vec<Self>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let slice = unsafe {
                std::slice::from_raw_parts(buffer.as_ptr() as *const f64, len)
            };
            Ok(slice.to_vec())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_arrow_buffer(data: &[Self]) -> Result<ArrowBuffer, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let byte_slice = unsafe {
                std::slice::from_raw_parts(
                    data.as_ptr() as *const u8,
                    data.len() * std::mem::size_of::<f64>()
                )
            };
            Ok(ArrowBuffer::from(byte_slice))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
}

impl ArrowIntegration<f32> for RustArray<f32> {
    fn from_arrow(arrow_array: ArrayRef) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            // 检查数据类型匹配
            if !matches!(arrow_array.data_type(), ArrowDataType::Float32) {
                return Err(RustNumError::TypeMismatch {
                    expected: "Float32".into(),
                    got: format!("{:?}", arrow_array.data_type()),
                });
            }
            
            let float_array = arrow_array
                .as_any()
                .downcast_ref::<Float32Array>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to Float32Array".into()))?;
            
            // 零拷贝数据提取
            let values = float_array.values();
            let data = f32::from_arrow_buffer(values.clone().into(), float_array.len())?;
            
            // 创建 RustArray（1D 数组）
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            let mut result = Self::new(vec![data.len()], super::array_impl::StorageOrder::RowMajor, pool)?;
            
            // 直接复制数据（在真实实现中可以进一步优化为零拷贝）
            result.data_mut().copy_from_slice(&data);
            
            Ok(result)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_arrow(&self) -> Result<ArrayRef, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            // 创建 Arrow Buffer
            let buffer = f32::to_arrow_buffer(self.data())?;
            
            // 创建 Float32Array
            let array = Float32Array::new(buffer.into(), None);
            
            Ok(Arc::new(array))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn from_record_batch(batch: &RecordBatch, column: usize) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            if column >= batch.num_columns() {
                return Err(RustNumError::IndexError(format!(
                    "Column index {} out of bounds (num_columns: {})",
                    column, batch.num_columns()
                )));
            }
            
            let column_array = batch.column(column);
            Self::from_arrow(column_array.clone())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_record_batch(&self, field_name: &str) -> Result<RecordBatch, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let arrow_array = self.to_arrow()?;
            let field = Field::new(field_name, ArrowDataType::Float32, false);
            let schema = Schema::new(vec![field]);
            
            RecordBatch::try_new(Arc::new(schema), vec![arrow_array])
                .map_err(|e| RustNumError::ConversionError(format!("Failed to create RecordBatch: {}", e)))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
}

impl ArrowIntegration<f64> for RustArray<f64> {
    fn from_arrow(arrow_array: ArrayRef) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            if !matches!(arrow_array.data_type(), ArrowDataType::Float64) {
                return Err(RustNumError::TypeMismatch {
                    expected: "Float64".into(),
                    got: format!("{:?}", arrow_array.data_type()),
                });
            }
            
            let float_array = arrow_array
                .as_any()
                .downcast_ref::<Float64Array>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to Float64Array".into()))?;
            
            let values = float_array.values();
            let data = f64::from_arrow_buffer(values.clone().into(), float_array.len())?;
            
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            let mut result = Self::new(vec![data.len()], super::array_impl::StorageOrder::RowMajor, pool)?;
            
            result.data_mut().copy_from_slice(&data);
            
            Ok(result)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_arrow(&self) -> Result<ArrayRef, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let buffer = f64::to_arrow_buffer(self.data())?;
            let array = Float64Array::new(buffer.into(), None);
            Ok(Arc::new(array))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn from_record_batch(batch: &RecordBatch, column: usize) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            if column >= batch.num_columns() {
                return Err(RustNumError::IndexError(format!(
                    "Column index {} out of bounds (num_columns: {})",
                    column, batch.num_columns()
                )));
            }
            
            let column_array = batch.column(column);
            Self::from_arrow(column_array.clone())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn to_record_batch(&self, field_name: &str) -> Result<RecordBatch, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let arrow_array = self.to_arrow()?;
            let field = Field::new(field_name, ArrowDataType::Float64, false);
            let schema = Schema::new(vec![field]);
            
            RecordBatch::try_new(Arc::new(schema), vec![arrow_array])
                .map_err(|e| RustNumError::ConversionError(format!("Failed to create RecordBatch: {}", e)))
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
}

// 为 RustArray<f64> 实现 ArrowCompute 特征
impl ArrowCompute for RustArray<f64> {
    fn arrow_add(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::add_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_sub(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::subtract_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::multiply_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_div(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::divide_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_sum(&self) -> Result<f64, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let arrow_array = self.to_arrow()?;
            let float_array = arrow_array.as_any().downcast_ref::<Float64Array>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to Float64Array".into()))?;
            
            use arrow::compute::sum;
            let result = sum(float_array)
                .ok_or_else(|| RustNumError::ComputationError("Sum computation failed".into()))?;
            Ok(result)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_mean(&self) -> Result<f64, RustNumError> {
        let sum = self.arrow_sum()?;
        let count = self.data().len() as f64;
        if count > 0.0 {
            Ok(sum / count)
        } else {
            Err(RustNumError::ComputationError("Cannot compute mean of empty array".into()))
        }
    }
    
    fn arrow_min(&self) -> Result<f64, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let arrow_array = self.to_arrow()?;
            let float_array = arrow_array.as_any().downcast_ref::<Float64Array>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to Float64Array".into()))?;
            
            use arrow::compute::min;
            let result = min(float_array)
                .ok_or_else(|| RustNumError::ComputationError("Min computation failed".into()))?;
            Ok(result)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_max(&self) -> Result<f64, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let arrow_array = self.to_arrow()?;
            let float_array = arrow_array.as_any().downcast_ref::<Float64Array>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to Float64Array".into()))?;
            
            use arrow::compute::max;
            let result = max(float_array)
                .ok_or_else(|| RustNumError::ComputationError("Max computation failed".into()))?;
            Ok(result)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_std(&self) -> Result<f64, RustNumError> {
        let var = self.arrow_var()?;
        Ok(var.sqrt())
    }
    
    fn arrow_var(&self) -> Result<f64, RustNumError> {
        let mean = self.arrow_mean()?;
        let data = self.data();
        let sum_sq_diff: f64 = data.iter().map(|&x| (x - mean).powi(2)).sum();
        let count = data.len() as f64;
        if count > 1.0 {
            Ok(sum_sq_diff / (count - 1.0))
        } else {
            Err(RustNumError::ComputationError("Cannot compute variance with less than 2 elements".into()))
        }
    }
    
    fn arrow_eq(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = eq(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Equal comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn arrow_ne(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = neq(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Not-equal comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }

    fn arrow_gt(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = gt(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Greater-than comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }

    fn arrow_ge(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = gt_eq(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Greater-equal comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }

    fn arrow_lt(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = lt(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Less-than comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }

    fn arrow_le(&self, other: &Self) -> Result<Vec<bool>, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            
            let result_array = lt_eq(&left_arrow, &right_arrow)
                .map_err(|e| RustNumError::ComputationError(format!("Less-equal comparison failed: {}", e)))?;
            
            let bool_array = result_array.as_any().downcast_ref::<arrow::array::BooleanArray>()
                .ok_or_else(|| RustNumError::ConversionError("Failed to downcast to BooleanArray".into()))?;
            
            Ok((0..bool_array.len()).map(|i| bool_array.value(i)).collect())
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    fn arrow_filter(&self, mask: &[bool]) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let arrow_array = self.to_arrow()?;
            let bool_array = arrow::array::BooleanArray::from(mask.to_vec());
            
            use arrow::compute::filter;
            let filtered_array = filter(&arrow_array, &bool_array)
                .map_err(|e| RustNumError::ComputationError(format!("Filter operation failed: {}", e)))?;
            
            Self::from_arrow(filtered_array)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
    
    fn arrow_take(&self, indices: &[usize]) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow-compute")]
        {
            let arrow_array = self.to_arrow()?;
            let indices_array = arrow::array::UInt64Array::from(
                indices.iter().map(|&i| i as u64).collect::<Vec<_>>()
            );
            
            use arrow::compute::take;
            let taken_array = take(&arrow_array, &indices_array, None)
                .map_err(|e| RustNumError::ComputationError(format!("Take operation failed: {}", e)))?;
            
            Self::from_arrow(taken_array)
        }
        #[cfg(not(feature = "arrow-compute"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow-compute".into()))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;

    #[test]
    fn test_arrow_type_mapping() {
        // 测试数据类型映射
        assert_eq!(f32::arrow_data_type(), ArrowDataType::Float32);
        assert_eq!(f64::arrow_data_type(), ArrowDataType::Float64);
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_arrow_integration_f32() {
        // 创建测试数据
        let data = vec![1.0f32, 2.0, 3.0, 4.0, 5.0];
        let rust_array = RustArray::<f32>::from_vec(data.clone(), vec![5]).unwrap();

        // 转换为 Arrow
        let arrow_array = rust_array.to_arrow().unwrap();

        // 验证 Arrow 数组
        assert_eq!(arrow_array.len(), 5);
        assert_eq!(arrow_array.data_type(), &ArrowDataType::Float32);

        // 从 Arrow 转换回来
        let converted_back = RustArray::<f32>::from_arrow(arrow_array).unwrap();

        // 验证数据一致性
        assert_eq!(converted_back.shape(), &[5]);
        for (i, &val) in converted_back.data().iter().enumerate() {
            assert_eq!(val, data[i]);
        }
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_arrow_integration_f64() {
        let data = vec![1.0f64, 2.0, 3.0, 4.0, 5.0];
        let rust_array = RustArray::<f64>::from_vec(data.clone(), vec![5]).unwrap();

        let arrow_array = rust_array.to_arrow().unwrap();
        assert_eq!(arrow_array.len(), 5);
        assert_eq!(arrow_array.data_type(), &ArrowDataType::Float64);

        let converted_back = RustArray::<f64>::from_arrow(arrow_array).unwrap();
        assert_eq!(converted_back.shape(), &[5]);
        for (i, &val) in converted_back.data().iter().enumerate() {
            assert_eq!(val, data[i]);
        }
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_record_batch_integration() {
        // 创建测试数组
        let rust_array = RustArray::<f32>::full(&[3], 2.5).unwrap();

        // 转换为 RecordBatch
        let record_batch = rust_array.to_record_batch("test_column").unwrap();

        // 验证 RecordBatch
        assert_eq!(record_batch.num_columns(), 1);
        assert_eq!(record_batch.num_rows(), 3);
        assert_eq!(record_batch.schema().field(0).name(), "test_column");

        // 从 RecordBatch 转换回来
        let converted_back = RustArray::<f32>::from_record_batch(&record_batch, 0).unwrap();

        // 验证数据
        assert_eq!(converted_back.shape(), &[3]);
        for &val in converted_back.data() {
            assert_eq!(val, 2.5);
        }
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_arrow_compute_operations() {
        // 创建测试数组
        let a = RustArray::<f32>::full(&[3], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[3], 3.0).unwrap();

        // 测试 Arrow 计算引擎
        let add_result = a.arrow_add(&b).unwrap();
        let sub_result = a.arrow_sub(&b).unwrap();
        let mul_result = a.arrow_mul(&b).unwrap();
        let div_result = a.arrow_div(&b).unwrap();

        // 验证结果
        for &val in add_result.data() {
            assert_eq!(val, 5.0); // 2 + 3 = 5
        }

        for &val in sub_result.data() {
            assert_eq!(val, -1.0); // 2 - 3 = -1
        }

        for &val in mul_result.data() {
            assert_eq!(val, 6.0); // 2 * 3 = 6
        }

        for &val in div_result.data() {
            assert!((val - 2.0/3.0).abs() < 1e-6); // 2 / 3 ≈ 0.667
        }
    }

    #[test]
    fn test_error_handling() {
        // 测试特性未启用的错误
        #[cfg(not(feature = "arrow"))]
        {
            let rust_array = RustArray::<f32>::ones(&[3]).unwrap();
            assert!(rust_array.to_arrow().is_err());
        }

        // 测试类型不匹配错误（需要在启用 arrow 特性时测试）
        #[cfg(feature = "arrow")]
        {
            // 创建一个 Int32Array
            let int_array = arrow::array::Int32Array::from(vec![1, 2, 3]);
            let int_array_ref: ArrayRef = Arc::new(int_array);

            // 尝试转换为 f32 RustArray 应该失败
            let result = RustArray::<f32>::from_arrow(int_array_ref);
            assert!(result.is_err());

            if let Err(RustNumError::TypeMismatch { expected, got }) = result {
                assert_eq!(expected, "Float32");
                assert!(got.contains("Int32"));
            } else {
                panic!("Expected TypeMismatch error");
            }
        }
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_zero_copy_optimization() {
        // 这个测试验证零拷贝转换的概念
        // 在真实实现中，我们应该确保数据指针相同

        let data = vec![1.0f32, 2.0, 3.0, 4.0, 5.0];
        let rust_array = RustArray::<f32>::from_vec(data.clone(), vec![5]).unwrap();

        // 转换为 Arrow 并立即转换回来
        let arrow_array = rust_array.to_arrow().unwrap();
        let converted_back = RustArray::<f32>::from_arrow(arrow_array).unwrap();

        // 验证数据完整性
        assert_eq!(rust_array.data(), converted_back.data());

        // 在真实的零拷贝实现中，我们还应该验证：
        // assert_eq!(rust_array.data().as_ptr(), converted_back.data().as_ptr());
    }

    #[test]
    #[cfg(feature = "arrow")]
    fn test_large_array_performance() {
        // 测试大数组的转换性能
        let size = 10000;
        let data: Vec<f64> = (0..size).map(|i| i as f64).collect();
        let rust_array = RustArray::<f64>::from_vec(data.clone(), vec![size]).unwrap();

        // 测试转换时间（在实际基准测试中应该使用 criterion）
        let start = std::time::Instant::now();
        let arrow_array = rust_array.to_arrow().unwrap();
        let to_arrow_time = start.elapsed();

        let start = std::time::Instant::now();
        let _converted_back = RustArray::<f64>::from_arrow(arrow_array).unwrap();
        let from_arrow_time = start.elapsed();

        // 验证转换时间合理（这里只是示例，实际阈值需要根据性能要求调整）
        assert!(to_arrow_time.as_millis() < 100);
        assert!(from_arrow_time.as_millis() < 100);

        println!("Large array conversion performance:");
        println!("  To Arrow: {:?}", to_arrow_time);
        println!("  From Arrow: {:?}", from_arrow_time);
    }
}

/// Arrow 计算特征
pub trait ArrowCompute {
    /// 使用 Arrow 计算内核进行向量化运算
    fn arrow_add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    fn arrow_sub(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    fn arrow_mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    fn arrow_div(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// Arrow 聚合运算
    fn arrow_sum(&self) -> Result<f64, RustNumError>;
    fn arrow_mean(&self) -> Result<f64, RustNumError>;
    fn arrow_min(&self) -> Result<f64, RustNumError>;
    fn arrow_max(&self) -> Result<f64, RustNumError>;
    fn arrow_std(&self) -> Result<f64, RustNumError>;
    fn arrow_var(&self) -> Result<f64, RustNumError>;
    
    /// Arrow 比较运算
    fn arrow_eq(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    fn arrow_ne(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    fn arrow_gt(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    fn arrow_ge(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    fn arrow_lt(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    fn arrow_le(&self, other: &Self) -> Result<Vec<bool>, RustNumError>;
    
    /// Arrow 过滤和选择
    fn arrow_filter(&self, mask: &[bool]) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    fn arrow_take(&self, indices: &[usize]) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

/// Arrow 计算集成
pub struct ArrowComputeEngine;

impl ArrowComputeEngine {
    /// 使用 Arrow 计算引擎进行数组运算
    #[cfg(feature = "arrow")]
    pub fn add_arrays(left: &ArrayRef, right: &ArrayRef) -> Result<ArrayRef, RustNumError> {
        add(left, right)
            .map_err(|e| RustNumError::ComputationError(format!("Arrow compute error: {}", e)))
    }
    
    #[cfg(feature = "arrow")]
    pub fn subtract_arrays(left: &ArrayRef, right: &ArrayRef) -> Result<ArrayRef, RustNumError> {
        subtract(left, right)
            .map_err(|e| RustNumError::ComputationError(format!("Arrow compute error: {}", e)))
    }
    
    #[cfg(feature = "arrow")]
    pub fn multiply_arrays(left: &ArrayRef, right: &ArrayRef) -> Result<ArrayRef, RustNumError> {
        multiply(left, right)
            .map_err(|e| RustNumError::ComputationError(format!("Arrow compute error: {}", e)))
    }
    
    #[cfg(feature = "arrow")]
    pub fn divide_arrays(left: &ArrayRef, right: &ArrayRef) -> Result<ArrayRef, RustNumError> {
        divide(left, right)
            .map_err(|e| RustNumError::ComputationError(format!("Arrow compute error: {}", e)))
    }
}

/// 便利函数：为 RustArray 添加 Arrow 计算支持
impl<T> RustArray<T> 
where 
    T: ArrowTypeMapping + Copy + Default,
    Self: ArrowIntegration<T>,
{
    /// 使用 Arrow 计算引擎进行加法
    pub fn arrow_add(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::add_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    /// 使用 Arrow 计算引擎进行减法
    pub fn arrow_sub(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::subtract_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    /// 使用 Arrow 计算引擎进行乘法
    pub fn arrow_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::multiply_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
    
    /// 使用 Arrow 计算引擎进行除法
    pub fn arrow_div(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "arrow")]
        {
            let left_arrow = self.to_arrow()?;
            let right_arrow = other.to_arrow()?;
            let result_arrow = ArrowComputeEngine::divide_arrays(&left_arrow, &right_arrow)?;
            Self::from_arrow(result_arrow)
        }
        #[cfg(not(feature = "arrow"))]
        {
            Err(RustNumError::FeatureNotEnabled("arrow".into()))
        }
    }
}
