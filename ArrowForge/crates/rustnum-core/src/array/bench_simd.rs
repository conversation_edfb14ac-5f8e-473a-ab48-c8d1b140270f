//! SIMD 性能基准测试
//! 
//! 比较标量运算和 SIMD 运算的性能差异

use std::time::Instant;
use super::array_impl::RustArray;
use super::creation::ArrayCreation;
use super::math_ops::ArrayMath;
use super::simd_math::SimdArrayMath;

/// 性能测试结果
#[derive(Debug)]
pub struct BenchmarkResult {
    pub operation: String,
    pub array_size: usize,
    pub scalar_time_ns: u128,
    pub simd_time_ns: u128,
    pub speedup: f64,
}

impl BenchmarkResult {
    pub fn new(operation: String, array_size: usize, scalar_time_ns: u128, simd_time_ns: u128) -> Self {
        let speedup = if simd_time_ns > 0 {
            scalar_time_ns as f64 / simd_time_ns as f64
        } else {
            0.0
        };
        
        Self {
            operation,
            array_size,
            scalar_time_ns,
            simd_time_ns,
            speedup,
        }
    }
    
    pub fn print(&self) {
        println!("📊 {} (size: {})", self.operation, self.array_size);
        println!("   标量运算: {:>10} ns", self.scalar_time_ns);
        println!("   SIMD运算: {:>10} ns", self.simd_time_ns);
        println!("   加速比:   {:>10.2}x", self.speedup);
        if self.speedup > 1.0 {
            println!("   🚀 SIMD 更快!");
        } else if self.speedup < 1.0 {
            println!("   🐌 标量更快");
        } else {
            println!("   ⚖️  性能相当");
        }
        println!();
    }
}

/// SIMD 性能基准测试器
pub struct SimdBenchmark;

impl SimdBenchmark {
    /// 运行加法性能测试
    pub fn bench_addition(size: usize, iterations: usize) -> BenchmarkResult {
        let a = RustArray::<f32>::ones(&[size]).unwrap();
        let b = RustArray::<f32>::ones(&[size]).unwrap();
        
        // 预热
        for _ in 0..10 {
            let _ = a.add(&b).unwrap();
            let _ = a.simd_add(&b).unwrap();
        }
        
        // 测试标量运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.add(&b).unwrap();
        }
        let scalar_time = start.elapsed().as_nanos();
        
        // 测试 SIMD 运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.simd_add(&b).unwrap();
        }
        let simd_time = start.elapsed().as_nanos();
        
        BenchmarkResult::new(
            "数组加法".to_string(),
            size,
            scalar_time,
            simd_time,
        )
    }
    
    /// 运行乘法性能测试
    pub fn bench_multiplication(size: usize, iterations: usize) -> BenchmarkResult {
        let a = RustArray::<f32>::full(&[size], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[size], 3.0).unwrap();
        
        // 预热
        for _ in 0..10 {
            let _ = a.mul(&b).unwrap();
            let _ = a.simd_mul(&b).unwrap();
        }
        
        // 测试标量运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.mul(&b).unwrap();
        }
        let scalar_time = start.elapsed().as_nanos();
        
        // 测试 SIMD 运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.simd_mul(&b).unwrap();
        }
        let simd_time = start.elapsed().as_nanos();
        
        BenchmarkResult::new(
            "数组乘法".to_string(),
            size,
            scalar_time,
            simd_time,
        )
    }
    
    /// 运行标量运算性能测试
    pub fn bench_scalar_operations(size: usize, iterations: usize) -> BenchmarkResult {
        let a = RustArray::<f32>::ones(&[size]).unwrap();
        
        // 预热
        for _ in 0..10 {
            let _ = a.add_scalar(5.0).unwrap();
            let _ = a.simd_add_scalar(5.0).unwrap();
        }
        
        // 测试标量运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.add_scalar(5.0).unwrap();
        }
        let scalar_time = start.elapsed().as_nanos();
        
        // 测试 SIMD 运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.simd_add_scalar(5.0).unwrap();
        }
        let simd_time = start.elapsed().as_nanos();
        
        BenchmarkResult::new(
            "标量加法".to_string(),
            size,
            scalar_time,
            simd_time,
        )
    }
    
    /// 运行智能运算性能测试
    pub fn bench_smart_operations(size: usize, iterations: usize) -> BenchmarkResult {
        let a = RustArray::<f32>::ones(&[size]).unwrap();
        let b = RustArray::<f32>::ones(&[size]).unwrap();
        
        // 预热
        for _ in 0..10 {
            let _ = a.add(&b).unwrap();
            let _ = a.smart_add(&b).unwrap();
        }
        
        // 测试普通运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.add(&b).unwrap();
        }
        let normal_time = start.elapsed().as_nanos();
        
        // 测试智能运算
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = a.smart_add(&b).unwrap();
        }
        let smart_time = start.elapsed().as_nanos();
        
        BenchmarkResult::new(
            "智能加法".to_string(),
            size,
            normal_time,
            smart_time,
        )
    }
    
    /// 运行完整的性能测试套件
    pub fn run_full_benchmark() {
        println!("🚀 RustNum SIMD 性能基准测试");
        println!("================================");
        println!();
        
        let sizes = vec![64, 256, 1024, 4096, 16384];
        let iterations = 1000;
        
        for &size in &sizes {
            println!("📏 数组大小: {}", size);
            println!("🔄 迭代次数: {}", iterations);
            println!();
            
            // 加法测试
            let add_result = Self::bench_addition(size, iterations);
            add_result.print();
            
            // 乘法测试
            let mul_result = Self::bench_multiplication(size, iterations);
            mul_result.print();
            
            // 标量运算测试
            let scalar_result = Self::bench_scalar_operations(size, iterations);
            scalar_result.print();
            
            // 智能运算测试
            let smart_result = Self::bench_smart_operations(size, iterations);
            smart_result.print();
            
            println!("{}", "─".repeat(50));
            println!();
        }
        
        println!("🎉 性能测试完成！");
    }
}

/// 快速性能验证
pub fn quick_simd_test() {
    println!("⚡ 快速 SIMD 性能验证");
    println!("====================");
    
    let size = 1024;
    let a = RustArray::<f32>::ones(&[size]).unwrap();
    let b = RustArray::<f32>::ones(&[size]).unwrap();
    
    // 测试正确性
    let scalar_result = a.add(&b).unwrap();
    let simd_result = a.simd_add(&b).unwrap();
    
    // 验证结果一致性
    let mut results_match = true;
    for (i, (&scalar_val, &simd_val)) in scalar_result.data().iter().zip(simd_result.data().iter()).enumerate() {
        if (scalar_val - simd_val).abs() > 1e-6 {
            println!("❌ 结果不匹配 at index {}: scalar={}, simd={}", i, scalar_val, simd_val);
            results_match = false;
            break;
        }
    }
    
    if results_match {
        println!("✅ SIMD 运算结果正确");
    } else {
        println!("❌ SIMD 运算结果错误");
        return;
    }
    
    // 简单性能测试
    let iterations = 100;
    
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = a.add(&b).unwrap();
    }
    let scalar_time = start.elapsed();
    
    let start = Instant::now();
    for _ in 0..iterations {
        let _ = a.simd_add(&b).unwrap();
    }
    let simd_time = start.elapsed();
    
    println!("📊 性能对比 ({}次迭代):", iterations);
    println!("   标量运算: {:?}", scalar_time);
    println!("   SIMD运算: {:?}", simd_time);
    
    if simd_time < scalar_time {
        let speedup = scalar_time.as_nanos() as f64 / simd_time.as_nanos() as f64;
        println!("   🚀 SIMD 加速比: {:.2}x", speedup);
    } else {
        println!("   📝 注意: 在小数组上 SIMD 可能不会显示明显优势");
    }
    
    println!();
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_simd_benchmark() {
        // 运行快速测试确保基准测试代码正常工作
        quick_simd_test();
        
        // 运行小规模基准测试
        let result = SimdBenchmark::bench_addition(256, 10);
        assert!(result.scalar_time_ns > 0);
        assert!(result.simd_time_ns > 0);
        assert!(result.speedup >= 0.0);
    }
}
