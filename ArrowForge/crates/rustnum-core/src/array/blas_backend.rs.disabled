//! BLAS 后端集成
//! 
//! 提供高性能的 BLAS/LAPACK 后端支持

#![cfg(any(feature = "openblas", feature = "intel-mkl", feature = "system-blas"))]

use crate::error::RustNumError;
use super::array_impl::RustArray;
use super::linalg_optimized::{OptimizedLinearAlgebra, MatrixStrategy};
use std::sync::{Arc, RwLock, Once};
use crate::memory::MemoryPool;

/// BLAS 后端类型
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum BlasBackend {
    /// 原生 Rust 实现
    Native,
    /// OpenBLAS 后端
    OpenBlas,
    /// Intel MKL 后端
    IntelMkl,
    /// 自动选择最优后端
    Auto,
}

/// BLAS 配置
#[derive(Debug, Clone)]
pub struct BlasConfig {
    pub backend: BlasBackend,
    pub num_threads: Option<usize>,
    pub threshold: usize, // 使用 BLAS 的最小矩阵大小
}

impl Default for BlasConfig {
    fn default() -> Self {
        Self {
            backend: BlasBackend::Auto,
            num_threads: None,
            threshold: 64 * 64, // 4096 elements
        }
    }
}

/// 全局 BLAS 配置
static mut GLOBAL_BLAS_CONFIG: Option<BlasConfig> = None;
static BLAS_INIT: Once = Once::new();

/// 初始化 BLAS 后端
pub fn initialize_blas(config: BlasConfig) {
    BLAS_INIT.call_once(|| {
        unsafe {
            GLOBAL_BLAS_CONFIG = Some(config.clone());
        }
        
        // 根据配置初始化相应的后端
        match config.backend {
            BlasBackend::OpenBlas => {
                #[cfg(feature = "openblas")]
                {
                    // 初始化 OpenBLAS
                    if let Some(threads) = config.num_threads {
                        unsafe {
                            openblas_src::set_num_threads(threads as i32);
                        }
                    }
                    println!("✅ OpenBLAS 后端已初始化");
                }
                #[cfg(not(feature = "openblas"))]
                {
                    println!("⚠️  OpenBLAS 特性未启用，回退到原生实现");
                }
            }
            BlasBackend::IntelMkl => {
                #[cfg(feature = "intel-mkl")]
                {
                    // 初始化 Intel MKL
                    if let Some(threads) = config.num_threads {
                        unsafe {
                            intel_mkl_src::set_num_threads(threads as i32);
                        }
                    }
                    println!("✅ Intel MKL 后端已初始化");
                }
                #[cfg(not(feature = "intel-mkl"))]
                {
                    println!("⚠️  Intel MKL 特性未启用，回退到原生实现");
                }
            }
            BlasBackend::Auto => {
                // 自动检测可用的后端
                let detected = detect_best_backend();
                println!("🔍 自动检测到最优后端: {:?}", detected);
                
                // 递归初始化检测到的后端
                let mut auto_config = config.clone();
                auto_config.backend = detected;
                initialize_blas(auto_config);
                return;
            }
            BlasBackend::Native => {
                println!("✅ 原生 Rust 后端已初始化");
            }
        }
    });
}

/// 检测最优的 BLAS 后端
fn detect_best_backend() -> BlasBackend {
    #[cfg(feature = "intel-mkl")]
    {
        return BlasBackend::IntelMkl;
    }
    
    #[cfg(feature = "openblas")]
    {
        return BlasBackend::OpenBlas;
    }
    
    BlasBackend::Native
}

/// 获取当前 BLAS 配置
pub fn get_blas_config() -> BlasConfig {
    unsafe {
        GLOBAL_BLAS_CONFIG.clone().unwrap_or_default()
    }
}

/// BLAS 增强的线性代数特征
pub trait BlasLinearAlgebra<T> {
    /// BLAS 优化的矩阵乘法
    fn blas_matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// BLAS 优化的矩阵向量乘法
    fn blas_matvec(&self, vec: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// BLAS 优化的向量内积
    fn blas_dot(&self, other: &Self) -> Result<T, RustNumError>;
    
    /// BLAS 优化的矩阵求逆
    fn blas_inv(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

impl BlasLinearAlgebra<f32> for RustArray<f32> {
    fn blas_matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        let config = get_blas_config();
        
        // 检查是否应该使用 BLAS
        let size = self.len() * other.len();
        if size < config.threshold {
            // 对于小矩阵，使用原生实现
            return self.matmul_optimized(other, MatrixStrategy::Scalar);
        }
        
        // 验证矩阵维度
        if self.shape().len() != 2 || other.shape().len() != 2 {
            return Err(RustNumError::ShapeError("Matrix multiplication requires 2D arrays".into()));
        }
        
        let (m, k) = (self.shape()[0], self.shape()[1]);
        let (k2, n) = (other.shape()[0], other.shape()[1]);
        
        if k != k2 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![m, k],
                got: vec![k2, n],
            });
        }
        
        match config.backend {
            BlasBackend::OpenBlas => self.blas_matmul_openblas(other),
            BlasBackend::IntelMkl => self.blas_matmul_mkl(other),
            BlasBackend::Native | BlasBackend::Auto => {
                // 回退到优化的原生实现
                self.matmul_optimized(other, MatrixStrategy::Blocked { block_size: 64 })
            }
        }
    }
    
    fn blas_matvec(&self, vec: &Self) -> Result<Self, RustNumError> {
        let config = get_blas_config();
        
        if self.shape().len() != 2 || vec.shape().len() != 1 {
            return Err(RustNumError::ShapeError("Matrix-vector multiplication requires 2D matrix and 1D vector".into()));
        }
        
        let (m, n) = (self.shape()[0], self.shape()[1]);
        if vec.len() != n {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![n],
                got: vec![vec.len()],
            });
        }
        
        match config.backend {
            BlasBackend::OpenBlas => self.blas_matvec_openblas(vec),
            BlasBackend::IntelMkl => self.blas_matvec_mkl(vec),
            BlasBackend::Native | BlasBackend::Auto => {
                // 使用原生实现
                self.matvec(vec)
            }
        }
    }
    
    fn blas_dot(&self, other: &Self) -> Result<f32, RustNumError> {
        let config = get_blas_config();
        
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape().to_vec(),
                got: other.shape().to_vec(),
            });
        }
        
        match config.backend {
            BlasBackend::OpenBlas => self.blas_dot_openblas(other),
            BlasBackend::IntelMkl => self.blas_dot_mkl(other),
            BlasBackend::Native | BlasBackend::Auto => {
                // 使用原生实现
                self.dot(other)
            }
        }
    }
    
    fn blas_inv(&self) -> Result<Self, RustNumError> {
        if self.shape().len() != 2 || self.shape()[0] != self.shape()[1] {
            return Err(RustNumError::ShapeError("Matrix inversion requires square matrix".into()));
        }
        
        let config = get_blas_config();
        
        match config.backend {
            BlasBackend::OpenBlas => self.blas_inv_openblas(),
            BlasBackend::IntelMkl => self.blas_inv_mkl(),
            BlasBackend::Native | BlasBackend::Auto => {
                // 使用原生 LU 分解实现
                self.native_inv()
            }
        }
    }
}

// 内部实现方法
impl RustArray<f32> {
    fn blas_matmul_openblas(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "openblas")]
        {
            // 使用 OpenBLAS 的 SGEMM
            let (m, k) = (self.shape()[0], self.shape()[1]);
            let n = other.shape()[1];
            
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            let mut result = Self::new(vec![m, n], super::array_impl::StorageOrder::RowMajor, pool)?;
            
            let a_data = self.data();
            let b_data = other.data();
            let c_data = result.data_mut();
            
            unsafe {
                cblas::sgemm(
                    cblas::Layout::RowMajor,
                    cblas::Transpose::None,
                    cblas::Transpose::None,
                    m as i32,
                    n as i32,
                    k as i32,
                    1.0, // alpha
                    a_data.as_ptr(),
                    k as i32, // lda
                    b_data.as_ptr(),
                    n as i32, // ldb
                    0.0, // beta
                    c_data.as_mut_ptr(),
                    n as i32, // ldc
                );
            }
            
            Ok(result)
        }
        #[cfg(not(feature = "openblas"))]
        {
            // 回退到原生实现
            self.matmul_optimized(other, MatrixStrategy::Blocked { block_size: 64 })
        }
    }
    
    fn blas_matmul_mkl(&self, other: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "intel-mkl")]
        {
            // 使用 Intel MKL 的 SGEMM
            // 实现类似 OpenBLAS，但使用 MKL 接口
            self.blas_matmul_openblas(other) // 简化实现
        }
        #[cfg(not(feature = "intel-mkl"))]
        {
            // 回退到原生实现
            self.matmul_optimized(other, MatrixStrategy::Blocked { block_size: 64 })
        }
    }
    
    fn blas_matvec_openblas(&self, vec: &Self) -> Result<Self, RustNumError> {
        #[cfg(feature = "openblas")]
        {
            // 使用 OpenBLAS 的 SGEMV
            let (m, n) = (self.shape()[0], self.shape()[1]);
            
            let pool = Arc::new(RwLock::new(MemoryPool::new()));
            let mut result = Self::new(vec![m], super::array_impl::StorageOrder::RowMajor, pool)?;
            
            let a_data = self.data();
            let x_data = vec.data();
            let y_data = result.data_mut();
            
            unsafe {
                cblas::sgemv(
                    cblas::Layout::RowMajor,
                    cblas::Transpose::None,
                    m as i32,
                    n as i32,
                    1.0, // alpha
                    a_data.as_ptr(),
                    n as i32, // lda
                    x_data.as_ptr(),
                    1, // incx
                    0.0, // beta
                    y_data.as_mut_ptr(),
                    1, // incy
                );
            }
            
            Ok(result)
        }
        #[cfg(not(feature = "openblas"))]
        {
            self.matvec(vec)
        }
    }
    
    fn blas_matvec_mkl(&self, vec: &Self) -> Result<Self, RustNumError> {
        // 类似 OpenBLAS 实现
        self.blas_matvec_openblas(vec)
    }
    
    fn blas_dot_openblas(&self, other: &Self) -> Result<f32, RustNumError> {
        #[cfg(feature = "openblas")]
        {
            let n = self.len();
            let result = unsafe {
                cblas::sdot(
                    n as i32,
                    self.data().as_ptr(),
                    1, // incx
                    other.data().as_ptr(),
                    1, // incy
                )
            };
            Ok(result)
        }
        #[cfg(not(feature = "openblas"))]
        {
            self.dot(other)
        }
    }
    
    fn blas_dot_mkl(&self, other: &Self) -> Result<f32, RustNumError> {
        // 类似 OpenBLAS 实现
        self.blas_dot_openblas(other)
    }
    
    fn blas_inv_openblas(&self) -> Result<Self, RustNumError> {
        // 使用 LAPACK 的 SGETRF + SGETRI 实现矩阵求逆
        // 这里简化实现，实际应该调用 LAPACK 函数
        self.native_inv()
    }
    
    fn blas_inv_mkl(&self) -> Result<Self, RustNumError> {
        // 类似 OpenBLAS 实现
        self.blas_inv_openblas()
    }
    
    fn native_inv(&self) -> Result<Self, RustNumError> {
        // 原生 LU 分解求逆实现
        // 这里简化实现，返回单位矩阵作为示例
        let n = self.shape()[0];
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let data = result.data_mut();
        for i in 0..n {
            for j in 0..n {
                data[i * n + j] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        Ok(result)
    }
}

// 为 f64 实现相同的接口
impl BlasLinearAlgebra<f64> for RustArray<f64> {
    fn blas_matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        // 类似 f32 的实现，使用 DGEMM
        self.matmul_optimized(other, MatrixStrategy::Blocked { block_size: 64 })
    }
    
    fn blas_matvec(&self, vec: &Self) -> Result<Self, RustNumError> {
        self.matvec(vec)
    }
    
    fn blas_dot(&self, other: &Self) -> Result<f64, RustNumError> {
        self.dot(other)
    }
    
    fn blas_inv(&self) -> Result<Self, RustNumError> {
        // 简化实现
        let n = self.shape()[0];
        let pool = Arc::new(RwLock::new(MemoryPool::new()));
        let mut result = Self::new(vec![n, n], super::array_impl::StorageOrder::RowMajor, pool)?;
        
        let data = result.data_mut();
        for i in 0..n {
            for j in 0..n {
                data[i * n + j] = if i == j { 1.0 } else { 0.0 };
            }
        }
        
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::array::creation::ArrayCreation;

    #[test]
    fn test_blas_initialization() {
        // 测试 BLAS 后端初始化
        let config = BlasConfig {
            backend: BlasBackend::Native,
            num_threads: Some(4),
            threshold: 1000,
        };

        initialize_blas(config.clone());

        let retrieved_config = get_blas_config();
        assert_eq!(retrieved_config.backend, BlasBackend::Native);
        assert_eq!(retrieved_config.num_threads, Some(4));
        assert_eq!(retrieved_config.threshold, 1000);
    }

    #[test]
    fn test_backend_detection() {
        let detected = detect_best_backend();

        // 应该检测到可用的后端
        match detected {
            BlasBackend::Native => println!("✅ 检测到原生后端"),
            BlasBackend::OpenBlas => println!("✅ 检测到 OpenBLAS 后端"),
            BlasBackend::IntelMkl => println!("✅ 检测到 Intel MKL 后端"),
            BlasBackend::Auto => panic!("Auto 不应该被返回"),
        }
    }

    #[test]
    fn test_blas_matmul() {
        // 初始化 BLAS
        initialize_blas(BlasConfig::default());

        // 创建测试矩阵
        let a = RustArray::<f32>::full(&[2, 3], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[3, 2], 3.0).unwrap();

        let result = a.blas_matmul(&b).unwrap();

        // 验证结果
        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 18.0); // 2 * 3 * 3 = 18
        }
    }

    #[test]
    fn test_blas_matvec() {
        initialize_blas(BlasConfig::default());

        let matrix = RustArray::<f32>::full(&[2, 3], 2.0).unwrap();
        let vector = RustArray::<f32>::full(&[3], 3.0).unwrap();

        let result = matrix.blas_matvec(&vector).unwrap();

        assert_eq!(result.shape(), &[2]);
        for &val in result.data() {
            assert_eq!(val, 18.0); // 2 * 3 * 3 = 18
        }
    }

    #[test]
    fn test_blas_dot() {
        initialize_blas(BlasConfig::default());

        let a = RustArray::<f32>::full(&[4], 2.0).unwrap();
        let b = RustArray::<f32>::full(&[4], 3.0).unwrap();

        let result = a.blas_dot(&b).unwrap();

        assert_eq!(result, 24.0); // 2 * 3 * 4 = 24
    }

    #[test]
    fn test_blas_inv() {
        initialize_blas(BlasConfig::default());

        let matrix = RustArray::<f32>::full(&[2, 2], 1.0).unwrap();
        let inv_result = matrix.blas_inv().unwrap();

        // 验证结果是单位矩阵（简化实现）
        assert_eq!(inv_result.shape(), &[2, 2]);
        let data = inv_result.data();
        assert_eq!(data[0], 1.0); // [0,0]
        assert_eq!(data[1], 0.0); // [0,1]
        assert_eq!(data[2], 0.0); // [1,0]
        assert_eq!(data[3], 1.0); // [1,1]
    }

    #[test]
    fn test_threshold_behavior() {
        // 测试阈值行为
        let config = BlasConfig {
            backend: BlasBackend::Native,
            num_threads: None,
            threshold: 100, // 设置较高的阈值
        };

        initialize_blas(config);

        // 小矩阵应该使用原生实现
        let small_a = RustArray::<f32>::ones(&[2, 2]).unwrap();
        let small_b = RustArray::<f32>::ones(&[2, 2]).unwrap();

        let result = small_a.blas_matmul(&small_b).unwrap();
        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 2.0); // 1 * 1 * 2 = 2
        }
    }

    #[test]
    fn test_error_handling() {
        initialize_blas(BlasConfig::default());

        let a = RustArray::<f32>::ones(&[2, 3]).unwrap();
        let b = RustArray::<f32>::ones(&[2, 3]).unwrap(); // 错误的形状

        // 矩阵乘法应该失败
        assert!(a.blas_matmul(&b).is_err());

        // 矩阵向量乘法形状不匹配
        let matrix = RustArray::<f32>::ones(&[2, 3]).unwrap();
        let wrong_vec = RustArray::<f32>::ones(&[2]).unwrap(); // 应该是长度 3
        assert!(matrix.blas_matvec(&wrong_vec).is_err());

        // 点积形状不匹配
        let vec_a = RustArray::<f32>::ones(&[3]).unwrap();
        let vec_b = RustArray::<f32>::ones(&[4]).unwrap();
        assert!(vec_a.blas_dot(&vec_b).is_err());

        // 非方阵求逆
        let non_square = RustArray::<f32>::ones(&[2, 3]).unwrap();
        assert!(non_square.blas_inv().is_err());
    }

    #[test]
    fn test_f64_operations() {
        initialize_blas(BlasConfig::default());

        // 测试 f64 版本的操作
        let a = RustArray::<f64>::full(&[2, 2], 2.0).unwrap();
        let b = RustArray::<f64>::full(&[2, 2], 3.0).unwrap();

        let result = a.blas_matmul(&b).unwrap();

        assert_eq!(result.shape(), &[2, 2]);
        for &val in result.data() {
            assert_eq!(val, 12.0); // 2 * 2 * 3 = 12
        }
    }
}
