use crate::error::RustNumError as Error;
use num_traits::Float;
use libc::{c_char, c_double, c_int};
use crate::backend::bindings::{dgemm_, dgesvd_, dgeev_};

// 辅助函数，将bool转换为c_char
trait AsChar {
    fn as_char(&self) -> c_char;
}

impl AsChar for bool {
    fn as_char(&self) -> c_char {
        if *self { b'T' as c_char } else { b'N' as c_char }
    }
}

impl AsChar for char {
    fn as_char(&self) -> c_char {
        *self as c_char
    }
}

// 简单的错误解析器，用于处理LAPACK的info返回值
pub struct LapackErrorParser;

impl LapackErrorParser {
    pub fn parse_info(info: c_int, func_name: &str) -> Result<(), Error> {
        if info < 0 {
            Err(Error::LapackError(format!(
                "LAPACK function {} received an illegal value for argument {}.",
                func_name, -info
            )))
        } else if info > 0 {
            Err(Error::LapackError(format!(
                "LAPACK function {} did not converge. Info: {}",
                func_name, info
            )))
        } else {
            Ok(())
        }
    }
}

/// BLAS/LAPACK后端抽象
pub trait BlasBackend {
    type Scalar: Float;
    
    /// 矩阵乘法 (BLAS Level 3 GEMM)
    fn gemm(
        transa: bool,
        transb: bool,
        m: usize,
        n: usize,
        k: usize,
        alpha: Self::Scalar,
        a: &[Self::Scalar],
        lda: usize,
        b: &[Self::Scalar],
        ldb: usize,
        beta: Self::Scalar,
        c: &mut [Self::Scalar],
        ldc: usize,
    ) -> Result<(), Error>;

    /// SVD分解 (LAPACK GESVD)
    fn gesvd(
        _jobu: char,
        _jobvt: char,
        _m: usize,
        _n: usize,
        a: &mut [Self::Scalar],
        lda: usize,
        s: &mut [Self::Scalar],
        u: &mut [Self::Scalar],
        ldu: usize,
        vt: &mut [Self::Scalar],
        ldvt: usize,
        work: &mut [Self::Scalar],
        lwork: usize,
    ) -> Result<(), Error>;

    /// 特征值分解 (LAPACK GEEV)
    fn geev(
        _jobvl: char,
        _jobvr: char,
        _n: usize,
        a: &mut [Self::Scalar],
        lda: usize,
        wr: &mut [Self::Scalar],
        wi: &mut [Self::Scalar],
        vl: &mut [Self::Scalar],
        ldvl: usize,
        vr: &mut [Self::Scalar],
        ldvr: usize,
        work: &mut [Self::Scalar],
        lwork: usize,
    ) -> Result<(), Error>;
}

/// OpenBLAS后端实现
pub struct OpenBlasBackend;

impl BlasBackend for OpenBlasBackend {
    type Scalar = f64;

    fn gemm(
        transa: bool,
        transb: bool,
        m: usize,
        n: usize,
        k: usize,
        alpha: Self::Scalar,
        a: &[Self::Scalar],
        lda: usize,
        b: &[Self::Scalar],
        ldb: usize,
        beta: Self::Scalar,
        c: &mut [Self::Scalar],
        ldc: usize,
    ) -> Result<(), Error> {
        // TODO: 调用OpenBLAS的dgemm_函数
        let (transa_char, transb_char) = (transa.as_char(), transb.as_char());
        let (m_c, n_c, k_c) = (m as c_int, n as c_int, k as c_int);
        let (alpha_c, beta_c) = (alpha as c_double, beta as c_double);
        let (lda_c, ldb_c, ldc_c) = (lda as c_int, ldb as c_int, ldc as c_int);

        unsafe {
            dgemm_(
                &transa_char,
                &transb_char,
                &m_c,
                &n_c,
                &k_c,
                &alpha_c,
                a.as_ptr(),
                &lda_c,
                b.as_ptr(),
                &ldb_c,
                &beta_c,
                c.as_mut_ptr(),
                &ldc_c,
             );
         }
        LapackErrorParser::parse_info(0, "DGEMM")?; // DGEMM通常不返回info，这里假设成功
        Ok(())
    }

    fn gesvd(
        jobu: char,
        jobvt: char,
        m: usize,
        n: usize,
        a: &mut [Self::Scalar],
        lda: usize,
        s: &mut [Self::Scalar],
        u: &mut [Self::Scalar],
        ldu: usize,
        vt: &mut [Self::Scalar],
        ldvt: usize,
        work: &mut [Self::Scalar],
        lwork: usize,
    ) -> Result<(), Error> {
        // TODO: 调用OpenBLAS的dgesvd_函数
        let (jobu_char, jobvt_char) = (jobu.as_char(), jobvt.as_char());
        let (m_c, n_c) = (m as c_int, n as c_int);
        let (lda_c, ldu_c, ldvt_c) = (lda as c_int, ldu as c_int, ldvt as c_int);
        let lwork_c = lwork as c_int;
        let mut info = 0;

        unsafe {
            dgesvd_(
                &jobu_char,
                &jobvt_char,
                &m_c,
                &n_c,
                a.as_mut_ptr(),
                &lda_c,
                s.as_mut_ptr(),
                u.as_mut_ptr(),
                &ldu_c,
                vt.as_mut_ptr(),
                &ldvt_c,
                work.as_mut_ptr(),
                &lwork_c,
                &mut info,
             );
        }
        LapackErrorParser::parse_info(info, "DGESVD")?; 
        Ok(())
    }

    fn geev(
        jobvl: char,
        jobvr: char,
        n: usize,
        a: &mut [Self::Scalar],
        lda: usize,
        wr: &mut [Self::Scalar],
        wi: &mut [Self::Scalar],
        vl: &mut [Self::Scalar],
        ldvl: usize,
        vr: &mut [Self::Scalar],
        ldvr: usize,
        work: &mut [Self::Scalar],
        lwork: usize,
    ) -> Result<(), Error> {
        // TODO: 调用OpenBLAS的dgeev_函数
        let (jobvl_char, jobvr_char) = (jobvl.as_char(), jobvr.as_char());
        let n_c = n as c_int;
        let (lda_c, ldvl_c, ldvr_c) = (lda as c_int, ldvl as c_int, ldvr as c_int);
        let lwork_c = lwork as c_int;
        let mut info = 0;

        unsafe {
            dgeev_(
                &jobvl_char,
                &jobvr_char,
                &n_c,
                a.as_mut_ptr(),
                &lda_c,
                wr.as_mut_ptr(),
                wi.as_mut_ptr(),
                vl.as_mut_ptr(),
                &ldvl_c,
                vr.as_mut_ptr(),
                &ldvr_c,
                work.as_mut_ptr(),
                &lwork_c,
                &mut info,
             );
         }
        LapackErrorParser::parse_info(info, "DGEEV")?; 
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_openblas_gemm() {
        // TODO: 添加OpenBLAS gemm测试
    }

    #[test]
    fn test_openblas_svd() {
        // TODO: 添加OpenBLAS svd测试
    }

    #[test]
    fn test_openblas_eigendecomposition() {
        // TODO: 添加OpenBLAS eigendecomposition测试
    }
}
