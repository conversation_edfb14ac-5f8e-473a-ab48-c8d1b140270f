use libc::{c_char, c_double, c_int};
use crate::error::RustNumError;
use crate::backend::blas_lapack::LapackErrorParser;
use crate::backend::config::{BlasProvider, get_optimal_blas_provider};



/// 安全的BLAS函数包装
pub struct BlasWrapper {
    provider: BlasProvider,
}

#[link(name = "openblas")]
extern "C" {
    // BLAS Level 1
    pub fn ddot_(n: *const c_int, x: *const c_double, incx: *const c_int, 
                 y: *const c_double, incy: *const c_int) -> c_double;
    
    pub fn daxpy_(n: *const c_int, alpha: *const c_double, x: *const c_double, incx: *const c_int,
                  y: *mut c_double, incy: *const c_int);
    
    // BLAS Level 2
    pub fn dgemv_(trans: *const c_char, m: *const c_int, n: *const c_int,
                  alpha: *const c_double, a: *const c_double, lda: *const c_int,
                  x: *const c_double, incx: *const c_int,
                  beta: *const c_double, y: *mut c_double, incy: *const c_int);
    
    // BLAS Level 3
    pub fn dgemm_(transa: *const c_char, transb: *const c_char,
                  m: *const c_int, n: *const c_int, k: *const c_int,
                  alpha: *const c_double, a: *const c_double, lda: *const c_int,
                  b: *const c_double, ldb: *const c_int,
                  beta: *const c_double, c: *mut c_double, ldc: *const c_int);
    
    // LAPACK函数
    pub fn dgeev_(jobvl: *const c_char, jobvr: *const c_char,
                  n: *const c_int, a: *mut c_double, lda: *const c_int,
                  wr: *mut c_double, wi: *mut c_double,
                  vl: *mut c_double, ldvl: *const c_int,
                  vr: *mut c_double, ldvr: *const c_int,
                  work: *mut c_double, lwork: *const c_int,
                  info: *mut c_int);
    
    pub fn dgesvd_(jobu: *const c_char, jobvt: *const c_char,
                   m: *const c_int, n: *const c_int,
                   a: *mut c_double, lda: *const c_int,
                   s: *mut c_double,
                   u: *mut c_double, ldu: *const c_int,
                   vt: *mut c_double, ldvt: *const c_int,
                   work: *mut c_double, lwork: *const c_int,
                   info: *mut c_int);
}

// Intel MKL函数绑定
#[cfg(feature = "intel-mkl")]
#[link(name = "mkl_rt")]
extern "C" {
    // Intel MKL BLAS Level 1
    fn mkl_ddot_(n: *const c_int, x: *const c_double, incx: *const c_int, 
                 y: *const c_double, incy: *const c_int) -> c_double;
    
    fn mkl_daxpy_(n: *const c_int, alpha: *const c_double, x: *const c_double, incx: *const c_int,
                  y: *mut c_double, incy: *const c_int);
    
    // Intel MKL BLAS Level 2
    fn mkl_dgemv_(trans: *const c_char, m: *const c_int, n: *const c_int,
                  alpha: *const c_double, a: *const c_double, lda: *const c_int,
                  x: *const c_double, incx: *const c_int,
                  beta: *const c_double, y: *mut c_double, incy: *const c_int);
    
    // Intel MKL BLAS Level 3
    fn mkl_dgemm_(transa: *const c_char, transb: *const c_char,
                  m: *const c_int, n: *const c_int, k: *const c_int,
                  alpha: *const c_double, a: *const c_double, lda: *const c_int,
                  b: *const c_double, ldb: *const c_int,
                  beta: *const c_double, c: *mut c_double, ldc: *const c_int);
}

impl BlasWrapper {
    /// 创建新的BLAS包装器
    pub fn new() -> Self {
        Self {
            provider: get_optimal_blas_provider(),
        }
    }
    
    /// 向量点积
    pub fn dot(&self, x: &[f64], y: &[f64]) -> Result<f64, RustNumError> {
        if x.len() != y.len() {
            return Err(RustNumError::DimensionError("Vectors must have same length".into()));
        }

        let n = x.len() as i32;
        let inc = 1 as i32;

        unsafe {
            Ok(ddot_(
                &n as *const i32,
                x.as_ptr(),
                &inc as *const i32,
                y.as_ptr(),
                &inc as *const i32,
            ))
        }
    }

    pub fn geev(
        &self,
        jobvl: char,
        jobvr: char,
        n: usize,
        a: &mut [f64],
        lda: usize,
        wr: &mut [f64],
        wi: &mut [f64],
        vl: &mut [f64],
        ldvl: usize,
        vr: &mut [f64],
        ldvr: usize,
        work: &mut [f64],
        lwork: usize,
    ) -> Result<(), RustNumError> {
        let jobvl_char = jobvl as u8 as i8;
        let jobvr_char = jobvr as u8 as i8;

        let n_c = n as i32;
        let lda_c = lda as i32;
        let ldvl_c = ldvl as i32;
        let ldvr_c = ldvr as i32;
        let lwork_c = lwork as i32;
        let mut info = 0;

        unsafe {
            dgeev_(
                &jobvl_char as *const i8,
                &jobvr_char as *const i8,
                &n_c as *const i32,
                a.as_mut_ptr(),
                &lda_c as *const i32,
                wr.as_mut_ptr(),
                wi.as_mut_ptr(),
                vl.as_mut_ptr(),
                &ldvl_c as *const i32,
                vr.as_mut_ptr(),
                &ldvr_c as *const i32,
                work.as_mut_ptr(),
                &lwork_c as *const i32,
                &mut info as *mut i32,
            );
        }
        LapackErrorParser::parse_info(info, "dgeev_")?;
        Ok(())
    }

    /// 矩阵乘法
    pub fn gemm(
        &self,
        transa: bool,
        transb: bool,
        m: usize,
        n: usize,
        k: usize,
        alpha: f64,
        a: &[f64],
        lda: usize,
        b: &[f64],
        ldb: usize,
        beta: f64,
        c: &mut [f64],
        ldc: usize,
    ) -> Result<(), RustNumError> {
        let transa = if transa { b'T' } else { b'N' } as i8;
        let transb = if transb { b'T' } else { b'N' } as i8;

        unsafe {
            dgemm_(
                &transa as *const i8,
                &transb as *const i8,
                &(m as i32) as *const i32,
                &(n as i32) as *const i32,
                &(k as i32) as *const i32,
                &alpha,
                a.as_ptr(),
                &(lda as i32) as *const i32,
                b.as_ptr(),
                &(ldb as i32) as *const i32,
                &beta,
                c.as_mut_ptr(),
                &(ldc as i32) as *const i32,
            );
        }

        Ok(())
    }

    /// SVD分解
    pub fn svd(
        &self,
        jobu: char,
        jobvt: char,
        m: usize,
        n: usize,
        a: &mut [f64],
        lda: usize,
        s: &mut [f64],
        u: &mut [f64],
        ldu: usize,
        vt: &mut [f64],
        ldvt: usize,
        work: &mut [f64],
        lwork: usize,
    ) -> Result<(), RustNumError> {
        let jobu_char = jobu as c_char;
        let jobvt_char = jobvt as c_char;

        let m_c = m as c_int;
        let n_c = n as c_int;
        let lda_c = lda as c_int;
        let ldu_c = ldu as c_int;
        let ldvt_c = ldvt as c_int;
        let lwork_c = lwork as c_int;
        let mut info = 0 as c_int;

        unsafe {
            dgesvd_(
                &jobu_char as *const c_char,
                &jobvt_char as *const c_char,
                &m_c as *const c_int,
                &n_c as *const c_int,
                a.as_mut_ptr(),
                &lda_c as *const c_int,
                s.as_mut_ptr(),
                u.as_mut_ptr(),
                &ldu_c as *const c_int,
                vt.as_mut_ptr(),
                &ldvt_c as *const c_int,
                work.as_mut_ptr(),
                &lwork_c as *const c_int,
                &mut info as *mut c_int,
            );
        }
        LapackErrorParser::parse_info(info, "dgesvd_")?;
        Ok(())
    }
    
    pub fn get_provider(&self) -> BlasProvider {
        self.provider
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use approx::assert_relative_eq;

    #[test]
    fn test_dot_product() {
        let wrapper = BlasWrapper::new();
        let x = vec![1.0, 2.0, 3.0];
        let y = vec![4.0, 5.0, 6.0];
        let result = wrapper.dot(&x, &y).unwrap();
        assert_relative_eq!(result, 32.0); // 1*4 + 2*5 + 3*6 = 32
    }

    #[test]
    fn test_matrix_multiplication() {
        let wrapper = BlasWrapper::new();
        let a = vec![1.0, 2.0, 3.0, 4.0]; // 2x2矩阵
        let b = vec![5.0, 6.0, 7.0, 8.0]; // 2x2矩阵
        let mut c = vec![0.0; 4];         // 结果矩阵

        wrapper.gemm(
            false, // 不转置A
            false, // 不转置B
            2,     // m
            2,     // n
            2,     // k
            1.0,   // alpha
            &a,
            2,     // lda
            &b,
            2,     // ldb
            0.0,   // beta
            &mut c,
            2,     // ldc
        ).unwrap();

        // 验证结果 (按列主序存储)
        assert_relative_eq!(c[0], 23.0); // 1*5 + 3*6 = 23
        assert_relative_eq!(c[1], 34.0); // 2*5 + 4*6 = 34
        assert_relative_eq!(c[2], 31.0); // 1*7 + 3*8 = 31
        assert_relative_eq!(c[3], 46.0); // 2*7 + 4*8 = 46
    }

    #[test]
    fn test_svd() {
        let wrapper = BlasWrapper::new();
        // 创建测试矩阵
        let mut a = vec![1.0, 2.0, 3.0, 4.0]; // 2x2矩阵
        let mut s = vec![0.0; 2];             // 奇异值
        let mut u = vec![0.0; 4];             // 左奇异向量
        let mut vt = vec![0.0; 4];            // 右奇异向量
        let mut work = vec![0.0; 10];         // 工作数组

        wrapper.svd(
            'A', 'A',    // jobu, jobvt
            2, 2,        // m, n
            &mut a, 2,   // a, lda
            &mut s,      // s
            &mut u, 2,   // u, ldu
            &mut vt, 2,  // vt, ldvt
            &mut work, 10 // work, lwork
        ).unwrap();

        // 验证奇异值非负
        assert!(s[0] >= 0.0);
        assert!(s[1] >= 0.0);
        // 验证奇异值降序排列
        assert!(s[0] >= s[1]);
    }
}
