use std::sync::{OnceLock, atomic::{AtomicUsize, Ordering}};
use raw_cpuid::CpuId;

/// CPU特性检测
pub struct CpuFeatures {
    pub vendor: CpuVendor,
    pub has_avx: bool,
    pub has_avx2: bool,
    pub has_avx512: bool,
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq)]
pub enum CpuVendor {
    Intel,
    Amd,
    Other,
}

/// 检测CPU特性
pub fn detect_cpu_features() -> CpuFeatures {
    let cpuid = CpuId::new();
    
    let vendor = if let Some(vendor_info) = cpuid.get_vendor_info() {
        match vendor_info.as_str() {
            "GenuineIntel" => CpuVendor::Intel,
            "AuthenticAMD" => CpuVendor::Amd,
            _ => CpuVendor::Other,
        }
    } else {
        CpuVendor::Other
    };
    
    let feature_info = cpuid.get_feature_info();
    let extended_features = cpuid.get_extended_feature_info();
    
    CpuFeatures {
        vendor,
        has_avx: feature_info.map_or(false, |f| f.has_avx()),
        has_avx2: if let Some(ref ef) = extended_features { ef.has_avx2() } else { false },
        has_avx512: if let Some(ref ef) = extended_features { ef.has_avx512f() } else { false },
    }
}

/// Intel MKL可用性检测
fn intel_mkl_is_available() -> bool {
    #[cfg(feature = "intel-mkl")]
    {
        // 检查MKL环境变量或库文件
        std::env::var("MKLROOT").is_ok() || 
        std::path::Path::new("/usr/lib/x86_64-linux-gnu/libmkl_rt.so").exists() ||
        std::path::Path::new("/opt/intel/mkl/lib/intel64/libmkl_rt.so").exists() ||
        std::path::Path::new("/opt/intel/oneapi/mkl/latest/lib/intel64/libmkl_rt.so").exists()
    }
    #[cfg(not(feature = "intel-mkl"))]
    {
        false
    }
}

/// AMD GPU可用性检测
fn amd_gpu_is_available() -> bool {
    #[cfg(feature = "amd-gpu")]
    {
        // 检查ROCm环境
        std::env::var("ROCM_PATH").is_ok() ||
        std::path::Path::new("/opt/rocm").exists() ||
        std::path::Path::new("/usr/lib/x86_64-linux-gnu/librocblas.so").exists()
    }
    #[cfg(not(feature = "amd-gpu"))]
    {
        false
    }
}



/// BLAS后端类型
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum BlasProvider {
    /// OpenBLAS - 开源BLAS实现
    OpenBlas,
    /// Intel MKL - Intel数学核心库
    IntelMkl,
    /// CUDA BLAS - NVIDIA GPU加速
    
    /// 系统默认BLAS
    SystemBlas,
}

/// BLAS配置
#[derive(Debug)]
pub struct BlasConfig {
    /// 当前使用的BLAS提供者
    provider: AtomicUsize,
    /// 线程数配置
    num_threads: AtomicUsize,
}

impl Default for BlasConfig {
    fn default() -> Self {
        Self {
            provider: AtomicUsize::new(BlasProvider::OpenBlas as usize),
            num_threads: AtomicUsize::new(num_cpus::get()),
        }
    }
}

/// 全局BLAS配置单例
pub struct BlasRuntime {
    config: BlasConfig,
}

impl BlasRuntime {
    /// 获取全局实例
    pub fn global() -> &'static Self {
        static SINGLETON: OnceLock<BlasRuntime> = OnceLock::new();
        
        SINGLETON.get_or_init(|| BlasRuntime {
            config: BlasConfig::default(),
        })
    }

    /// 设置BLAS提供者
    pub fn set_provider(&self, provider: BlasProvider) {
        // 检查提供者是否可用
        match provider {
            BlasProvider::IntelMkl if !intel_mkl_is_available() => {
                // 如果MKL不可用，保持当前提供者不变
                return;
            }
            _ => {}
        }
        
        self.config.provider.store(provider as usize, Ordering::SeqCst);
        
        // 根据不同后端设置环境变量
        match provider {
            BlasProvider::OpenBlas => {
                std::env::set_var("OPENBLAS_NUM_THREADS", 
                    self.config.num_threads.load(Ordering::SeqCst).to_string());
            }
            BlasProvider::IntelMkl => {
                std::env::set_var("MKL_NUM_THREADS",
                    self.config.num_threads.load(Ordering::SeqCst).to_string());
            }

            BlasProvider::SystemBlas => {
                // 系统BLAS配置
            }
        }
    }

    /// 获取当前BLAS提供者
    pub fn current_provider(&self) -> BlasProvider {
        let provider = self.config.provider.load(Ordering::SeqCst);
        match provider {
            0 => BlasProvider::OpenBlas,
            1 => BlasProvider::IntelMkl,
            
            _ => BlasProvider::SystemBlas,
        }
    }

    /// 设置BLAS线程数
    pub fn set_num_threads(&self, num_threads: usize) {
        self.config.num_threads.store(num_threads, Ordering::SeqCst);
        
        // 同步更新当前后端的线程配置
        self.set_provider(self.current_provider());
    }

    /// 获取当前线程数配置
    pub fn get_num_threads(&self) -> usize {
        self.config.num_threads.load(Ordering::SeqCst)
    }

    /// 自动检测最优BLAS后端
    pub fn auto_detect() -> BlasProvider {
        // 1. 检测CPU特性
        #[cfg(target_arch = "x86_64")]
        {
            /// CPU特性结构体
            pub struct CpuFeatures {
                pub has_avx: bool,
                pub has_avx2: bool,
                pub has_avx512: bool,
                pub has_fma: bool,
            }

            /// CPU特性检测
            fn detect_cpu_features() -> CpuFeatures {
                CpuFeatures {
                    has_avx: is_x86_feature_detected!("avx"),
                    has_avx2: is_x86_feature_detected!("avx2"),
                    has_avx512: is_x86_feature_detected!("avx512f"),
                    has_fma: is_x86_feature_detected!("fma"),
                }
            }
            let _cpu_features = detect_cpu_features();
        }
        let _has_avx512 = false; // Placeholder for non-x86_64 or if feature not detected
        


        // 2. 检测GPU可用性
        
        
        // 3. 检测MKL
         let has_mkl = if cfg!(feature = "intel-mkl") { intel_mkl_is_available() } else { false };
        
        // 4. 根据特征选择最优后端
    if has_mkl {
            BlasProvider::IntelMkl
        } else {
            BlasProvider::OpenBlas
        }
    }
}

/// 获取最佳BLAS提供者
pub fn get_optimal_blas_provider() -> BlasProvider {
    static PROVIDER: OnceLock<BlasProvider> = OnceLock::new();
    
    *PROVIDER.get_or_init(|| {
        let cpu_features = detect_cpu_features();
        
        // Intel CPU优先使用Intel MKL
        if cpu_features.vendor == CpuVendor::Intel && intel_mkl_is_available() {
            BlasProvider::IntelMkl
        }
        // AMD CPU检查OpenBLAS
        else if cpu_features.vendor == CpuVendor::Amd {
            BlasProvider::OpenBlas
        }
        // 其他情况使用系统BLAS或OpenBLAS
        else {
            BlasProvider::OpenBlas
        }
    })
}

/// 获取最佳GPU后端
pub fn get_optimal_gpu_backend() -> Option<GpuBackend> {
    static GPU_BACKEND: OnceLock<Option<GpuBackend>> = OnceLock::new();
    
    *GPU_BACKEND.get_or_init(|| {
        // 优先检查AMD GPU支持
        if amd_gpu_is_available() {
            Some(GpuBackend::AmdRocm)
        }
        // 检查OpenCL通用支持
        else {
            #[cfg(feature = "opencl")]
            {
                Some(GpuBackend::OpenCL)
            }
            #[cfg(not(feature = "opencl"))]
            {
                None
            }
        }
    })
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum GpuBackend {
    AmdRocm,
    OpenCL,
    Cuda,
}





#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_blas_runtime_singleton() {
        let runtime1 = BlasRuntime::global();
        let runtime2 = BlasRuntime::global();
        
        // 验证是同一个实例
        assert_eq!(
            runtime1.current_provider() as usize,
            runtime2.current_provider() as usize
        );
    }

    #[test]
    fn test_provider_switching() {
        let runtime = BlasRuntime::global();
        
        // 首先确保是OpenBlas
        runtime.set_provider(BlasProvider::OpenBlas);
        assert_eq!(runtime.current_provider(), BlasProvider::OpenBlas);
        
        // 尝试切换到IntelMkl
        runtime.set_provider(BlasProvider::IntelMkl);
        if mkl_is_available() {
            assert_eq!(runtime.current_provider(), BlasProvider::IntelMkl);
        } else {
            // 如果MKL不可用，应该保持OpenBlas
            assert_eq!(runtime.current_provider(), BlasProvider::OpenBlas);
        }
        
        // 切换回OpenBlas
        runtime.set_provider(BlasProvider::OpenBlas);
        assert_eq!(runtime.current_provider(), BlasProvider::OpenBlas);
    }

    #[test]
    fn test_thread_configuration() {
        let runtime = BlasRuntime::global();
        
        // 设置线程数
        runtime.set_num_threads(4);
        assert_eq!(runtime.get_num_threads(), 4);
        
        // 验证环境变量（如果存在的话）
        if let Ok(threads) = std::env::var("OPENBLAS_NUM_THREADS") {
            assert_eq!(threads, "4");
        }
        // 如果环境变量不存在，测试仍然通过，因为线程数设置已经验证
    }
}
