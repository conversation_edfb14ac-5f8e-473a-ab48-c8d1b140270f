use std::fmt;
use std::error::Error as StdError;

/// LAPACK错误码及其详细描述
#[derive(Debug, Clone, PartialEq)]
pub enum LapackError {
    /// 输入参数非法
    IllegalParameter {
        /// 非法参数的位置
        parameter: i32,
        /// 详细说明
        description: String,
    },
    
    /// 算法收敛失败
    ConvergenceFailure {
        /// 未收敛的特征值数量
        unconverged: i32,
        /// 迭代次数
        iterations: i32,
    },
    
    /// 矩阵奇异
    Singular {
        /// 奇异元素的位置
        position: (i32, i32),
    },
    
    /// 矩阵不正定
    NotPositiveDefinite {
        /// 导致问题的对角线元素位置
        diagonal_position: i32,
    },
    
    /// 内存分配失败
    MemoryAllocation {
        /// 请求的内存大小（字节）
        requested_size: usize,
    },
    
    /// 工作区大小不足
    InsufficientWorkspace {
        /// 需要的工作区大小
        required: i32,
        /// 当前工作区大小
        provided: i32,
    },
}

impl fmt::Display for LapackError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            LapackError::IllegalParameter { parameter, description } => {
                write!(f, "非法参数 #{}: {}", parameter, description)
            }
            LapackError::ConvergenceFailure { unconverged, iterations } => {
                write!(f, "算法未收敛: {} 个特征值在 {} 次迭代后仍未收敛", unconverged, iterations)
            }
            LapackError::Singular { position } => {
                write!(f, "矩阵在位置 ({}, {}) 处奇异", position.0, position.1)
            }
            LapackError::NotPositiveDefinite { diagonal_position } => {
                write!(f, "矩阵不正定: 对角线元素 #{} 不满足条件", diagonal_position)
            }
            LapackError::MemoryAllocation { requested_size } => {
                write!(f, "内存分配失败: 无法分配 {} 字节", requested_size)
            }
            LapackError::InsufficientWorkspace { required, provided } => {
                write!(f, "工作区大小不足: 需要 {} 但只提供了 {}", required, provided)
            }
        }
    }
}

impl StdError for LapackError {}

/// LAPACK错误码解析器
pub struct LapackErrorParser;

impl LapackErrorParser {
    /// 解析LAPACK函数返回的info值
    pub fn parse_info(info: i32, routine: &str, params: &[(&str, i32)]) -> Result<(), LapackError> {
        match info {
            0 => Ok(()),
            n if n < 0 => {
                let param_idx = -n as usize;
                if param_idx <= params.len() {
                    let (param_name, param_value) = params[param_idx - 1];
                    Err(LapackError::IllegalParameter {
                        parameter: -n,
                        description: format!(
                            "参数 {} = {} 在例程 {} 中非法",
                            param_name, param_value, routine
                        ),
                    })
                } else {
                    Err(LapackError::IllegalParameter {
                        parameter: -n,
                        description: format!("未知参数错误在例程 {}", routine),
                    })
                }
            }
            n if n > 0 => {
                match routine {
                    "DGESVD" => Err(LapackError::ConvergenceFailure {
                        unconverged: n,
                        iterations: 30, // LAPACK默认最大迭代次数
                    }),
                    "DGEEV" => Err(LapackError::ConvergenceFailure {
                        unconverged: n,
                        iterations: 30,
                    }),
                    "DPOTRF" => Err(LapackError::NotPositiveDefinite {
                        diagonal_position: n,
                    }),
                    "DGETRF" => Err(LapackError::Singular {
                        position: (n, n),
                    }),
                    _ => Err(LapackError::IllegalParameter {
                        parameter: n,
                        description: format!("未知错误在例程 {}", routine),
                    }),
                }
            }
            _ => unreachable!(),
        }
    }

    /// 验证工作区大小
    pub fn check_workspace(lwork: i32, required: i32) -> Result<(), LapackError> {
        if lwork < required {
            Err(LapackError::InsufficientWorkspace {
                required,
                provided: lwork,
            })
        } else {
            Ok(())
        }
    }

    /// 验证矩阵维度
    pub fn check_dimensions(
        routine: &str,
        dims: &[(&str, i32)],
    ) -> Result<(), LapackError> {
        for &(name, dim) in dims {
            if dim < 0 {
                return Err(LapackError::IllegalParameter {
                    parameter: -1,
                    description: format!(
                        "维度 {} = {} 在例程 {} 中非法",
                        name, dim, routine
                    ),
                });
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_success() {
        let result = LapackErrorParser::parse_info(0, "DGESVD", &[]);
        assert!(result.is_ok());
    }

    #[test]
    fn test_parse_illegal_parameter() {
        let params = [("M", 10), ("N", -1)];
        let result = LapackErrorParser::parse_info(-2, "DGESVD", &params);
        
        match result {
            Err(LapackError::IllegalParameter { parameter, .. }) => {
                assert_eq!(parameter, 2);
            }
            _ => panic!("Expected IllegalParameter error"),
        }
    }

    #[test]
    fn test_parse_convergence_failure() {
        let result = LapackErrorParser::parse_info(5, "DGEEV", &[]);
        
        match result {
            Err(LapackError::ConvergenceFailure { unconverged, .. }) => {
                assert_eq!(unconverged, 5);
            }
            _ => panic!("Expected ConvergenceFailure error"),
        }
    }

    #[test]
    fn test_check_workspace() {
        let result = LapackErrorParser::check_workspace(10, 20);
        
        match result {
            Err(LapackError::InsufficientWorkspace { required, provided }) => {
                assert_eq!(required, 20);
                assert_eq!(provided, 10);
            }
            _ => panic!("Expected InsufficientWorkspace error"),
        }
    }

    #[test]
    fn test_check_dimensions() {
        let dims = [("M", -1), ("N", 10)];
        let result = LapackErrorParser::check_dimensions("DGESVD", &dims);
        
        assert!(matches!(result, Err(LapackError::IllegalParameter { .. })));
    }
}
