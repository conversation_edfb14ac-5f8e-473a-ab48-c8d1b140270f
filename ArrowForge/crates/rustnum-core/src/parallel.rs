//! 并行计算管理模块
use std::sync::Arc;
use parking_lot::RwLock;

#[cfg(feature = "parallel")]
use rayon::ThreadPool;
#[cfg(feature = "parallel")]
use rayon::iter::ParallelBridge;
#[cfg(feature = "parallel")]
use rayon::ThreadPoolBuilder;

use lazy_static::lazy_static;
/// 并行计算配置
#[derive(Debug, Clone)]
pub struct ParallelConfig {
    /// 线程池大小
    pub num_threads: usize,
    /// 是否启用BLAS/LAPACK多线程
    pub enable_blas_threads: bool,
    /// 最小并行块大小
    pub min_parallel_size: usize,
    /// 是否启用自动并行
    pub auto_parallel: bool,
}

impl Default for ParallelConfig {
    fn default() -> Self {
        Self {
            num_threads: num_cpus::get(),
            enable_blas_threads: true,
            min_parallel_size: 1024,
            auto_parallel: true,
        }
    }
}

lazy_static! {
    static ref PARALLEL_CONFIG: RwLock<ParallelConfig> = RwLock::new(ParallelConfig::default());
    #[cfg(feature = "parallel")]
    static ref THREAD_POOL: RwLock<Option<Arc<ThreadPool>>> = RwLock::new(None);
}

/// 并行计算管理器
pub struct ParallelManager;

impl ParallelManager {
    /// 初始化并行计算环境
    pub fn initialize(config: Option<ParallelConfig>) {
        let config = config.unwrap_or_default();
        
        // 配置BLAS/LAPACK线程
        // if config.enable_blas_threads {
        //     #[cfg(feature = "openblas")]
        //     unsafe {
        //         openblas_src::openblas_set_num_threads(config.num_threads as i32);
        //     }
        //     #[cfg(feature = "mkl")]
        //     unsafe {
        //         mkl_sys::MKL_Set_Num_Threads(config.num_threads as i32);
        //     }
        // } else {
        //     #[cfg(feature = "openblas")]
        //     unsafe {
        //         openblas_src::openblas_set_num_threads(1);
        //     }
        //     #[cfg(feature = "mkl")]
        //     unsafe {
        //         mkl_sys::MKL_Set_Num_Threads(1);
        //     }
        // }
        // stub: openblas_set_num_threads 等 FFI 调用已注释
        
        #[cfg(feature = "parallel")]
        {
            // 创建Rayon线程池
            let pool = ThreadPoolBuilder::new()
                .num_threads(config.num_threads)
                .build()
                .unwrap();
            
            *THREAD_POOL.write() = Some(Arc::new(pool));
        }
        *PARALLEL_CONFIG.write() = config;
    }
    
    /// 获取线程池
    #[cfg(feature = "parallel")]
    pub fn get_thread_pool() -> Arc<ThreadPool> {
        if let Some(pool) = THREAD_POOL.read().as_ref() {
            pool.clone()
        } else {
            Self::initialize(None);
            THREAD_POOL.read().as_ref().unwrap().clone()
        }
    }
    
    /// 获取配置
    pub fn get_config() -> ParallelConfig {
        PARALLEL_CONFIG.read().clone()
    }
    
    /// 判断是否应该使用并行计算
    pub fn should_parallelize(size: usize) -> bool {
        let config = PARALLEL_CONFIG.read();
        config.auto_parallel && size >= config.min_parallel_size
    }
    
    /// 在线程池中执行任务
    #[cfg(feature = "parallel")]
    pub fn execute<F, R>(f: F) -> R 
    where
        F: FnOnce() -> R + Send,
        R: Send,
    {
        let pool = Self::get_thread_pool();
        pool.install(f)
    }
    
    /// 在没有并行特性时的串行执行
    #[cfg(not(feature = "parallel"))]
    pub fn execute<F, R>(f: F) -> R 
    where
        F: FnOnce() -> R + Send,
        R: Send,
    {
        f()
    }
    
    /// 并行迭代
    #[cfg(feature = "parallel")]
    pub fn parallel_iter<I>(iter: I) -> rayon::iter::IterBridge<I::IntoIter>
    where
        I: IntoIterator + Send,
        I::Item: Send,
        I::IntoIter: Iterator + Send,
        <I::IntoIter as Iterator>::Item: Send,
    {
        let pool = Self::get_thread_pool();
        pool.install(|| iter.into_iter().par_bridge())
    }
}

/// RAII风格的BLAS线程控制
pub struct BlasThreadGuard {
    prev_num_threads: i32,
}

impl BlasThreadGuard {
    /// 临时设置BLAS线程数
    pub fn new(_num_threads: i32) -> Self {
        #[cfg(feature = "openblas")]
        let prev_num_threads = 1i32;
        
        #[cfg(feature = "mkl")]
        let prev_num_threads = 1i32;
        
        #[cfg(not(any(feature = "openblas", feature = "mkl")))]
        let prev_num_threads = 1i32;
        
        Self { prev_num_threads }
    }
}

impl Drop for BlasThreadGuard {
    fn drop(&mut self) {
        // BLAS线程恢复暂时禁用，避免链接问题
        #[cfg(feature = "openblas")]
        {
            // openblas线程恢复暂时跳过
            ()
        }
        
        #[cfg(feature = "mkl")]
        {
            // mkl线程恢复暂时跳过
            ()
        }
    }
}
