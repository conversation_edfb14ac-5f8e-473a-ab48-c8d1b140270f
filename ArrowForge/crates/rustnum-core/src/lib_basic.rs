//! ArrowSciCompute 核心库 - 基础模块版本
//! 
//! 这是一个只包含基础功能的版本，用于模块隔离测试

// 错误处理模块
pub mod error;

// 数值 trait 模块
pub mod traits;

// 内存管理模块
pub mod memory;

// SIMD 模块
pub mod simd;

// 数组模块 - 只包含基础功能
pub mod array {
    pub mod array_impl;
    pub mod creation;
    pub mod math_ops;
    
    // 重新导出核心类型
    pub use array_impl::RustArray;
    pub use creation::ArrayCreation;
}

// 重新导出主要类型
pub use array::RustArray;
pub use error::RustNumError;
pub use traits::{Numeric, Float, Complex, Integer};
