//! 数值运算 Trait 定义
//! 
//! 本模块定义了支持泛型数值运算的核心 trait，为线性代数算法提供
//! 统一的数值运算接口。支持实数、复数和其他数值类型。

use std::fmt::{Debug, Display};
use std::ops::{Add, Sub, Mul, Div, Neg, AddAssign, SubAssign, MulAssign, DivAssign};

use crate::error::RustNumError;

/// 数值类型的核心 trait
/// 
/// 定义了所有数值计算所需的基本操作，包括：
/// - 基本算术运算（加减乘除）
/// - 数学函数（平方根、绝对值等）
/// - 类型转换和常量
/// - 比较操作
pub trait Numeric: 
    Copy + Clone + Debug + Display + PartialEq + PartialOrd +
    Add<Output = Self> + Sub<Output = Self> + Mul<Output = Self> + Div<Output = Self> + Neg<Output = Self> +
    AddAssign + SubAssign + MulAssign + DivAssign +
    Send + Sync + 'static
{
    /// 零值常量
    fn zero() -> Self;
    
    /// 单位值常量
    fn one() -> Self;
    
    /// 负单位值常量
    fn neg_one() -> Self {
        -Self::one()
    }
    
    /// 检查是否为零
    fn is_zero(&self) -> bool {
        *self == Self::zero()
    }
    
    /// 检查是否为单位值
    fn is_one(&self) -> bool {
        *self == Self::one()
    }
    
    /// 绝对值
    fn abs(&self) -> Self;
    
    /// 平方根
    fn sqrt(&self) -> Self;
    
    /// 平方
    fn square(&self) -> Self {
        *self * *self
    }
    
    /// 倒数
    fn recip(&self) -> Self {
        Self::one() / *self
    }
    
    /// 符号函数
    fn signum(&self) -> Self;
    
    /// 最小值
    fn min(self, other: Self) -> Self;
    
    /// 最大值
    fn max(self, other: Self) -> Self;
    
    /// 从 f64 转换
    fn from_f64(value: f64) -> Option<Self>;
    
    /// 转换为 f64
    fn to_f64(&self) -> Option<f64>;
    
    /// 从 usize 转换
    fn from_usize(value: usize) -> Option<Self>;
    
    /// 转换为 usize
    fn to_usize(&self) -> Option<usize>;
}

/// 浮点数特有的数值 trait
pub trait Float: Numeric {
    /// 无穷大
    fn infinity() -> Self;
    
    /// 负无穷大
    fn neg_infinity() -> Self;
    
    /// NaN (Not a Number)
    fn nan() -> Self;
    
    /// 检查是否为无穷大
    fn is_infinite(&self) -> bool;
    
    /// 检查是否为 NaN
    fn is_nan(&self) -> bool;
    
    /// 检查是否为有限数
    fn is_finite(&self) -> bool;
    
    /// 检查是否为正常数（非零、非无穷、非NaN）
    fn is_normal(&self) -> bool;
    
    /// 向上取整
    fn ceil(&self) -> Self;
    
    /// 向下取整
    fn floor(&self) -> Self;
    
    /// 四舍五入
    fn round(&self) -> Self;
    
    /// 截断小数部分
    fn trunc(&self) -> Self;
    
    /// 小数部分
    fn fract(&self) -> Self;
    
    /// 指数函数
    fn exp(&self) -> Self;
    
    /// 自然对数
    fn ln(&self) -> Self;
    
    /// 以2为底的对数
    fn log2(&self) -> Self;
    
    /// 以10为底的对数
    fn log10(&self) -> Self;
    
    /// 幂运算
    fn powf(&self, exp: Self) -> Self;
    
    /// 正弦函数
    fn sin(&self) -> Self;
    
    /// 余弦函数
    fn cos(&self) -> Self;
    
    /// 正切函数
    fn tan(&self) -> Self;
    
    /// 反正弦函数
    fn asin(&self) -> Self;
    
    /// 反余弦函数
    fn acos(&self) -> Self;
    
    /// 反正切函数
    fn atan(&self) -> Self;
    
    /// 双参数反正切函数
    fn atan2(&self, other: Self) -> Self;
    
    /// 双曲正弦函数
    fn sinh(&self) -> Self;
    
    /// 双曲余弦函数
    fn cosh(&self) -> Self;
    
    /// 双曲正切函数
    fn tanh(&self) -> Self;
}

/// 复数特有的数值 trait
pub trait Complex: Numeric {
    /// 实部类型
    type Real: Float;
    
    /// 创建复数
    fn new(real: Self::Real, imag: Self::Real) -> Self;
    
    /// 获取实部
    fn real(&self) -> Self::Real;
    
    /// 获取虚部
    fn imag(&self) -> Self::Real;
    
    /// 复共轭
    fn conj(&self) -> Self;
    
    /// 模长
    fn norm(&self) -> Self::Real {
        (self.real().square() + self.imag().square()).sqrt()
    }
    
    /// 模长的平方
    fn norm_sqr(&self) -> Self::Real {
        self.real().square() + self.imag().square()
    }
    
    /// 幅角
    fn arg(&self) -> Self::Real {
        self.imag().atan2(self.real())
    }
    
    /// 从极坐标创建
    fn from_polar(r: Self::Real, theta: Self::Real) -> Self {
        Self::new(r * theta.cos(), r * theta.sin())
    }
}

/// 整数特有的数值 trait
pub trait Integer: Numeric {
    /// 检查是否为偶数
    fn is_even(&self) -> bool;
    
    /// 检查是否为奇数
    fn is_odd(&self) -> bool {
        !self.is_even()
    }
    
    /// 整数除法
    fn div_floor(&self, other: &Self) -> Self;
    
    /// 取模运算
    fn mod_floor(&self, other: &Self) -> Self;
    
    /// 最大公约数
    fn gcd(&self, other: &Self) -> Self;
    
    /// 最小公倍数
    fn lcm(&self, other: &Self) -> Self {
        *self / self.gcd(other) * *other
    }
}

// 为 f32 实现 Numeric trait
impl Numeric for f32 {
    fn zero() -> Self { 0.0 }
    fn one() -> Self { 1.0 }
    
    fn abs(&self) -> Self { (*self).abs() }
    fn sqrt(&self) -> Self { (*self).sqrt() }
    fn signum(&self) -> Self { (*self).signum() }
    fn min(self, other: Self) -> Self {
        if self <= other { self } else { other }
    }
    fn max(self, other: Self) -> Self {
        if self >= other { self } else { other }
    }
    
    fn from_f64(value: f64) -> Option<Self> {
        if value.is_finite() && value >= f32::MIN as f64 && value <= f32::MAX as f64 {
            Some(value as f32)
        } else {
            None
        }
    }
    
    fn to_f64(&self) -> Option<f64> { Some(*self as f64) }
    
    fn from_usize(value: usize) -> Option<Self> {
        if value <= (1u64 << f32::MANTISSA_DIGITS) as usize {
            Some(value as f32)
        } else {
            None
        }
    }
    
    fn to_usize(&self) -> Option<usize> {
        if self.is_finite() && *self >= 0.0 && *self <= usize::MAX as f32 {
            Some(*self as usize)
        } else {
            None
        }
    }
}

// 为 f64 实现 Numeric trait
impl Numeric for f64 {
    fn zero() -> Self { 0.0 }
    fn one() -> Self { 1.0 }
    
    fn abs(&self) -> Self { (*self).abs() }
    fn sqrt(&self) -> Self { (*self).sqrt() }
    fn signum(&self) -> Self { (*self).signum() }
    fn min(self, other: Self) -> Self {
        if self <= other { self } else { other }
    }
    fn max(self, other: Self) -> Self {
        if self >= other { self } else { other }
    }
    
    fn from_f64(value: f64) -> Option<Self> { Some(value) }
    fn to_f64(&self) -> Option<f64> { Some(*self) }
    
    fn from_usize(value: usize) -> Option<Self> {
        if value <= (1u64 << f64::MANTISSA_DIGITS) as usize {
            Some(value as f64)
        } else {
            None
        }
    }
    
    fn to_usize(&self) -> Option<usize> {
        if self.is_finite() && *self >= 0.0 && *self <= usize::MAX as f64 {
            Some(*self as usize)
        } else {
            None
        }
    }
}

// 为 f32 实现 Float trait
impl Float for f32 {
    fn infinity() -> Self { f32::INFINITY }
    fn neg_infinity() -> Self { f32::NEG_INFINITY }
    fn nan() -> Self { f32::NAN }

    fn is_infinite(&self) -> bool { (*self).is_infinite() }
    fn is_nan(&self) -> bool { (*self).is_nan() }
    fn is_finite(&self) -> bool { (*self).is_finite() }
    fn is_normal(&self) -> bool { (*self).is_normal() }

    fn ceil(&self) -> Self { (*self).ceil() }
    fn floor(&self) -> Self { (*self).floor() }
    fn round(&self) -> Self { (*self).round() }
    fn trunc(&self) -> Self { (*self).trunc() }
    fn fract(&self) -> Self { (*self).fract() }

    fn exp(&self) -> Self { (*self).exp() }
    fn ln(&self) -> Self { (*self).ln() }
    fn log2(&self) -> Self { (*self).log2() }
    fn log10(&self) -> Self { (*self).log10() }
    fn powf(&self, exp: Self) -> Self { (*self).powf(exp) }

    fn sin(&self) -> Self { (*self).sin() }
    fn cos(&self) -> Self { (*self).cos() }
    fn tan(&self) -> Self { (*self).tan() }
    fn asin(&self) -> Self { (*self).asin() }
    fn acos(&self) -> Self { (*self).acos() }
    fn atan(&self) -> Self { (*self).atan() }
    fn atan2(&self, other: Self) -> Self { (*self).atan2(other) }

    fn sinh(&self) -> Self { (*self).sinh() }
    fn cosh(&self) -> Self { (*self).cosh() }
    fn tanh(&self) -> Self { (*self).tanh() }
}

// 为 f64 实现 Float trait
impl Float for f64 {
    fn infinity() -> Self { f64::INFINITY }
    fn neg_infinity() -> Self { f64::NEG_INFINITY }
    fn nan() -> Self { f64::NAN }

    fn is_infinite(&self) -> bool { (*self).is_infinite() }
    fn is_nan(&self) -> bool { (*self).is_nan() }
    fn is_finite(&self) -> bool { (*self).is_finite() }
    fn is_normal(&self) -> bool { (*self).is_normal() }

    fn ceil(&self) -> Self { (*self).ceil() }
    fn floor(&self) -> Self { (*self).floor() }
    fn round(&self) -> Self { (*self).round() }
    fn trunc(&self) -> Self { (*self).trunc() }
    fn fract(&self) -> Self { (*self).fract() }

    fn exp(&self) -> Self { (*self).exp() }
    fn ln(&self) -> Self { (*self).ln() }
    fn log2(&self) -> Self { (*self).log2() }
    fn log10(&self) -> Self { (*self).log10() }
    fn powf(&self, exp: Self) -> Self { (*self).powf(exp) }

    fn sin(&self) -> Self { (*self).sin() }
    fn cos(&self) -> Self { (*self).cos() }
    fn tan(&self) -> Self { (*self).tan() }
    fn asin(&self) -> Self { (*self).asin() }
    fn acos(&self) -> Self { (*self).acos() }
    fn atan(&self) -> Self { (*self).atan() }
    fn atan2(&self, other: Self) -> Self { (*self).atan2(other) }

    fn sinh(&self) -> Self { (*self).sinh() }
    fn cosh(&self) -> Self { (*self).cosh() }
    fn tanh(&self) -> Self { (*self).tanh() }
}

// 为 i32 实现 Numeric trait
impl Numeric for i32 {
    fn zero() -> Self { 0 }
    fn one() -> Self { 1 }

    fn abs(&self) -> Self { (*self).abs() }
    fn sqrt(&self) -> Self { (*self as f64).sqrt() as i32 }
    fn signum(&self) -> Self { (*self).signum() }
    fn min(self, other: Self) -> Self {
        if self <= other { self } else { other }
    }
    fn max(self, other: Self) -> Self {
        if self >= other { self } else { other }
    }

    fn from_f64(value: f64) -> Option<Self> {
        if value.is_finite() && value >= i32::MIN as f64 && value <= i32::MAX as f64 {
            Some(value as i32)
        } else {
            None
        }
    }

    fn to_f64(&self) -> Option<f64> { Some(*self as f64) }

    fn from_usize(value: usize) -> Option<Self> {
        if value <= i32::MAX as usize {
            Some(value as i32)
        } else {
            None
        }
    }

    fn to_usize(&self) -> Option<usize> {
        if *self >= 0 {
            Some(*self as usize)
        } else {
            None
        }
    }
}

// 为 i64 实现 Numeric trait
impl Numeric for i64 {
    fn zero() -> Self { 0 }
    fn one() -> Self { 1 }

    fn abs(&self) -> Self { (*self).abs() }
    fn sqrt(&self) -> Self { (*self as f64).sqrt() as i64 }
    fn signum(&self) -> Self { (*self).signum() }
    fn min(self, other: Self) -> Self {
        if self <= other { self } else { other }
    }
    fn max(self, other: Self) -> Self {
        if self >= other { self } else { other }
    }

    fn from_f64(value: f64) -> Option<Self> {
        if value.is_finite() && value >= i64::MIN as f64 && value <= i64::MAX as f64 {
            Some(value as i64)
        } else {
            None
        }
    }

    fn to_f64(&self) -> Option<f64> { Some(*self as f64) }

    fn from_usize(value: usize) -> Option<Self> {
        if value <= i64::MAX as usize {
            Some(value as i64)
        } else {
            None
        }
    }

    fn to_usize(&self) -> Option<usize> {
        if *self >= 0 {
            Some(*self as usize)
        } else {
            None
        }
    }
}

// 为 i32 实现 Integer trait
impl Integer for i32 {
    fn is_even(&self) -> bool { self % 2 == 0 }

    fn div_floor(&self, other: &Self) -> Self {
        let div = self / other;
        if (self % other != 0) && ((self < &0) != (other < &0)) {
            div - 1
        } else {
            div
        }
    }

    fn mod_floor(&self, other: &Self) -> Self {
        self - self.div_floor(other) * other
    }

    fn gcd(&self, other: &Self) -> Self {
        let mut a = self.abs();
        let mut b = other.abs();
        while b != 0 {
            let temp = b;
            b = a % b;
            a = temp;
        }
        a
    }
}

// 为 i64 实现 Integer trait
impl Integer for i64 {
    fn is_even(&self) -> bool { self % 2 == 0 }

    fn div_floor(&self, other: &Self) -> Self {
        let div = self / other;
        if (self % other != 0) && ((self < &0) != (other < &0)) {
            div - 1
        } else {
            div
        }
    }

    fn mod_floor(&self, other: &Self) -> Self {
        self - self.div_floor(other) * other
    }

    fn gcd(&self, other: &Self) -> Self {
        let mut a = self.abs();
        let mut b = other.abs();
        while b != 0 {
            let temp = b;
            b = a % b;
            a = temp;
        }
        a
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_numeric_f64() {
        assert_eq!(f64::zero(), 0.0);
        assert_eq!(f64::one(), 1.0);
        assert_eq!((-3.0f64).abs(), 3.0);
        assert_eq!(9.0f64.sqrt(), 3.0);
        assert_eq!((-1.0f64).signum(), -1.0);
        assert_eq!(f64::from_usize(42), Some(42.0));
        assert_eq!(42.0f64.to_usize(), Some(42));
    }

    #[test]
    fn test_float_f64() {
        assert!(f64::infinity().is_infinite());
        assert!(f64::nan().is_nan());
        assert!(42.0f64.is_finite());
        assert_eq!(2.7f64.floor(), 2.0);
        assert_eq!(2.3f64.ceil(), 3.0);
        assert!((1.0f64.exp() - std::f64::consts::E).abs() < 1e-10);
    }

    #[test]
    fn test_integer_i32() {
        assert_eq!(i32::zero(), 0);
        assert_eq!(i32::one(), 1);
        assert!(4i32.is_even());
        assert!(5i32.is_odd());
        assert_eq!(12i32.gcd(&18i32), 6);
        assert_eq!(12i32.lcm(&18i32), 36);
    }
}
