//! 数值计算 Trait 模块
//! 
//! 本模块定义了 ArrowSciCompute 中所有数值计算相关的 trait，
//! 为泛型数值运算提供统一的接口。

pub mod numerical;

// 重新导出主要的 trait
pub use numerical::{Numeric, Float, Complex, Integer};

/// 数值计算相关的常用类型别名
pub type DefaultFloat = f64;
pub type DefaultInt = i64;

/// 检查类型是否支持数值运算
pub trait NumericType {
    /// 检查类型是否为浮点数
    fn is_float() -> bool { false }
    
    /// 检查类型是否为整数
    fn is_integer() -> bool { false }
    
    /// 检查类型是否为复数
    fn is_complex() -> bool { false }
    
    /// 获取类型名称
    fn type_name() -> &'static str;
}

impl NumericType for f32 {
    fn is_float() -> bool { true }
    fn type_name() -> &'static str { "f32" }
}

impl NumericType for f64 {
    fn is_float() -> bool { true }
    fn type_name() -> &'static str { "f64" }
}

impl NumericType for i32 {
    fn is_integer() -> bool { true }
    fn type_name() -> &'static str { "i32" }
}

impl NumericType for i64 {
    fn is_integer() -> bool { true }
    fn type_name() -> &'static str { "i64" }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_numeric_type_detection() {
        assert!(f64::is_float());
        assert!(!f64::is_integer());
        assert!(i32::is_integer());
        assert!(!i32::is_float());
        assert_eq!(f64::type_name(), "f64");
        assert_eq!(i32::type_name(), "i32");
    }
}
