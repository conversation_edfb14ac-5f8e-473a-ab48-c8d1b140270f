use crate::array::SimpleArray;
use crate::error::RustNumError;
use crate::traits::Numeric;
use arrow::array::{PrimitiveArray, Array};
use arrow::datatypes::ArrowPrimitiveType;
use std::marker::PhantomData;
use std::ops::{Deref, DerefMut};

/// 零拷贝只读视图，直接引用 Arrow 数组的内存
pub struct ZeroCopyView<'a, T> {
    data: &'a [T],
    shape: Vec<usize>,
    strides: Vec<usize>,
    _phantom: PhantomData<T>,
}

/// 零拷贝可变视图，直接引用 Arrow 数组的内存
pub struct ZeroCopyMut<'a, T> {
    data: &'a mut [T],
    shape: Vec<usize>,
    strides: Vec<usize>,
    _phantom: PhantomData<T>,
}

impl<'a, T: Numeric> ZeroCopyView<'a, T> {
    /// 从 Arrow 数组创建零拷贝视图
    pub fn from_arrow<P>(array: &'a PrimitiveArray<P>) -> Self
    where
        P: ArrowPrimitiveType,
        P::Native: Into<T> + Copy,
        T: From<P::Native>,
    {
        let values = array.values();
        let data = unsafe {
            // 安全性：我们确保 T 和 P::Native 具有相同的内存布局
            std::slice::from_raw_parts(
                values.as_ptr() as *const T,
                values.len(),
            )
        };
        
        Self {
            data,
            shape: vec![array.len()],
            strides: vec![1],
            _phantom: PhantomData,
        }
    }
    
    /// 创建矩阵视图
    pub fn as_matrix(&self, rows: usize, cols: usize) -> Result<ZeroCopyView<'a, T>, RustNumError> {
        if rows * cols != self.data.len() {
            return Err(RustNumError::ShapeError(format!(
                "Cannot reshape array of length {} to {}x{}", 
                self.data.len(), rows, cols
            )));
        }
        
        Ok(ZeroCopyView {
            data: self.data,
            shape: vec![rows, cols],
            strides: vec![cols, 1],
            _phantom: PhantomData,
        })
    }
    
    /// 获取形状
    pub fn shape(&self) -> &[usize] {
        &self.shape
    }
    
    /// 获取元素数量
    pub fn len(&self) -> usize {
        self.data.len()
    }
    
    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.data.is_empty()
    }
    
    /// 获取元素（零拷贝）
    pub fn get(&self, indices: &[usize]) -> Result<T, RustNumError> {
        let flat_index = self.multi_index_to_flat(indices)?;
        Ok(self.data[flat_index])
    }
    
    /// 多维索引转换为一维索引
    fn multi_index_to_flat(&self, indices: &[usize]) -> Result<usize, RustNumError> {
        if indices.len() != self.shape.len() {
            return Err(RustNumError::IndexError(format!(
                "Index dimension {} doesn't match array dimension {}",
                indices.len(), self.shape.len()
            )));
        }

        let mut flat_index = 0;
        for (i, &idx) in indices.iter().enumerate() {
            if idx >= self.shape[i] {
                return Err(RustNumError::IndexError(format!(
                    "Index {} is out of bounds for axis {} with size {}",
                    idx, i, self.shape[i]
                )));
            }
            flat_index += idx * self.strides[i];
        }
        Ok(flat_index)
    }
    
    /// 矩阵乘法（零拷贝输入，新分配输出）
    pub fn matmul(&self, other: &ZeroCopyView<T>) -> Result<SimpleArray<T>, RustNumError> {
        if self.shape.len() != 2 || other.shape.len() != 2 {
            return Err(RustNumError::ShapeError(
                "Matrix multiplication requires 2D arrays".to_string()
            ));
        }

        let (m, k) = (self.shape[0], self.shape[1]);
        let (k2, n) = (other.shape[0], other.shape[1]);

        if k != k2 {
            return Err(RustNumError::ShapeError(format!(
                "Cannot multiply matrices with shapes {:?} and {:?}", 
                self.shape, other.shape
            )));
        }

        let mut result_data = vec![T::zero(); m * n];

        for i in 0..m {
            for j in 0..n {
                let mut sum = T::zero();
                for l in 0..k {
                    let a_val = self.data[i * k + l];
                    let b_val = other.data[l * n + j];
                    sum = sum + a_val * b_val;
                }
                result_data[i * n + j] = sum;
            }
        }

        SimpleArray::new(result_data, vec![m, n])
    }
    
    /// 转换为 SimpleArray（会复制数据）
    pub fn to_simple_array(&self) -> SimpleArray<T> {
        SimpleArray::new(self.data.to_vec(), self.shape.clone())
            .expect("Valid shape from zero-copy view")
    }
    
    /// 求和（零拷贝计算）
    pub fn sum(&self) -> T {
        self.data.iter().fold(T::zero(), |acc, &x| acc + x)
    }
    
    /// 求平均值（零拷贝计算）
    pub fn mean(&self) -> T {
        if self.data.is_empty() {
            return T::zero();
        }
        let sum = self.sum();
        let len = T::from_usize(self.data.len()).unwrap_or(T::one());
        sum / len
    }
}

impl<'a, T: Numeric> ZeroCopyMut<'a, T> {
    /// 从可变 Arrow 数组创建零拷贝可变视图
    pub fn from_slice(data: &'a mut [T], shape: Vec<usize>) -> Result<Self, RustNumError> {
        let total_elements: usize = shape.iter().product();
        if data.len() != total_elements {
            return Err(RustNumError::ShapeError(format!(
                "Data length {} doesn't match shape {:?} (expected {})",
                data.len(), shape, total_elements
            )));
        }

        let strides = Self::compute_strides(&shape);
        Ok(ZeroCopyMut {
            data,
            shape,
            strides,
            _phantom: PhantomData,
        })
    }
    
    /// 计算步长
    fn compute_strides(shape: &[usize]) -> Vec<usize> {
        let mut strides = vec![1; shape.len()];
        for i in (0..shape.len().saturating_sub(1)).rev() {
            strides[i] = strides[i + 1] * shape[i + 1];
        }
        strides
    }
    
    /// 设置元素（零拷贝）
    pub fn set(&mut self, indices: &[usize], value: T) -> Result<(), RustNumError> {
        let flat_index = self.multi_index_to_flat(indices)?;
        self.data[flat_index] = value;
        Ok(())
    }
    
    /// 多维索引转换为一维索引
    fn multi_index_to_flat(&self, indices: &[usize]) -> Result<usize, RustNumError> {
        if indices.len() != self.shape.len() {
            return Err(RustNumError::IndexError(format!(
                "Index dimension {} doesn't match array dimension {}",
                indices.len(), self.shape.len()
            )));
        }

        let mut flat_index = 0;
        for (i, &idx) in indices.iter().enumerate() {
            if idx >= self.shape[i] {
                return Err(RustNumError::IndexError(format!(
                    "Index {} is out of bounds for axis {} with size {}",
                    idx, i, self.shape[i]
                )));
            }
            flat_index += idx * self.strides[i];
        }
        Ok(flat_index)
    }
    
    /// 标量乘法（就地修改，零拷贝）
    pub fn scalar_mul_inplace(&mut self, scalar: T) {
        for item in self.data.iter_mut() {
            *item = *item * scalar;
        }
    }
    
    /// 填充值（就地修改，零拷贝）
    pub fn fill(&mut self, value: T) {
        for item in self.data.iter_mut() {
            *item = value;
        }
    }
}

// 实现 Deref trait 以便于访问底层数据
impl<'a, T> Deref for ZeroCopyView<'a, T> {
    type Target = [T];
    
    fn deref(&self) -> &Self::Target {
        self.data
    }
}

impl<'a, T> Deref for ZeroCopyMut<'a, T> {
    type Target = [T];
    
    fn deref(&self) -> &Self::Target {
        self.data
    }
}

impl<'a, T> DerefMut for ZeroCopyMut<'a, T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.data
    }
}
