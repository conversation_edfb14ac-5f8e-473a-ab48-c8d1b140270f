/// Arrow 生态集成模块
/// 
/// 本模块实现了与 Apache Arrow 生态的深度集成，充分利用：
/// 1. Rust 的零成本抽象
/// 2. Arrow 的内存零拷贝特性
/// 3. 列式存储的性能优势

#[cfg(feature = "arrow")]
pub mod array_conversion;

#[cfg(feature = "arrow")]
pub mod zero_copy;

#[cfg(feature = "arrow")]
pub mod compute_integration;

#[cfg(feature = "arrow")]
pub mod memory_layout;

// 重新导出核心类型
#[cfg(feature = "arrow")]
pub use array_conversion::{ArrowArrayExt, SimpleArrayExt};

#[cfg(feature = "arrow")]
pub use zero_copy::{ZeroCopyView, ZeroCopyMut};

#[cfg(feature = "arrow")]
pub use compute_integration::{ArrowCompute, ComputeOps};

#[cfg(feature = "arrow")]
pub use memory_layout::{MemoryLayout, ArrowMemoryView};
