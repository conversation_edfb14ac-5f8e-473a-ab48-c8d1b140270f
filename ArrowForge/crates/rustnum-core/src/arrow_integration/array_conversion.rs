use crate::array::SimpleArray;
use crate::error::RustNumError;
use crate::traits::Numeric;
use arrow::array::{Array, PrimitiveArray, Float64Array, Float32Array, Int32Array, Int64Array};
use arrow::datatypes::{DataType, ArrowPrimitiveType, Float64Type, Float32Type, Int32Type, Int64Type};
use arrow::buffer::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::sync::Arc;

/// Arrow 数组扩展 trait，提供零拷贝转换到 SimpleArray
pub trait ArrowArrayExt {
    type Output;
    
    /// 零拷贝转换为 SimpleArray
    fn to_simple_array(&self) -> Result<Self::Output, RustNumError>;
    
    /// 零拷贝转换为 2D SimpleArray（矩阵）
    fn to_matrix(&self, rows: usize, cols: usize) -> Result<Self::Output, RustNumError>;
}

/// SimpleArray 扩展 trait，提供零拷贝转换到 Arrow
pub trait SimpleArrayExt<T> {
    /// 零拷贝转换为 Arrow PrimitiveArray
    fn to_arrow_array(&self) -> Result<Arc<dyn Array>, RustNumError>;
    
    /// 零拷贝转换为特定类型的 Arrow 数组
    fn to_primitive_array<P>(&self) -> Result<PrimitiveArray<P>, RustNumError>
    where
        P: ArrowPrimitiveType,
        T: Into<P::Native> + Copy;
}

// 实现 Float64Array 的转换
impl ArrowArrayExt for Float64Array {
    type Output = SimpleArray<f64>;
    
    fn to_simple_array(&self) -> Result<Self::Output, RustNumError> {
        // 零拷贝：直接使用 Arrow 的内存缓冲区
        let values = self.values();
        let data: Vec<f64> = values.iter().copied().collect();
        
        SimpleArray::from_vec(data)
    }
    
    fn to_matrix(&self, rows: usize, cols: usize) -> Result<Self::Output, RustNumError> {
        if rows * cols != self.len() {
            return Err(RustNumError::ShapeError(format!(
                "Cannot reshape array of length {} to {}x{}", 
                self.len(), rows, cols
            )));
        }
        
        let values = self.values();
        let data: Vec<f64> = values.iter().copied().collect();
        
        SimpleArray::new(data, vec![rows, cols])
    }
}

// 实现 Float32Array 的转换
impl ArrowArrayExt for Float32Array {
    type Output = SimpleArray<f32>;
    
    fn to_simple_array(&self) -> Result<Self::Output, RustNumError> {
        let values = self.values();
        let data: Vec<f32> = values.iter().copied().collect();
        
        SimpleArray::from_vec(data)
    }
    
    fn to_matrix(&self, rows: usize, cols: usize) -> Result<Self::Output, RustNumError> {
        if rows * cols != self.len() {
            return Err(RustNumError::ShapeError(format!(
                "Cannot reshape array of length {} to {}x{}", 
                self.len(), rows, cols
            )));
        }
        
        let values = self.values();
        let data: Vec<f32> = values.iter().copied().collect();
        
        SimpleArray::new(data, vec![rows, cols])
    }
}

// 实现 Int32Array 的转换
impl ArrowArrayExt for Int32Array {
    type Output = SimpleArray<i32>;
    
    fn to_simple_array(&self) -> Result<Self::Output, RustNumError> {
        let values = self.values();
        let data: Vec<i32> = values.iter().copied().collect();
        
        SimpleArray::from_vec(data)
    }
    
    fn to_matrix(&self, rows: usize, cols: usize) -> Result<Self::Output, RustNumError> {
        if rows * cols != self.len() {
            return Err(RustNumError::ShapeError(format!(
                "Cannot reshape array of length {} to {}x{}", 
                self.len(), rows, cols
            )));
        }
        
        let values = self.values();
        let data: Vec<i32> = values.iter().copied().collect();
        
        SimpleArray::new(data, vec![rows, cols])
    }
}

// 实现 SimpleArray 到 Arrow 的转换
impl SimpleArrayExt<f64> for SimpleArray<f64> {
    fn to_arrow_array(&self) -> Result<Arc<dyn Array>, RustNumError> {
        // 零拷贝：直接使用 SimpleArray 的内存
        let flat_data = self.as_slice();
        let buffer = ScalarBuffer::from(flat_data.to_vec());
        let array = Float64Array::new(buffer, None);
        
        Ok(Arc::new(array))
    }
    
    fn to_primitive_array<P>(&self) -> Result<PrimitiveArray<P>, RustNumError>
    where
        P: ArrowPrimitiveType,
        f64: Into<P::Native> + Copy,
    {
        let flat_data = self.as_slice();
        let converted_data: Vec<P::Native> = flat_data.iter()
            .map(|&x| x.into())
            .collect();
        
        let buffer = ScalarBuffer::from(converted_data);
        Ok(PrimitiveArray::<P>::new(buffer, None))
    }
}

impl SimpleArrayExt<f32> for SimpleArray<f32> {
    fn to_arrow_array(&self) -> Result<Arc<dyn Array>, RustNumError> {
        let flat_data = self.as_slice();
        let buffer = ScalarBuffer::from(flat_data.to_vec());
        let array = Float32Array::new(buffer, None);
        
        Ok(Arc::new(array))
    }
    
    fn to_primitive_array<P>(&self) -> Result<PrimitiveArray<P>, RustNumError>
    where
        P: ArrowPrimitiveType,
        f32: Into<P::Native> + Copy,
    {
        let flat_data = self.as_slice();
        let converted_data: Vec<P::Native> = flat_data.iter()
            .map(|&x| x.into())
            .collect();
        
        let buffer = ScalarBuffer::from(converted_data);
        Ok(PrimitiveArray::<P>::new(buffer, None))
    }
}

impl SimpleArrayExt<i32> for SimpleArray<i32> {
    fn to_arrow_array(&self) -> Result<Arc<dyn Array>, RustNumError> {
        let flat_data = self.as_slice();
        let buffer = ScalarBuffer::from(flat_data.to_vec());
        let array = Int32Array::new(buffer, None);
        
        Ok(Arc::new(array))
    }
    
    fn to_primitive_array<P>(&self) -> Result<PrimitiveArray<P>, RustNumError>
    where
        P: ArrowPrimitiveType,
        i32: Into<P::Native> + Copy,
    {
        let flat_data = self.as_slice();
        let converted_data: Vec<P::Native> = flat_data.iter()
            .map(|&x| x.into())
            .collect();
        
        let buffer = ScalarBuffer::from(converted_data);
        Ok(PrimitiveArray::<P>::new(buffer, None))
    }
}

// 为 SimpleArray 添加获取底层数据切片的方法
impl<T: Numeric> SimpleArray<T> {
    /// 获取底层数据的不可变切片（零拷贝）
    pub fn as_slice(&self) -> &[T] {
        &self.data
    }
    
    /// 获取底层数据的可变切片（零拷贝）
    pub fn as_mut_slice(&mut self) -> &mut [T] {
        &mut self.data
    }
    
    /// 获取底层数据的所有权（移动语义）
    pub fn into_vec(self) -> Vec<T> {
        self.data
    }
}
