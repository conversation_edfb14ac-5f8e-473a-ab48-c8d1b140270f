use crate::array::SimpleArray;
use crate::error::RustNumError;
use crate::traits::Numeric;
use arrow::array::{Array, PrimitiveArray};
use arrow::buffer::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};
use arrow::datatypes::ArrowPrimitiveType;
use std::marker::PhantomData;

/// 内存布局优化 trait
pub trait MemoryLayout<T> {
    /// 获取内存对齐信息
    fn memory_alignment(&self) -> usize;
    
    /// 检查是否为连续内存布局
    fn is_contiguous(&self) -> bool;
    
    /// 获取内存使用统计
    fn memory_usage(&self) -> MemoryUsage;
    
    /// 优化内存布局
    fn optimize_layout(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

/// Arrow 内存视图，提供零拷贝访问 Arrow 数组的内存
pub struct ArrowMemoryView<'a, T, P>
where
    P: ArrowPrimitiveType,
{
    array: &'a PrimitiveArray<P>,
    _phantom: PhantomData<T>,
}

/// 内存使用统计
#[derive(Debug, Clone)]
pub struct MemoryUsage {
    /// 总字节数
    pub total_bytes: usize,
    /// 数据字节数
    pub data_bytes: usize,
    /// 元数据字节数
    pub metadata_bytes: usize,
    /// 内存对齐
    pub alignment: usize,
    /// 是否连续
    pub is_contiguous: bool,
}

impl<'a, T, P> ArrowMemoryView<'a, T, P>
where
    P: ArrowPrimitiveType,
    T: Numeric + From<P::Native>,
    P::Native: Copy,
{
    /// 创建新的 Arrow 内存视图
    pub fn new(array: &'a PrimitiveArray<P>) -> Self {
        Self {
            array,
            _phantom: PhantomData,
        }
    }
    
    /// 获取底层缓冲区
    pub fn buffer(&self) -> &ScalarBuffer<P::Native> {
        self.array.values()
    }
    
    /// 获取原始指针（零拷贝）
    pub fn as_ptr(&self) -> *const P::Native {
        self.array.values().as_ptr()
    }
    
    /// 获取数据切片（零拷贝）
    pub fn as_slice(&self) -> &[P::Native] {
        self.array.values()
    }
    
    /// 检查内存是否对齐
    pub fn is_aligned(&self) -> bool {
        let ptr = self.as_ptr() as usize;
        let alignment = std::mem::align_of::<P::Native>();
        ptr % alignment == 0
    }
    
    /// 获取内存布局信息
    pub fn layout_info(&self) -> MemoryLayoutInfo {
        MemoryLayoutInfo {
            element_size: std::mem::size_of::<P::Native>(),
            element_count: self.array.len(),
            alignment: std::mem::align_of::<P::Native>(),
            is_aligned: self.is_aligned(),
            buffer_capacity: self.buffer().len(),
        }
    }
    
    /// 零拷贝转换为 SimpleArray
    pub fn to_simple_array(&self, shape: Vec<usize>) -> Result<SimpleArray<T>, RustNumError> {
        let total_elements: usize = shape.iter().product();
        if total_elements != self.array.len() {
            return Err(RustNumError::ShapeError(format!(
                "Shape {:?} doesn't match array length {}", shape, self.array.len()
            )));
        }
        
        // 零拷贝：直接使用 Arrow 的数据
        let data: Vec<T> = self.as_slice().iter()
            .map(|&x| T::from(x))
            .collect();
        
        SimpleArray::new(data, shape)
    }
}

/// 内存布局信息
#[derive(Debug, Clone)]
pub struct MemoryLayoutInfo {
    /// 元素大小（字节）
    pub element_size: usize,
    /// 元素数量
    pub element_count: usize,
    /// 内存对齐
    pub alignment: usize,
    /// 是否对齐
    pub is_aligned: bool,
    /// 缓冲区容量
    pub buffer_capacity: usize,
}

impl<T: Numeric> MemoryLayout<T> for SimpleArray<T> {
    fn memory_alignment(&self) -> usize {
        std::mem::align_of::<T>()
    }
    
    fn is_contiguous(&self) -> bool {
        // SimpleArray 总是使用连续内存（Vec）
        true
    }
    
    fn memory_usage(&self) -> MemoryUsage {
        let element_size = std::mem::size_of::<T>();
        let data_bytes = self.len() * element_size;
        let metadata_bytes = std::mem::size_of::<Vec<T>>() + 
                           self.shape().len() * std::mem::size_of::<usize>() * 2; // shape + strides
        
        MemoryUsage {
            total_bytes: data_bytes + metadata_bytes,
            data_bytes,
            metadata_bytes,
            alignment: self.memory_alignment(),
            is_contiguous: true,
        }
    }
    
    fn optimize_layout(&self) -> Result<Self, RustNumError> {
        // SimpleArray 已经是优化的布局，直接返回克隆
        Ok(self.clone())
    }
}

/// 内存优化工具
pub struct MemoryOptimizer;

impl MemoryOptimizer {
    /// 检查两个数组是否可以进行零拷贝操作
    pub fn can_zero_copy<T>(left: &SimpleArray<T>, right: &SimpleArray<T>) -> bool {
        // 检查内存布局兼容性
        left.is_contiguous() && 
        right.is_contiguous() && 
        left.memory_alignment() == right.memory_alignment()
    }
    
    /// 估算操作的内存开销
    pub fn estimate_memory_cost<T: Numeric>(
        operation: &str,
        arrays: &[&SimpleArray<T>]
    ) -> MemoryUsage {
        let total_elements: usize = arrays.iter().map(|arr| arr.len()).sum();
        let element_size = std::mem::size_of::<T>();
        
        let data_bytes = match operation {
            "add" | "sub" | "mul" | "div" => total_elements * element_size, // 结果数组
            "matmul" => {
                if arrays.len() >= 2 && arrays[0].shape().len() == 2 && arrays[1].shape().len() == 2 {
                    let m = arrays[0].shape()[0];
                    let n = arrays[1].shape()[1];
                    m * n * element_size
                } else {
                    0
                }
            },
            "transpose" => arrays.get(0).map_or(0, |arr| arr.len() * element_size),
            _ => 0,
        };
        
        MemoryUsage {
            total_bytes: data_bytes,
            data_bytes,
            metadata_bytes: 0,
            alignment: std::mem::align_of::<T>(),
            is_contiguous: true,
        }
    }
    
    /// 建议最优的计算策略
    pub fn suggest_strategy<T: Numeric>(
        operation: &str,
        arrays: &[&SimpleArray<T>]
    ) -> ComputeStrategy {
        let memory_cost = Self::estimate_memory_cost(operation, arrays);
        let total_elements: usize = arrays.iter().map(|arr| arr.len()).sum();
        
        if total_elements < 1000 {
            ComputeStrategy::InPlace
        } else if memory_cost.data_bytes < 1024 * 1024 { // < 1MB
            ComputeStrategy::ZeroCopy
        } else {
            ComputeStrategy::Chunked { chunk_size: 10000 }
        }
    }
}

/// 计算策略
#[derive(Debug, Clone)]
pub enum ComputeStrategy {
    /// 就地计算
    InPlace,
    /// 零拷贝计算
    ZeroCopy,
    /// 分块计算
    Chunked { chunk_size: usize },
}

impl MemoryUsage {
    /// 格式化内存使用报告
    pub fn format_report(&self) -> String {
        format!(
            "Memory Usage Report:\n\
             - Total: {} bytes ({:.2} MB)\n\
             - Data: {} bytes ({:.2} MB)\n\
             - Metadata: {} bytes\n\
             - Alignment: {} bytes\n\
             - Contiguous: {}",
            self.total_bytes,
            self.total_bytes as f64 / 1024.0 / 1024.0,
            self.data_bytes,
            self.data_bytes as f64 / 1024.0 / 1024.0,
            self.metadata_bytes,
            self.alignment,
            self.is_contiguous
        )
    }
}
