use crate::array::SimpleArray;
use crate::error::RustNumError;
use crate::traits::Numeric;
use arrow::array::{Array, PrimitiveArray, Float64Array, Float32Array};
use arrow::compute::kernels::numeric::{add, subtract, multiply, divide};
use arrow::compute::{sum, min, max};
use arrow::datatypes::{ArrowPrimitiveType, Float64Type, Float32Type};
use std::sync::Arc;

/// Arrow Compute 集成 trait
pub trait ArrowCompute<T> {
    /// 使用 Arrow Compute 进行向量化加法
    fn arrow_add(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 使用 Arrow Compute 进行向量化减法
    fn arrow_sub(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 使用 Arrow Compute 进行向量化乘法
    fn arrow_mul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 使用 Arrow Compute 进行向量化除法
    fn arrow_div(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 使用 Arrow Compute 进行标量乘法
    fn arrow_scalar_mul(&self, scalar: T) -> Result<Self, RustNumError>
    where
        Self: Sized;
    
    /// 使用 Arrow Compute 进行求和
    fn arrow_sum(&self) -> Result<T, RustNumError>;
    
    /// 使用 Arrow Compute 进行求平均值
    fn arrow_mean(&self) -> Result<T, RustNumError>;
    
    /// 使用 Arrow Compute 进行求最大值
    fn arrow_max(&self) -> Result<T, RustNumError>;
    
    /// 使用 Arrow Compute 进行求最小值
    fn arrow_min(&self) -> Result<T, RustNumError>;
}

/// 计算操作 trait，提供高性能的向量化计算
pub trait ComputeOps<T> {
    /// 向量化点积计算
    fn dot_product(&self, other: &Self) -> Result<T, RustNumError>;
    
    /// 向量化范数计算
    fn norm(&self) -> Result<T, RustNumError>;
    
    /// 向量化归一化
    fn normalize(&self) -> Result<Self, RustNumError>
    where
        Self: Sized;
}

// 为 f64 类型的 SimpleArray 实现 Arrow Compute
impl ArrowCompute<f64> for SimpleArray<f64> {
    fn arrow_add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape(), other.shape()
            )));
        }
        
        // 转换为 Arrow 数组
        let left_arrow = self.to_arrow_f64_array()?;
        let right_arrow = other.to_arrow_f64_array()?;
        
        // 使用 Arrow Compute 进行向量化加法
        let result_arrow = add(&left_arrow, &right_arrow)
            .map_err(|e| RustNumError::ComputeError(format!("Arrow compute error: {}", e)))?;
        
        // 转换回 SimpleArray
        let result_f64_array = result_arrow.as_any()
            .downcast_ref::<Float64Array>()
            .ok_or_else(|| RustNumError::ComputeError("Failed to downcast result".to_string()))?;
        
        self.from_arrow_f64_array(result_f64_array)
    }
    
    fn arrow_sub(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape(), other.shape()
            )));
        }
        
        let left_arrow = self.to_arrow_f64_array()?;
        let right_arrow = other.to_arrow_f64_array()?;
        
        let result_arrow = subtract(&left_arrow, &right_arrow)
            .map_err(|e| RustNumError::ComputeError(format!("Arrow compute error: {}", e)))?;
        
        let result_f64_array = result_arrow.as_any()
            .downcast_ref::<Float64Array>()
            .ok_or_else(|| RustNumError::ComputeError("Failed to downcast result".to_string()))?;
        
        self.from_arrow_f64_array(result_f64_array)
    }
    
    fn arrow_mul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape(), other.shape()
            )));
        }
        
        let left_arrow = self.to_arrow_f64_array()?;
        let right_arrow = other.to_arrow_f64_array()?;
        
        let result_arrow = multiply(&left_arrow, &right_arrow)
            .map_err(|e| RustNumError::ComputeError(format!("Arrow compute error: {}", e)))?;
        
        let result_f64_array = result_arrow.as_any()
            .downcast_ref::<Float64Array>()
            .ok_or_else(|| RustNumError::ComputeError("Failed to downcast result".to_string()))?;
        
        self.from_arrow_f64_array(result_f64_array)
    }
    
    fn arrow_div(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape() != other.shape() {
            return Err(RustNumError::ShapeError(format!(
                "Shape mismatch: {:?} vs {:?}", self.shape(), other.shape()
            )));
        }
        
        let left_arrow = self.to_arrow_f64_array()?;
        let right_arrow = other.to_arrow_f64_array()?;
        
        let result_arrow = divide(&left_arrow, &right_arrow)
            .map_err(|e| RustNumError::ComputeError(format!("Arrow compute error: {}", e)))?;
        
        let result_f64_array = result_arrow.as_any()
            .downcast_ref::<Float64Array>()
            .ok_or_else(|| RustNumError::ComputeError("Failed to downcast result".to_string()))?;
        
        self.from_arrow_f64_array(result_f64_array)
    }
    
    fn arrow_scalar_mul(&self, scalar: f64) -> Result<Self, RustNumError> {
        let arrow_array = self.to_arrow_f64_array()?;
        let scalar_array = Float64Array::from(vec![scalar; self.len()]);
        
        let result_arrow = multiply(&arrow_array, &scalar_array)
            .map_err(|e| RustNumError::ComputeError(format!("Arrow compute error: {}", e)))?;
        
        let result_f64_array = result_arrow.as_any()
            .downcast_ref::<Float64Array>()
            .ok_or_else(|| RustNumError::ComputeError("Failed to downcast result".to_string()))?;
        
        self.from_arrow_f64_array(result_f64_array)
    }
    
    fn arrow_sum(&self) -> Result<f64, RustNumError> {
        let arrow_array = self.to_arrow_f64_array()?;
        
        sum(&arrow_array)
            .ok_or_else(|| RustNumError::ComputeError("Sum computation failed".to_string()))
    }
    
    fn arrow_mean(&self) -> Result<f64, RustNumError> {
        let sum = self.arrow_sum()?;
        Ok(sum / self.len() as f64)
    }
    
    fn arrow_max(&self) -> Result<f64, RustNumError> {
        let arrow_array = self.to_arrow_f64_array()?;
        
        max(&arrow_array)
            .ok_or_else(|| RustNumError::ComputeError("Max computation failed".to_string()))
    }
    
    fn arrow_min(&self) -> Result<f64, RustNumError> {
        let arrow_array = self.to_arrow_f64_array()?;
        
        min(&arrow_array)
            .ok_or_else(|| RustNumError::ComputeError("Min computation failed".to_string()))
    }
}

// 为 f64 类型的 SimpleArray 实现 ComputeOps
impl ComputeOps<f64> for SimpleArray<f64> {
    fn dot_product(&self, other: &Self) -> Result<f64, RustNumError> {
        if self.len() != other.len() {
            return Err(RustNumError::ShapeError(format!(
                "Length mismatch for dot product: {} vs {}", self.len(), other.len()
            )));
        }
        
        // 使用 Arrow Compute 进行向量化点积
        let product = self.arrow_mul(other)?;
        product.arrow_sum()
    }
    
    fn norm(&self) -> Result<f64, RustNumError> {
        let squared = self.arrow_mul(self)?;
        let sum_of_squares = squared.arrow_sum()?;
        Ok(sum_of_squares.sqrt())
    }
    
    fn normalize(&self) -> Result<Self, RustNumError> {
        let norm = self.norm()?;
        if norm == 0.0 {
            return Err(RustNumError::ComputeError("Cannot normalize zero vector".to_string()));
        }
        self.arrow_scalar_mul(1.0 / norm)
    }
}

// 辅助方法实现
impl SimpleArray<f64> {
    /// 转换为 Arrow Float64Array
    fn to_arrow_f64_array(&self) -> Result<Float64Array, RustNumError> {
        Ok(Float64Array::from(self.as_slice().to_vec()))
    }
    
    /// 从 Arrow Float64Array 创建 SimpleArray
    fn from_arrow_f64_array(&self, array: &Float64Array) -> Result<SimpleArray<f64>, RustNumError> {
        let data: Vec<f64> = array.values().iter().copied().collect();
        SimpleArray::new(data, self.shape().to_vec())
    }
}
