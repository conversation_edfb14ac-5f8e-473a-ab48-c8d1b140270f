//! 性能基准测试模块
//! 
//! 这个模块包含了各种性能基准测试，用于评估 RustNum 的性能
//! 并与其他科学计算库进行对比。

pub mod faer_comparison;

pub use faer_comparison::*;

/// 运行所有基准测试
pub fn run_all_benchmarks() -> Result<(), crate::error::RustNumError> {
    println!("🚀 开始运行所有基准测试...");
    
    #[cfg(feature = "faer-comparison")]
    {
        println!("\n📊 运行 faer-rs 对比测试...");
        FaerComparison::run_full_benchmark()?;
    }
    
    #[cfg(not(feature = "faer-comparison"))]
    {
        println!("⚠️  faer-comparison 特性未启用，跳过 faer-rs 对比测试");
    }
    
    println!("\n✅ 所有基准测试完成!");
    Ok(())
}