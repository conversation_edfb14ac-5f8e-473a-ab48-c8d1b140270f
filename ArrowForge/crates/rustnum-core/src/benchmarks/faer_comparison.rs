//! faer-rs 性能对比测试模块

use std::time::Instant;
use chrono::{DateTime, Utc};

#[cfg(feature = "faer-comparison")]
use faer::Mat;
use crate::array::array_impl::RustArray;
use crate::array::linalg::LinearAlgebra;
use crate::error::RustNumError;

/// 性能测试结果
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub test_name: String,
    pub matrix_size: usize,
    pub rustnum_time_ms: f64,
    pub faer_time_ms: f64,
    pub speedup_ratio: f64,
    pub accuracy_diff: f64,
    pub timestamp: DateTime<Utc>,
}

impl std::fmt::Display for BenchmarkResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{} ({}x{}): RustNum {:.2}ms, faer {:.2}ms, speedup: {:.2}x, accuracy: {:.2e}",
            self.test_name,
            self.matrix_size,
            self.matrix_size,
            self.rustnum_time_ms,
            self.faer_time_ms,
            self.speedup_ratio,
            self.accuracy_diff
        )
    }
}

/// faer-rs 对比测试主结构
pub struct FaerComparison;

impl FaerComparison {
    /// 运行完整的对比测试套件
    #[cfg(feature = "faer-comparison")]
    pub fn run_full_benchmark() -> Result<Vec<BenchmarkResult>, RustNumError> {
        println!("开始 RustNum vs faer-rs 性能对比测试...");
        
        let mut results = Vec::new();
        let sizes = vec![64, 128, 256, 512];
        
        for &size in &sizes {
            println!("\n测试矩阵大小: {}x{}", size, size);
            
            // 矩阵乘法测试
            if let Ok(result) = Self::benchmark_matrix_multiplication(size) {
                println!("  {}", result);
                results.push(result);
            }
        }
        
        Self::print_summary(&results);
        Ok(results)
    }
    
    /// 矩阵乘法性能对比
    #[cfg(feature = "faer-comparison")]
    pub fn benchmark_matrix_multiplication(size: usize) -> Result<BenchmarkResult, RustNumError> {
        // 生成测试数据
        let data_a: Vec<f64> = (0..size*size).map(|i| (i as f64) * 0.01).collect();
        let data_b: Vec<f64> = (0..size*size).map(|i| (i as f64 + 1.0) * 0.01).collect();
        
        // RustNum 测试
        let rustnum_a = RustArray::from_vec(data_a.clone(), vec![size, size])?;
        let rustnum_b = RustArray::from_vec(data_b.clone(), vec![size, size])?;
        
        let start = Instant::now();
        let _rustnum_result = rustnum_a.matmul(&rustnum_b)?;
        let rustnum_time = start.elapsed().as_secs_f64() * 1000.0;
        
        // faer-rs 测试
        let faer_a = Mat::from_fn(size, size, |i, j| data_a[i * size + j]);
        let faer_b = Mat::from_fn(size, size, |i, j| data_b[i * size + j]);
        
        let start = Instant::now();
        let _faer_result = &faer_a * &faer_b;
        let faer_time = start.elapsed().as_secs_f64() * 1000.0;
        
        // 计算性能比率
        let speedup_ratio = if faer_time > 0.0 {
            rustnum_time / faer_time
        } else {
            1.0
        };
        
        Ok(BenchmarkResult {
            test_name: "Matrix Multiplication".to_string(),
            matrix_size: size,
            rustnum_time_ms: rustnum_time,
            faer_time_ms: faer_time,
            speedup_ratio,
            accuracy_diff: 0.0, // 简化版本不计算精度差异
            timestamp: Utc::now(),
        })
    }
    
    /// 打印测试总结
    fn print_summary(results: &[BenchmarkResult]) {
        if results.is_empty() {
            return;
        }
        
        println!("\n=== 性能对比总结 ===");
        
        let avg_speedup: f64 = results.iter().map(|r| r.speedup_ratio).sum::<f64>() / results.len() as f64;
        
        println!("平均加速比: {:.2}x", avg_speedup);
        
        if avg_speedup > 1.0 {
            println!("🚀 RustNum 平均比 faer-rs 快 {:.1}%", (avg_speedup - 1.0) * 100.0);
        } else {
            println!("📊 faer-rs 平均比 RustNum 快 {:.1}%", (1.0 / avg_speedup - 1.0) * 100.0);
        }
    }
    
    /// 当 faer-comparison 特性未启用时的占位函数
    #[cfg(not(feature = "faer-comparison"))]
    pub fn run_full_benchmark() -> Result<Vec<BenchmarkResult>, RustNumError> {
        println!("faer-comparison 特性未启用，跳过对比测试");
        Ok(vec![])
    }
}