use std::f64::consts::PI;
use crate::error::RustNumError;
use crate::parallel::ParallelManager;

#[cfg(feature = "parallel")]
use rayon::prelude::*;


/// 复数类型定义
#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq)]
pub struct Complex64 {
    pub re: f64,
    pub im: f64,
}

impl Complex64 {
    pub fn new(re: f64, im: f64) -> Self {
        Self { re, im }
    }
    
    pub fn from_polar(r: f64, theta: f64) -> Self {
        Self {
            re: r * theta.cos(),
            im: r * theta.sin(),
        }
    }
}

impl std::ops::Add for Complex64 {
    type Output = Self;
    fn add(self, other: Self) -> Self {
        Self {
            re: self.re + other.re,
            im: self.im + other.im,
        }
    }
}

impl std::ops::Sub for Complex64 {
    type Output = Self;
    fn sub(self, other: Self) -> Self {
        Self {
            re: self.re - other.re,
            im: self.im - other.im,
        }
    }
}

impl std::ops::Mul for Complex64 {
    type Output = Self;
    fn mul(self, other: Self) -> Self {
        Self {
            re: self.re * other.re - self.im * other.im,
            im: self.re * other.im + self.im * other.re,
        }
    }
}

impl std::ops::MulAssign<f64> for Complex64 {
    fn mul_assign(&mut self, scalar: f64) {
        self.re *= scalar;
        self.im *= scalar;
    }
}

/// FFT方向
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum FftDirection {
    Forward,
    Inverse,
}

/// FFT计算包装器
pub struct FftComputer;

impl FftComputer {
    /// 计算1D FFT
    pub fn fft_1d(data: &[Complex64], direction: FftDirection) -> Result<Vec<Complex64>, RustNumError> {
        Self::compute_fft_1d(data, direction)
    }
    
    /// 计算2D FFT
    pub fn fft_2d(data: &[Complex64], direction: FftDirection) -> Result<Vec<Vec<Complex64>>, RustNumError> {
        Self::compute_fft_2d(data, direction)
    }
    
    fn compute_fft_1d(data: &[Complex64], direction: FftDirection) -> Result<Vec<Complex64>, RustNumError> {
        let n = data.len();
        
        // 检查输入长度是2的幂
        if !n.is_power_of_two() {
            return Err(RustNumError::ValueError(
                "FFT要求输入长度为2的幂".to_string()
            ));
        }
        
        if n <= 1 {
            return Ok(data.to_vec());
        }
        
        // 如果数据量足够大，使用并行计算
        if ParallelManager::should_parallelize(n) {
            return parallel_fft_1d(data, direction);
        }
        
        // 蝶形算法重排输入
        let mut output = bit_reverse_copy(data);
        
        // 计算
        let sign = match direction {
            FftDirection::Forward => -1.0,
            FftDirection::Inverse => 1.0,
        };
        
        // 蝶形运算
        let mut size = 2;
        while size <= n {
            let half_size = size / 2;
            let omega_step = sign * 2.0 * PI / size as f64;
            
            for i in (0..n).step_by(size) {

                
                for j in 0..half_size {
                    let even = output[i + j];
                    let odd = output[i + j + half_size];
                    let twiddle = Complex64::from_polar(1.0, omega_step * j as f64);
                    
                    output[i + j] = even + twiddle * odd;
                    output[i + j + half_size] = even - twiddle * odd;
                }
            }
            
            size *= 2;
        }
        
        // 对逆变换进行归一化
        if direction == FftDirection::Inverse {
            let scale = 1.0 / n as f64;
            output.iter_mut().for_each(|x| *x *= scale);
        }
        
        Ok(output)
    }
    
    fn compute_fft_2d(data: &[Complex64], direction: FftDirection) -> Result<Vec<Vec<Complex64>>, RustNumError> {
        let n = (data.len() as f64).sqrt() as usize;
        
        if n * n != data.len() {
            return Err(RustNumError::ValueError(
                "2D FFT要求输入为方阵".to_string()
            ));
        }
        
        // 将1D数组重组为2D数组
        let mut matrix = vec![vec![Complex64::new(0.0, 0.0); n]; n];
        for i in 0..n {
            for j in 0..n {
                matrix[i][j] = data[i * n + j];
            }
        }
        
        // 对每行进行FFT
        for row in &mut matrix {
            let row_fft = Self::compute_fft_1d(row, direction)?;
            *row = row_fft;
        }
        
        // 转置矩阵
        let transposed = transpose(&matrix);
        
        // 对每列（现在是行）进行FFT
        let mut result = Vec::new();
        for row in transposed {
            let col_fft = Self::compute_fft_1d(&row, direction)?;
            result.push(col_fft);
        }
        
        // 再次转置得到最终结果
        Ok(transpose(&result))
    }
}



/// 反转位序
fn reverse_bits(mut num: usize, bits: usize) -> usize {
    let mut result = 0;
    for _ in 0..bits {
        result = (result << 1) | (num & 1);
        num >>= 1;
    }
    result
}

/// 并行FFT实现
fn parallel_fft_1d(input: &[Complex64], direction: FftDirection) -> Result<Vec<Complex64>, RustNumError> {
    let n = input.len();
    let sign = match direction {
        FftDirection::Forward => -1.0,
        FftDirection::Inverse => 1.0,
    };
    
    // 蝶形算法重排输入
    let mut output = bit_reverse_copy(input);
    
    // 并行计算
    let mut size = 2;
    while size <= n {
        let half_size = size / 2;
        let omega_step = sign * 2.0 * PI / size as f64;

        
        #[cfg(feature = "parallel")]
        ParallelManager::execute(|| {
            output.par_chunks_mut(size).for_each(|chunk| {
                let (left, right) = chunk.split_at_mut(half_size);
                for j in 0..half_size {
                    let twiddle = Complex64::from_polar(1.0, omega_step * j as f64);
                    let even = left[j];
                    let odd = right[j];
                    left[j] = even + twiddle * odd;
                    right[j] = even - twiddle * odd;
                }
            });
        });
        
        #[cfg(not(feature = "parallel"))]
        {
            for chunk in output.chunks_mut(size) {
                let (left, right) = chunk.split_at_mut(half_size);
                for j in 0..half_size {
                    let twiddle = Complex64::from_polar(1.0, omega_step * j as f64);
                    let even = left[j];
                    let odd = right[j];
                    left[j] = even + twiddle * odd;
                    right[j] = even - twiddle * odd;
                }
            }
        }
        
        size *= 2;
    }
    
    // 对逆变换进行归一化
    if direction == FftDirection::Inverse {
        let scale = 1.0 / n as f64;
        let scale = scale; // Capture scale by value
        #[cfg(feature = "parallel")]
        ParallelManager::execute(|| {
            output.par_iter_mut().for_each(|x| *x *= scale);
        });
        
        #[cfg(not(feature = "parallel"))]
        {
            for x in output.iter_mut() {
                *x *= scale;
            }
        }
    }
    
    Ok(output)
}

/// 矩阵转置
fn transpose(matrix: &[Vec<Complex64>]) -> Vec<Vec<Complex64>> {
    if matrix.is_empty() {
        return Vec::new();
    }
    
    let rows = matrix.len();
    let cols = matrix[0].len();
    let mut result = vec![vec![Complex64::new(0.0, 0.0); rows]; cols];
    
    for i in 0..rows {
        for j in 0..cols {
            result[j][i] = matrix[i][j];
        }
    }
    
    result
}

// 删除了违反孤儿规则的impl块，使用FftComputer代替



// 辅助函数
fn bit_reverse_copy(input: &[Complex64]) -> Vec<Complex64> {
    let n = input.len();
    let mut output = vec![Complex64::new(0.0, 0.0); n];
    let bits = (n as f64).log2() as u32;

    #[cfg(feature = "parallel")]
    if ParallelManager::should_parallelize(n) {
        ParallelManager::execute(|| {
            output.par_iter_mut().enumerate().for_each(|(i, out_val)| {
                let input_idx = reverse_bits(i, bits as usize);
                *out_val = input[input_idx];
            });
        });
    } else {
        for i in 0..n {
            output[reverse_bits(i, bits as usize)] = input[i];
        }
    }
    
    #[cfg(not(feature = "parallel"))]
    {
        for i in 0..n {
            output[reverse_bits(i, bits as usize)] = input[i];
        }
    }
    
    output
}

// 重复的函数定义已删除，使用前面已定义的版本
