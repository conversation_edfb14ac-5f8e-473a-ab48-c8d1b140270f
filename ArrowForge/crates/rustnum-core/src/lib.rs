//! # RustNum Core
//! 

// ... existing code ...


#[cfg(any(feature = "default", feature = "array"))]
pub mod array;
pub mod simd;
pub mod error;
pub mod traits;
#[cfg(any(feature = "default", feature = "parallel"))]
pub mod parallel;
#[cfg(any(feature = "default", feature = "sparse"))]
pub mod sparse;
#[cfg(any(feature = "default", feature = "solvers"))]
pub mod solvers;
#[cfg(any(feature = "default", feature = "transform"))]
pub mod transform;
#[cfg(any(feature = "default", feature = "backend"))]
pub mod backend;
pub mod memory;
#[cfg(any(feature = "default", feature = "gpu"))]
pub mod gpu;
#[cfg(feature = "ml_scheduler")]
pub mod scheduler;
#[cfg(feature = "distributed")]
pub mod distributed;
#[cfg(feature = "ml")]
pub mod ml;
#[cfg(feature = "cloud")]
pub mod cloud;
#[cfg(feature = "bindings")]
pub mod bindings;
#[cfg(feature = "mlops")]
pub mod mlops;
#[cfg(feature = "edge")]
pub mod edge;
#[cfg(feature = "ai_native")]
pub mod ai_native;
#[cfg(feature = "industry")]
pub mod industry;
// pub mod benchmarks; // 暂时禁用

// faer-rs 集成模块
#[cfg(feature = "faer-comparison")]
pub mod faer_integration;

// Arrow 生态集成模块
#[cfg(feature = "arrow")]
pub mod arrow_integration;
#[cfg(any(feature = "openblas", feature = "intel-mkl", feature = "system-blas"))]
pub use backend::bindings::BlasWrapper;

pub use memory::allocator::SmartAllocator;
/// 在此进行全局状态初始化
pub fn initialize() {
    // 检测SIMD功能
    simd::detect_capabilities();
    // 初始化并行计算环境
    #[cfg(any(feature = "default", feature = "parallel"))]
    parallel::ParallelManager::initialize(None);
    // 初始化内存池
    let _pool = crate::memory::create_default_pool();
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_initialization() {
        initialize();
        // 验证初始化是否成功
    }
}
