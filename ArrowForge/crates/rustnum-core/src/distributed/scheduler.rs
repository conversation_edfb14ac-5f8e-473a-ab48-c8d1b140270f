//! 分布式任务调度器
//! 
//! 负责分布式计算任务的调度和执行

use crate::error::RustNumError;
use super::{DistributedConfig, FlightClient, TaskResult, TaskStatus, NodeHandle};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use tokio::time::{Duration, Instant};

/// 计算任务
#[derive(Debug, Clone)]
pub struct ComputeTask {
    pub task_id: String,
    pub operation: String,
    pub input_partitions: Vec<String>,
    pub output_partition: String,
    pub parameters: HashMap<String, String>,
    pub priority: TaskPriority,
    pub estimated_duration: Duration,
    pub dependencies: Vec<String>,
}

/// 任务优先级
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    Low = 1,
    Normal = 2,
    High = 3,
    Critical = 4,
}

/// 执行计划
#[derive(Debug)]
pub struct ExecutionPlan {
    pub tasks: Vec<ComputeTask>,
    pub estimated_total_time: Duration,
    pub resource_requirements: ResourceRequirements,
}

/// 资源需求
#[derive(Debug)]
pub struct ResourceRequirements {
    pub min_memory_mb: u64,
    pub min_cpu_cores: usize,
    pub requires_gpu: bool,
    pub network_bandwidth_mbps: u64,
}

/// 任务调度器
pub struct TaskScheduler {
    config: DistributedConfig,
    task_queue: Arc<Mutex<VecDeque<ComputeTask>>>,
    running_tasks: Arc<RwLock<HashMap<String, RunningTask>>>,
    completed_tasks: Arc<RwLock<HashMap<String, TaskResult>>>,
    node_loads: Arc<RwLock<HashMap<String, NodeLoad>>>,
    scheduler_stats: Arc<RwLock<SchedulerStats>>,
}

/// 运行中的任务
#[derive(Debug)]
struct RunningTask {
    task: ComputeTask,
    node_id: String,
    started_at: Instant,
    estimated_completion: Instant,
}

/// 节点负载
#[derive(Debug)]
struct NodeLoad {
    active_tasks: usize,
    cpu_usage: f64,
    memory_usage: f64,
    last_updated: Instant,
}

/// 调度器统计信息
#[derive(Debug)]
struct SchedulerStats {
    total_tasks_scheduled: u64,
    total_tasks_completed: u64,
    total_tasks_failed: u64,
    average_execution_time_ms: f64,
    total_execution_time_ms: u64,
}

impl TaskScheduler {
    /// 创建新的任务调度器
    pub fn new(config: DistributedConfig) -> Self {
        Self {
            config,
            task_queue: Arc::new(Mutex::new(VecDeque::new())),
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            completed_tasks: Arc::new(RwLock::new(HashMap::new())),
            node_loads: Arc::new(RwLock::new(HashMap::new())),
            scheduler_stats: Arc::new(RwLock::new(SchedulerStats {
                total_tasks_scheduled: 0,
                total_tasks_completed: 0,
                total_tasks_failed: 0,
                average_execution_time_ms: 0.0,
                total_execution_time_ms: 0,
            })),
        }
    }
    
    /// 提交任务
    pub async fn submit_task(&self, task: ComputeTask) -> Result<(), RustNumError> {
        let mut queue = self.task_queue.lock().await;
        
        // 按优先级插入任务
        let insert_pos = queue.iter().position(|t| t.priority < task.priority).unwrap_or(queue.len());
        queue.insert(insert_pos, task);
        
        // 更新统计信息
        let mut stats = self.scheduler_stats.write().await;
        stats.total_tasks_scheduled += 1;
        
        Ok(())
    }
    
    /// 执行任务
    pub async fn execute(&self, task: ComputeTask, clients: &HashMap<String, Arc<FlightClient>>) -> Result<TaskResult, RustNumError> {
        // 选择最佳节点
        let selected_node = self.select_best_node(&task, clients).await?;
        
        // 获取客户端
        let client = clients.get(&selected_node)
            .ok_or_else(|| RustNumError::NodeNotFound(selected_node.clone()))?;
        
        // 记录任务开始
        let running_task = RunningTask {
            task: task.clone(),
            node_id: selected_node.clone(),
            started_at: Instant::now(),
            estimated_completion: Instant::now() + task.estimated_duration,
        };
        
        {
            let mut running = self.running_tasks.write().await;
            running.insert(task.task_id.clone(), running_task);
        }
        
        // 执行任务（这里需要实际的客户端调用）
        let result = self.execute_on_node(&task, client).await;
        
        // 清理运行中的任务
        {
            let mut running = self.running_tasks.write().await;
            running.remove(&task.task_id);
        }
        
        // 记录完成的任务
        match &result {
            Ok(task_result) => {
                let mut completed = self.completed_tasks.write().await;
                completed.insert(task.task_id.clone(), task_result.clone());
                
                // 更新统计信息
                let mut stats = self.scheduler_stats.write().await;
                stats.total_tasks_completed += 1;
                stats.total_execution_time_ms += task_result.execution_time_ms;
                stats.average_execution_time_ms = 
                    stats.total_execution_time_ms as f64 / stats.total_tasks_completed as f64;
            }
            Err(_) => {
                let mut stats = self.scheduler_stats.write().await;
                stats.total_tasks_failed += 1;
            }
        }
        
        result
    }
    
    /// 选择最佳节点
    async fn select_best_node(&self, task: &ComputeTask, clients: &HashMap<String, Arc<FlightClient>>) -> Result<String, RustNumError> {
        let node_loads = self.node_loads.read().await;
        
        let mut best_node = None;
        let mut best_score = f64::INFINITY;
        
        for node in &self.config.nodes {
            if !clients.contains_key(node) {
                continue;
            }
            
            let load = node_loads.get(node);
            let score = self.calculate_node_score(task, load);
            
            if score < best_score {
                best_score = score;
                best_node = Some(node.clone());
            }
        }
        
        best_node.ok_or_else(|| RustNumError::NoAvailableNodes)
    }
    
    /// 计算节点评分
    fn calculate_node_score(&self, task: &ComputeTask, load: Option<&NodeLoad>) -> f64 {
        let base_score = match load {
            Some(load) => {
                // 考虑 CPU 使用率、内存使用率和活跃任务数
                let cpu_factor = load.cpu_usage / 100.0;
                let memory_factor = load.memory_usage / 100.0;
                let task_factor = load.active_tasks as f64 / 10.0; // 假设最大 10 个并发任务
                
                cpu_factor * 0.4 + memory_factor * 0.3 + task_factor * 0.3
            }
            None => 0.5, // 未知负载，给中等评分
        };
        
        // 根据任务优先级调整评分
        let priority_factor = match task.priority {
            TaskPriority::Critical => 0.8,
            TaskPriority::High => 0.9,
            TaskPriority::Normal => 1.0,
            TaskPriority::Low => 1.1,
        };
        
        base_score * priority_factor
    }
    
    /// 在节点上执行任务
    async fn execute_on_node(&self, task: &ComputeTask, _client: &Arc<FlightClient>) -> Result<TaskResult, RustNumError> {
        // 模拟任务执行
        let start_time = Instant::now();
        
        // 根据操作类型模拟不同的执行时间
        let execution_time = match task.operation.as_str() {
            "add" | "subtract" | "multiply" => Duration::from_millis(100),
            "matmul" => Duration::from_millis(500),
            "reduce" => Duration::from_millis(200),
            "map" => Duration::from_millis(150),
            "filter" => Duration::from_millis(120),
            _ => Duration::from_millis(100),
        };
        
        tokio::time::sleep(execution_time).await;
        
        let actual_execution_time = start_time.elapsed();
        
        Ok(TaskResult {
            task_id: task.task_id.clone(),
            status: TaskStatus::Completed,
            result_data: Some(vec![42; 100]), // 模拟结果数据
            execution_time_ms: actual_execution_time.as_millis() as u64,
            error_message: None,
        })
    }
    
    /// 更新节点负载
    pub async fn update_node_load(&self, node_id: String, cpu_usage: f64, memory_usage: f64, active_tasks: usize) {
        let mut loads = self.node_loads.write().await;
        loads.insert(node_id, NodeLoad {
            active_tasks,
            cpu_usage,
            memory_usage,
            last_updated: Instant::now(),
        });
    }
    
    /// 获取调度器统计信息
    pub async fn get_stats(&self) -> SchedulerStats {
        let stats = self.scheduler_stats.read().await;
        SchedulerStats {
            total_tasks_scheduled: stats.total_tasks_scheduled,
            total_tasks_completed: stats.total_tasks_completed,
            total_tasks_failed: stats.total_tasks_failed,
            average_execution_time_ms: stats.average_execution_time_ms,
            total_execution_time_ms: stats.total_execution_time_ms,
        }
    }
    
    /// 获取运行中的任务
    pub async fn get_running_tasks(&self) -> Vec<String> {
        let running = self.running_tasks.read().await;
        running.keys().cloned().collect()
    }
    
    /// 取消任务
    pub async fn cancel_task(&self, task_id: &str) -> Result<(), RustNumError> {
        // 从队列中移除
        {
            let mut queue = self.task_queue.lock().await;
            queue.retain(|task| task.task_id != task_id);
        }
        
        // 如果任务正在运行，标记为取消
        {
            let mut running = self.running_tasks.write().await;
            if let Some(_task) = running.remove(task_id) {
                // 这里应该通知执行节点取消任务
                // 简化实现，直接移除
            }
        }
        
        Ok(())
    }
    
    /// 创建执行计划
    pub fn create_execution_plan(&self, tasks: Vec<ComputeTask>) -> ExecutionPlan {
        let estimated_total_time = tasks.iter()
            .map(|task| task.estimated_duration)
            .sum();
        
        let resource_requirements = ResourceRequirements {
            min_memory_mb: 1024, // 1GB
            min_cpu_cores: 2,
            requires_gpu: false,
            network_bandwidth_mbps: 100,
        };
        
        ExecutionPlan {
            tasks,
            estimated_total_time,
            resource_requirements,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_task_scheduler_creation() {
        let config = DistributedConfig::default();
        let scheduler = TaskScheduler::new(config);
        
        let stats = scheduler.get_stats().await;
        assert_eq!(stats.total_tasks_scheduled, 0);
        assert_eq!(stats.total_tasks_completed, 0);
    }
    
    #[tokio::test]
    async fn test_task_submission() {
        let config = DistributedConfig::default();
        let scheduler = TaskScheduler::new(config);
        
        let task = ComputeTask {
            task_id: "test-task-1".to_string(),
            operation: "add".to_string(),
            input_partitions: vec!["partition-1".to_string()],
            output_partition: "result-1".to_string(),
            parameters: HashMap::new(),
            priority: TaskPriority::Normal,
            estimated_duration: Duration::from_millis(100),
            dependencies: vec![],
        };
        
        let result = scheduler.submit_task(task).await;
        assert!(result.is_ok());
        
        let stats = scheduler.get_stats().await;
        assert_eq!(stats.total_tasks_scheduled, 1);
    }
    
    #[tokio::test]
    async fn test_node_load_update() {
        let config = DistributedConfig::default();
        let scheduler = TaskScheduler::new(config);
        
        scheduler.update_node_load("node-1".to_string(), 45.5, 67.8, 3).await;
        
        let loads = scheduler.node_loads.read().await;
        let load = loads.get("node-1").unwrap();
        assert_eq!(load.cpu_usage, 45.5);
        assert_eq!(load.memory_usage, 67.8);
        assert_eq!(load.active_tasks, 3);
    }
    
    #[test]
    fn test_execution_plan_creation() {
        let config = DistributedConfig::default();
        let scheduler = TaskScheduler::new(config);
        
        let tasks = vec![
            ComputeTask {
                task_id: "task-1".to_string(),
                operation: "add".to_string(),
                input_partitions: vec![],
                output_partition: "result-1".to_string(),
                parameters: HashMap::new(),
                priority: TaskPriority::Normal,
                estimated_duration: Duration::from_millis(100),
                dependencies: vec![],
            },
            ComputeTask {
                task_id: "task-2".to_string(),
                operation: "matmul".to_string(),
                input_partitions: vec![],
                output_partition: "result-2".to_string(),
                parameters: HashMap::new(),
                priority: TaskPriority::High,
                estimated_duration: Duration::from_millis(500),
                dependencies: vec!["task-1".to_string()],
            },
        ];
        
        let plan = scheduler.create_execution_plan(tasks);
        assert_eq!(plan.tasks.len(), 2);
        assert_eq!(plan.estimated_total_time, Duration::from_millis(600));
    }
}
