//! 数据分区策略
//! 
//! 实现各种数据分区和负载均衡策略

use crate::error::RustNumError;
use std::collections::HashMap;
use std::hash::{Hash, Hasher};

/// 分区键
#[derive(Debug, <PERSON><PERSON>, Hash, PartialEq, Eq)]
pub struct PartitionKey {
    pub partition_id: usize,
    pub node_id: String,
}

/// 分区策略
#[derive(Debug, Clone)]
pub enum PartitionStrategy {
    /// 轮询分区
    RoundRobin,
    /// 哈希分区
    Hash { hash_function: HashFunction },
    /// 范围分区
    Range { ranges: Vec<(f64, f64)> },
    /// 基于大小的分区
    SizeBased { target_size_mb: u64 },
    /// 自定义分区
    Custom { partitioner: String },
}

/// 哈希函数类型
#[derive(Debug, Clone)]
pub enum HashFunction {
    Murmur3,
    Fnv,
    CityHash,
    Xxhash,
}

/// 分区器
pub struct Partitioner {
    strategy: PartitionStrategy,
    num_partitions: usize,
    nodes: Vec<String>,
    partition_stats: HashMap<PartitionKey, PartitionStats>,
}

/// 分区统计信息
#[derive(Debug, Clone)]
pub struct PartitionStats {
    pub size_bytes: u64,
    pub num_elements: usize,
    #[cfg(feature = "chrono")]
    pub last_accessed: chrono::DateTime<chrono::Utc>,
    #[cfg(not(feature = "chrono"))]
    pub last_accessed: std::time::SystemTime,
    pub access_count: u64,
    pub compression_ratio: f64,
}

impl Partitioner {
    /// 创建新的分区器
    pub fn new(strategy: PartitionStrategy, num_partitions: usize, nodes: Vec<String>) -> Self {
        Self {
            strategy,
            num_partitions,
            nodes,
            partition_stats: HashMap::new(),
        }
    }
    
    /// 为数据分配分区
    pub fn assign_partition<T>(&self, data: &[T], index: usize) -> Result<PartitionKey, RustNumError>
    where
        T: Hash
    {
        let partition_id = match &self.strategy {
            PartitionStrategy::RoundRobin => {
                index % self.num_partitions
            }
            PartitionStrategy::Hash { hash_function } => {
                let hash_value = self.calculate_hash(data, hash_function);
                (hash_value as usize) % self.num_partitions
            }
            PartitionStrategy::Range { ranges } => {
                // 对于范围分区，需要数值数据
                // 这里简化实现
                index % self.num_partitions
            }
            PartitionStrategy::SizeBased { target_size_mb: _ } => {
                // 基于大小的分区需要动态计算
                self.find_best_partition_by_size(data.len())
            }
            PartitionStrategy::Custom { partitioner: _ } => {
                // 自定义分区逻辑
                index % self.num_partitions
            }
        };
        
        let node_id = self.nodes[partition_id % self.nodes.len()].clone();
        
        Ok(PartitionKey {
            partition_id,
            node_id,
        })
    }
    
    /// 计算哈希值
    fn calculate_hash<T>(&self, data: &[T], hash_function: &HashFunction) -> u64
    where
        T: Hash
    {
        match hash_function {
            HashFunction::Murmur3 => {
                let mut hasher = std::collections::hash_map::DefaultHasher::new();
                data.hash(&mut hasher);
                hasher.finish()
            }
            HashFunction::Fnv => {
                let mut hasher = std::collections::hash_map::DefaultHasher::new();
                data.hash(&mut hasher);
                hasher.finish()
            }
            HashFunction::CityHash => {
                // 简化实现，使用默认哈希
                let mut hasher = std::collections::hash_map::DefaultHasher::new();
                data.hash(&mut hasher);
                hasher.finish()
            }
            HashFunction::Xxhash => {
                // 简化实现，使用默认哈希
                let mut hasher = std::collections::hash_map::DefaultHasher::new();
                data.hash(&mut hasher);
                hasher.finish()
            }
        }
    }
    
    /// 基于大小查找最佳分区
    fn find_best_partition_by_size(&self, data_size: usize) -> usize {
        // 找到当前大小最小的分区
        let mut best_partition = 0;
        let mut min_size = u64::MAX;
        
        for i in 0..self.num_partitions {
            let node_id = &self.nodes[i % self.nodes.len()];
            let key = PartitionKey {
                partition_id: i,
                node_id: node_id.clone(),
            };
            
            let current_size = self.partition_stats.get(&key)
                .map(|stats| stats.size_bytes)
                .unwrap_or(0);
            
            if current_size < min_size {
                min_size = current_size;
                best_partition = i;
            }
        }
        
        best_partition
    }
    
    /// 重新平衡分区
    pub fn rebalance(&mut self) -> Result<Vec<RebalanceOperation>, RustNumError> {
        let mut operations = Vec::new();
        
        // 计算平均分区大小
        let total_size: u64 = self.partition_stats.values().map(|stats| stats.size_bytes).sum();
        let average_size = total_size / self.num_partitions as u64;
        let threshold = average_size as f64 * 0.2; // 20% 阈值
        
        // 找到过大和过小的分区
        let mut oversized_partitions = Vec::new();
        let mut undersized_partitions = Vec::new();
        
        for (key, stats) in &self.partition_stats {
            let size_diff = stats.size_bytes as f64 - average_size as f64;
            
            if size_diff > threshold {
                oversized_partitions.push((key.clone(), size_diff));
            } else if size_diff < -threshold {
                undersized_partitions.push((key.clone(), -size_diff));
            }
        }
        
        // 生成重新平衡操作
        oversized_partitions.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        undersized_partitions.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        
        for (oversized_key, excess) in oversized_partitions {
            for (undersized_key, deficit) in &undersized_partitions {
                if excess > 0.0 && *deficit > 0.0 {
                    let transfer_size = excess.min(*deficit);
                    
                    operations.push(RebalanceOperation {
                        operation_type: RebalanceOperationType::Move,
                        source_partition: oversized_key.clone(),
                        target_partition: undersized_key.clone(),
                        data_size_bytes: transfer_size as u64,
                        estimated_time_ms: self.estimate_transfer_time(transfer_size as u64),
                    });
                    
                    break;
                }
            }
        }
        
        Ok(operations)
    }
    
    /// 估算传输时间
    fn estimate_transfer_time(&self, size_bytes: u64) -> u64 {
        // 假设网络带宽为 100 MB/s
        let bandwidth_mbps = 100 * 1024 * 1024; // 100 MB/s in bytes
        let transfer_time_ms = (size_bytes * 1000) / bandwidth_mbps;
        transfer_time_ms.max(10) // 最少 10ms
    }
    
    /// 更新分区统计信息
    pub fn update_partition_stats(&mut self, key: PartitionKey, stats: PartitionStats) {
        self.partition_stats.insert(key, stats);
    }
    
    /// 获取分区统计信息
    pub fn get_partition_stats(&self, key: &PartitionKey) -> Option<&PartitionStats> {
        self.partition_stats.get(key)
    }
    
    /// 获取所有分区的统计信息
    pub fn get_all_stats(&self) -> &HashMap<PartitionKey, PartitionStats> {
        &self.partition_stats
    }
    
    /// 获取负载均衡报告
    pub fn get_load_balance_report(&self) -> LoadBalanceReport {
        let total_size: u64 = self.partition_stats.values().map(|stats| stats.size_bytes).sum();
        let total_elements: usize = self.partition_stats.values().map(|stats| stats.num_elements).sum();
        
        let average_size = if self.num_partitions > 0 {
            total_size / self.num_partitions as u64
        } else {
            0
        };
        
        let mut size_variance = 0.0;
        for stats in self.partition_stats.values() {
            let diff = stats.size_bytes as f64 - average_size as f64;
            size_variance += diff * diff;
        }
        size_variance /= self.num_partitions as f64;
        
        let balance_factor = if average_size > 0 {
            1.0 - (size_variance.sqrt() / average_size as f64)
        } else {
            1.0
        };
        
        LoadBalanceReport {
            total_partitions: self.num_partitions,
            total_size_bytes: total_size,
            total_elements,
            average_partition_size: average_size,
            size_variance,
            balance_factor: balance_factor.max(0.0).min(1.0),
            hotspot_partitions: self.identify_hotspots(),
        }
    }
    
    /// 识别热点分区
    fn identify_hotspots(&self) -> Vec<PartitionKey> {
        let average_access = if !self.partition_stats.is_empty() {
            self.partition_stats.values().map(|stats| stats.access_count).sum::<u64>() as f64 
                / self.partition_stats.len() as f64
        } else {
            0.0
        };
        
        let hotspot_threshold = average_access * 2.0; // 访问次数超过平均值 2 倍
        
        self.partition_stats.iter()
            .filter(|(_, stats)| stats.access_count as f64 > hotspot_threshold)
            .map(|(key, _)| key.clone())
            .collect()
    }
}

/// 重新平衡操作
#[derive(Debug, Clone)]
pub struct RebalanceOperation {
    pub operation_type: RebalanceOperationType,
    pub source_partition: PartitionKey,
    pub target_partition: PartitionKey,
    pub data_size_bytes: u64,
    pub estimated_time_ms: u64,
}

/// 重新平衡操作类型
#[derive(Debug, Clone)]
pub enum RebalanceOperationType {
    Move,
    Copy,
    Split,
    Merge,
}

/// 负载均衡报告
#[derive(Debug)]
pub struct LoadBalanceReport {
    pub total_partitions: usize,
    pub total_size_bytes: u64,
    pub total_elements: usize,
    pub average_partition_size: u64,
    pub size_variance: f64,
    pub balance_factor: f64, // 0.0 (完全不平衡) 到 1.0 (完全平衡)
    pub hotspot_partitions: Vec<PartitionKey>,
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_partitioner_creation() {
        let strategy = PartitionStrategy::RoundRobin;
        let nodes = vec!["node1".to_string(), "node2".to_string()];
        let partitioner = Partitioner::new(strategy, 4, nodes);
        
        assert_eq!(partitioner.num_partitions, 4);
        assert_eq!(partitioner.nodes.len(), 2);
    }
    
    #[test]
    fn test_round_robin_partitioning() {
        let strategy = PartitionStrategy::RoundRobin;
        let nodes = vec!["node1".to_string(), "node2".to_string()];
        let partitioner = Partitioner::new(strategy, 4, nodes);
        
        let data = vec![1, 2, 3, 4, 5];
        
        let key0 = partitioner.assign_partition(&data, 0).unwrap();
        let key1 = partitioner.assign_partition(&data, 1).unwrap();
        let key4 = partitioner.assign_partition(&data, 4).unwrap();
        
        assert_eq!(key0.partition_id, 0);
        assert_eq!(key1.partition_id, 1);
        assert_eq!(key4.partition_id, 0); // 4 % 4 = 0
    }
    
    #[test]
    fn test_hash_partitioning() {
        let strategy = PartitionStrategy::Hash { 
            hash_function: HashFunction::Murmur3 
        };
        let nodes = vec!["node1".to_string(), "node2".to_string()];
        let partitioner = Partitioner::new(strategy, 4, nodes);
        
        let data1 = vec![1, 2, 3];
        let data2 = vec![1, 2, 3]; // 相同数据应该分配到相同分区
        
        let key1 = partitioner.assign_partition(&data1, 0).unwrap();
        let key2 = partitioner.assign_partition(&data2, 0).unwrap();
        
        assert_eq!(key1.partition_id, key2.partition_id);
    }
    
    #[test]
    fn test_partition_stats_update() {
        let strategy = PartitionStrategy::RoundRobin;
        let nodes = vec!["node1".to_string()];
        let mut partitioner = Partitioner::new(strategy, 2, nodes);
        
        let key = PartitionKey {
            partition_id: 0,
            node_id: "node1".to_string(),
        };
        
        let stats = PartitionStats {
            size_bytes: 1024,
            num_elements: 100,
            #[cfg(feature = "chrono")]
            last_accessed: chrono::Utc::now(),
            #[cfg(not(feature = "chrono"))]
            last_accessed: std::time::SystemTime::now(),
            access_count: 5,
            compression_ratio: 0.8,
        };
        
        partitioner.update_partition_stats(key.clone(), stats);
        
        let retrieved_stats = partitioner.get_partition_stats(&key);
        assert!(retrieved_stats.is_some());
        assert_eq!(retrieved_stats.unwrap().size_bytes, 1024);
    }
    
    #[test]
    fn test_load_balance_report() {
        let strategy = PartitionStrategy::RoundRobin;
        let nodes = vec!["node1".to_string(), "node2".to_string()];
        let mut partitioner = Partitioner::new(strategy, 2, nodes);
        
        // 添加一些统计信息
        for i in 0..2 {
            let key = PartitionKey {
                partition_id: i,
                node_id: format!("node{}", i + 1),
            };
            
            let stats = PartitionStats {
                size_bytes: 1000 + i as u64 * 100,
                num_elements: 100 + i * 10,
                #[cfg(feature = "chrono")]
                last_accessed: chrono::Utc::now(),
                #[cfg(not(feature = "chrono"))]
                last_accessed: std::time::SystemTime::now(),
                access_count: 10 + i as u64 * 5,
                compression_ratio: 0.8,
            };
            
            partitioner.update_partition_stats(key, stats);
        }
        
        let report = partitioner.get_load_balance_report();
        assert_eq!(report.total_partitions, 2);
        assert!(report.balance_factor >= 0.0 && report.balance_factor <= 1.0);
    }
}
