//! 边缘计算支持模块
//! 
//! 提供移动设备、IoT 设备和边缘节点的计算支持

pub mod device;
pub mod runtime;
pub mod optimization;
pub mod sync;
pub mod offline;

pub use device::{EdgeDevice, DeviceCapability, DeviceProfile};
pub use runtime::{EdgeRuntime, RuntimeConfig, ExecutionMode};
pub use optimization::{ModelOptimizer, OptimizationStrategy, CompressionMethod};
pub use sync::{SyncManager, SyncStrategy, DataSync};
pub use offline::{OfflineCompute, CacheManager, LocalStorage};

use crate::error::RustNumError;
use crate::ml::tensor::Tensor;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// 边缘计算配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EdgeConfig {
    /// 设备配置
    pub device: DeviceConfig,
    /// 运行时配置
    pub runtime: RuntimeConfig,
    /// 优化配置
    pub optimization: OptimizationConfig,
    /// 同步配置
    pub sync: SyncConfig,
}

/// 设备配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceConfig {
    pub device_type: DeviceType,
    pub capabilities: DeviceCapability,
    pub constraints: ResourceConstraints,
    pub profile: DeviceProfile,
}

/// 设备类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeviceType {
    Mobile { platform: MobilePlatform },
    IoT { category: IoTCategory },
    Edge { tier: EdgeTier },
    Embedded { architecture: EmbeddedArch },
}

/// 移动平台
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MobilePlatform {
    iOS,
    Android,
    HarmonyOS,
}

/// IoT 类别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IoTCategory {
    Sensor,
    Gateway,
    Controller,
    Camera,
}

/// 边缘层级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EdgeTier {
    Micro,    // 微边缘
    Mini,     // 小边缘
    Standard, // 标准边缘
    Heavy,    // 重边缘
}

/// 嵌入式架构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmbeddedArch {
    ARM,
    RISC_V,
    x86,
    MIPS,
}

/// 资源约束
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceConstraints {
    pub max_memory_mb: u32,
    pub max_storage_mb: u32,
    pub max_cpu_cores: u8,
    pub battery_powered: bool,
    pub network_limited: bool,
}

/// 优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationConfig {
    pub model_compression: bool,
    pub quantization: QuantizationConfig,
    pub pruning: PruningConfig,
    pub knowledge_distillation: bool,
}

/// 量化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QuantizationConfig {
    pub enabled: bool,
    pub precision: QuantizationPrecision,
    pub calibration_data_size: usize,
}

/// 量化精度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QuantizationPrecision {
    Int8,
    Int16,
    Float16,
    BFloat16,
}

/// 剪枝配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PruningConfig {
    pub enabled: bool,
    pub sparsity_ratio: f32,
    pub structured: bool,
}

/// 同步配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncConfig {
    pub enabled: bool,
    pub strategy: SyncStrategy,
    pub batch_size: usize,
    pub sync_interval: u64,
    pub compression: bool,
}

/// 边缘计算管理器
pub struct EdgeManager {
    config: EdgeConfig,
    devices: HashMap<String, EdgeDevice>,
    runtimes: HashMap<String, EdgeRuntime>,
    optimizer: ModelOptimizer,
    sync_manager: SyncManager,
    cache_manager: CacheManager,
}

impl EdgeManager {
    /// 创建新的边缘计算管理器
    pub fn new(config: EdgeConfig) -> Result<Self, RustNumError> {
        Ok(Self {
            optimizer: ModelOptimizer::new(config.optimization.clone())?,
            sync_manager: SyncManager::new(config.sync.clone())?,
            cache_manager: CacheManager::new()?,
            devices: HashMap::new(),
            runtimes: HashMap::new(),
            config,
        })
    }
    
    /// 注册边缘设备
    pub fn register_device(&mut self, device: EdgeDevice) -> Result<String, RustNumError> {
        let device_id = device.id.clone();
        
        // 创建适配的运行时
        let runtime = self.create_runtime_for_device(&device)?;
        self.runtimes.insert(device_id.clone(), runtime);
        
        self.devices.insert(device_id.clone(), device);
        
        Ok(device_id)
    }
    
    /// 为设备创建运行时
    fn create_runtime_for_device(&self, device: &EdgeDevice) -> Result<EdgeRuntime, RustNumError> {
        let runtime_config = RuntimeConfig {
            execution_mode: self.determine_execution_mode(device),
            memory_limit: device.capabilities.memory_mb,
            thread_count: device.capabilities.cpu_cores as usize,
            use_gpu: device.capabilities.has_gpu,
            optimization_level: self.determine_optimization_level(device),
        };
        
        EdgeRuntime::new(runtime_config)
    }
    
    /// 确定执行模式
    fn determine_execution_mode(&self, device: &EdgeDevice) -> ExecutionMode {
        match device.profile.device_type {
            DeviceType::Mobile { .. } => ExecutionMode::LowLatency,
            DeviceType::IoT { .. } => ExecutionMode::LowPower,
            DeviceType::Edge { tier: EdgeTier::Heavy } => ExecutionMode::HighThroughput,
            _ => ExecutionMode::Balanced,
        }
    }
    
    /// 确定优化级别
    fn determine_optimization_level(&self, device: &EdgeDevice) -> OptimizationLevel {
        if device.profile.constraints.max_memory_mb < 512 {
            OptimizationLevel::Aggressive
        } else if device.profile.constraints.max_memory_mb < 2048 {
            OptimizationLevel::Moderate
        } else {
            OptimizationLevel::Conservative
        }
    }
    
    /// 部署模型到边缘设备
    pub async fn deploy_model(&mut self, device_id: &str, model: &Tensor<f32>, model_config: ModelConfig) -> Result<String, RustNumError> {
        let device = self.devices.get(device_id)
            .ok_or_else(|| RustNumError::InvalidInput(format!("Device not found: {}", device_id)))?;
        
        // 优化模型
        let optimized_model = self.optimizer.optimize_for_device(model, device).await?;
        
        // 部署到运行时
        let runtime = self.runtimes.get_mut(device_id)
            .ok_or_else(|| RustNumError::InvalidState("Runtime not found".into()))?;
        
        let deployment_id = runtime.deploy_model(optimized_model, model_config).await?;
        
        Ok(deployment_id)
    }
    
    /// 在边缘设备上执行推理
    pub async fn inference(&self, device_id: &str, deployment_id: &str, input: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        let runtime = self.runtimes.get(device_id)
            .ok_or_else(|| RustNumError::InvalidInput(format!("Runtime not found for device: {}", device_id)))?;
        
        runtime.inference(deployment_id, input).await
    }
    
    /// 同步数据到云端
    pub async fn sync_to_cloud(&mut self, device_id: &str, data: Vec<u8>) -> Result<(), RustNumError> {
        self.sync_manager.sync_data(device_id, data).await
    }
    
    /// 从云端同步数据
    pub async fn sync_from_cloud(&mut self, device_id: &str) -> Result<Vec<u8>, RustNumError> {
        self.sync_manager.fetch_data(device_id).await
    }
    
    /// 获取设备状态
    pub fn get_device_status(&self, device_id: &str) -> Result<DeviceStatus, RustNumError> {
        let device = self.devices.get(device_id)
            .ok_or_else(|| RustNumError::InvalidInput(format!("Device not found: {}", device_id)))?;
        
        let runtime = self.runtimes.get(device_id)
            .ok_or_else(|| RustNumError::InvalidState("Runtime not found".into()))?;
        
        Ok(DeviceStatus {
            device_id: device_id.to_string(),
            online: true,
            cpu_usage: runtime.get_cpu_usage(),
            memory_usage: runtime.get_memory_usage(),
            battery_level: device.get_battery_level(),
            network_status: device.get_network_status(),
            deployed_models: runtime.list_deployed_models(),
        })
    }
    
    /// 获取边缘集群状态
    pub fn get_cluster_status(&self) -> EdgeClusterStatus {
        let total_devices = self.devices.len();
        let online_devices = self.devices.values()
            .filter(|device| device.is_online())
            .count();
        
        let total_memory = self.devices.values()
            .map(|device| device.capabilities.memory_mb as u64)
            .sum();
        
        let used_memory = self.runtimes.values()
            .map(|runtime| runtime.get_memory_usage() as u64)
            .sum();
        
        EdgeClusterStatus {
            total_devices,
            online_devices,
            total_memory_mb: total_memory,
            used_memory_mb: used_memory,
            active_deployments: self.runtimes.values()
                .map(|runtime| runtime.count_deployments())
                .sum(),
        }
    }
}

/// 模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub name: String,
    pub version: String,
    pub input_shape: Vec<usize>,
    pub output_shape: Vec<usize>,
    pub batch_size: usize,
    pub precision: ModelPrecision,
}

/// 模型精度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelPrecision {
    Float32,
    Float16,
    Int8,
    Int16,
}

/// 优化级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OptimizationLevel {
    Conservative,
    Moderate,
    Aggressive,
}

/// 设备状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceStatus {
    pub device_id: String,
    pub online: bool,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub battery_level: Option<f32>,
    pub network_status: NetworkStatus,
    pub deployed_models: Vec<String>,
}

/// 网络状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NetworkStatus {
    Connected { signal_strength: f32 },
    Disconnected,
    Limited,
}

/// 边缘集群状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EdgeClusterStatus {
    pub total_devices: usize,
    pub online_devices: usize,
    pub total_memory_mb: u64,
    pub used_memory_mb: u64,
    pub active_deployments: usize,
}

impl Default for EdgeConfig {
    fn default() -> Self {
        Self {
            device: DeviceConfig {
                device_type: DeviceType::Edge { tier: EdgeTier::Standard },
                capabilities: DeviceCapability {
                    memory_mb: 4096,
                    storage_mb: 32768,
                    cpu_cores: 4,
                    has_gpu: false,
                    has_npu: false,
                },
                constraints: ResourceConstraints {
                    max_memory_mb: 2048,
                    max_storage_mb: 16384,
                    max_cpu_cores: 2,
                    battery_powered: false,
                    network_limited: false,
                },
                profile: DeviceProfile::default(),
            },
            runtime: RuntimeConfig {
                execution_mode: ExecutionMode::Balanced,
                memory_limit: 2048,
                thread_count: 2,
                use_gpu: false,
                optimization_level: OptimizationLevel::Moderate,
            },
            optimization: OptimizationConfig {
                model_compression: true,
                quantization: QuantizationConfig {
                    enabled: true,
                    precision: QuantizationPrecision::Int8,
                    calibration_data_size: 1000,
                },
                pruning: PruningConfig {
                    enabled: true,
                    sparsity_ratio: 0.5,
                    structured: false,
                },
                knowledge_distillation: false,
            },
            sync: SyncConfig {
                enabled: true,
                strategy: SyncStrategy::Incremental,
                batch_size: 100,
                sync_interval: 300,
                compression: true,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_edge_config_creation() {
        let config = EdgeConfig::default();
        assert!(config.optimization.model_compression);
        assert!(config.sync.enabled);
        assert_eq!(config.device.capabilities.cpu_cores, 4);
    }
    
    #[test]
    fn test_edge_manager_creation() {
        let config = EdgeConfig::default();
        let manager = EdgeManager::new(config);
        assert!(manager.is_ok());
    }
    
    #[test]
    fn test_device_type_matching() {
        let mobile_device = DeviceType::Mobile { platform: MobilePlatform::Android };
        let iot_device = DeviceType::IoT { category: IoTCategory::Sensor };
        
        match mobile_device {
            DeviceType::Mobile { .. } => assert!(true),
            _ => assert!(false),
        }
        
        match iot_device {
            DeviceType::IoT { .. } => assert!(true),
            _ => assert!(false),
        }
    }
}
