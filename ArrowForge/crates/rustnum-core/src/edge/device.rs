//! 边缘设备实现

use crate::error::RustNumError;
use super::{DeviceType, ResourceConstraints, NetworkStatus};
use serde::{Serialize, Deserialize};

/// 边缘设备
#[derive(Debug, Clone)]
pub struct EdgeDevice {
    pub id: String,
    pub name: String,
    pub capabilities: DeviceCapability,
    pub profile: DeviceProfile,
    pub status: DeviceStatus,
}

/// 设备能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceCapability {
    pub memory_mb: u32,
    pub storage_mb: u32,
    pub cpu_cores: u8,
    pub has_gpu: bool,
    pub has_npu: bool,
}

/// 设备配置文件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceProfile {
    pub device_type: DeviceType,
    pub constraints: ResourceConstraints,
    pub power_profile: PowerProfile,
    pub network_profile: NetworkProfile,
}

/// 电源配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PowerProfile {
    pub battery_powered: bool,
    pub max_power_watts: f32,
    pub idle_power_watts: f32,
    pub thermal_limit_celsius: f32,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkProfile {
    pub wifi_enabled: bool,
    pub cellular_enabled: bool,
    pub bluetooth_enabled: bool,
    pub max_bandwidth_mbps: f32,
}

/// 设备状态
#[derive(Debug, Clone)]
pub struct DeviceStatus {
    pub online: bool,
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub storage_usage: f32,
    pub temperature: f32,
    pub battery_level: Option<f32>,
    pub network_status: NetworkStatus,
}

impl EdgeDevice {
    pub fn new(id: String, name: String, capabilities: DeviceCapability, profile: DeviceProfile) -> Self {
        Self {
            id,
            name,
            capabilities,
            profile,
            status: DeviceStatus {
                online: true,
                cpu_usage: 0.0,
                memory_usage: 0.0,
                storage_usage: 0.0,
                temperature: 25.0,
                battery_level: if profile.power_profile.battery_powered { Some(100.0) } else { None },
                network_status: NetworkStatus::Connected { signal_strength: 0.8 },
            },
        }
    }
    
    pub fn is_online(&self) -> bool {
        self.status.online
    }
    
    pub fn get_battery_level(&self) -> Option<f32> {
        self.status.battery_level
    }
    
    pub fn get_network_status(&self) -> NetworkStatus {
        self.status.network_status.clone()
    }
    
    pub fn update_status(&mut self, cpu_usage: f32, memory_usage: f32) {
        self.status.cpu_usage = cpu_usage;
        self.status.memory_usage = memory_usage;
    }
}

impl Default for DeviceProfile {
    fn default() -> Self {
        Self {
            device_type: DeviceType::Edge { tier: super::EdgeTier::Standard },
            constraints: ResourceConstraints {
                max_memory_mb: 2048,
                max_storage_mb: 16384,
                max_cpu_cores: 2,
                battery_powered: false,
                network_limited: false,
            },
            power_profile: PowerProfile {
                battery_powered: false,
                max_power_watts: 50.0,
                idle_power_watts: 5.0,
                thermal_limit_celsius: 85.0,
            },
            network_profile: NetworkProfile {
                wifi_enabled: true,
                cellular_enabled: false,
                bluetooth_enabled: true,
                max_bandwidth_mbps: 100.0,
            },
        }
    }
}
