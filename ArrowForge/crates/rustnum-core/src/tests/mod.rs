#[cfg(test)]
mod tests {
    use crate::sparse::storage::CsrMatrix;
    use crate::sparse::decomposition::SparseLU;
    use std::time::Duration;
    
    #[test]
    fn test_sparse_lu_small_matrix() {
        // 创建一个小型稀疏矩阵
        let mut matrix = CsrMatrix::with_capacity(3, 3, 5);
        matrix.insert(0, 0, 2.0).unwrap();
        matrix.insert(0, 1, 1.0).unwrap();
        matrix.insert(1, 1, 4.0).unwrap();
        matrix.insert(2, 1, -1.0).unwrap();
        matrix.insert(2, 2, 3.0).unwrap();
        
                let lu = SparseLU::lu_decompose(&matrix).unwrap();
        
        // 验证分解结果
        let b = vec![1.0, 2.0, 3.0];
        let x = lu.solve(&b).unwrap();
        
        // 验证解的准确性
        let residual = matrix.multiply(&x)
            .iter()
            .zip(b.iter())
            .map(|(a, b)| (a - b).abs())
            .sum::<f64>();
            
        assert!(residual < 1e-10);
    }
    
    #[test]
    fn test_gpu_matrix_multiply() {
        if let Ok(engine) = GpuEngine::new() {
            let a = vec![1.0f32, 2.0, 3.0, 4.0];
            let b = vec![5.0f32, 6.0, 7.0, 8.0];
            
            let result = engine.matrix_multiply(&a, &b, 2, 2, 2).unwrap();
            
            // 验证结果
            let expected = vec![19.0, 22.0, 43.0, 50.0];
            for (r, e) in result.iter().zip(expected.iter()) {
                assert!((r - e).abs() < 1e-6);
            }
        }
    }
    
    #[test]
    fn test_scheduler_priority_adjustment() {
        let scheduler = IntelligentScheduler::new();
        
        let task = ComputeTask {
            id: 1,
            priority: TaskPriority::Normal,
            affinity: None,
            gpu_required: false,
            memory_estimate: 1024 * 1024 * 1024, // 1GB
        };
        
        scheduler.submit(task);
        
        // 验证任务是否被正确调度
        // TODO: 添加更多断言
    }
    
    #[test]
    fn test_resource_monitor_updates() {
        let monitor = ResourceMonitor {
            cpu_usage: Arc::new(RwLock::new(vec![0.0; 4])),
            memory_usage: Arc::new(RwLock::new(0.0)),
            gpu_usage: Arc::new(RwLock::new(None)),
        };
        
        monitor.update();
        
        // 验证资源监控数据是否正确更新
        let cpu_usage = monitor.cpu_usage.read();
        assert!(cpu_usage.len() > 0);
        
        let memory_usage = monitor.memory_usage.read();
        assert!(*memory_usage >= 0.0 && *memory_usage <= 100.0);
    }
    
    #[test]
    fn test_numa_aware_allocation() {
        let scheduler = IntelligentScheduler::new();
        
        let task = ComputeTask {
            id: 2,
            priority: TaskPriority::High,
            affinity: None,
            gpu_required: false,
            memory_estimate: 2 * 1024 * 1024 * 1024, // 2GB
        };
        
        let allocated_cores = scheduler.numa_aware_allocation(&task);
        
        // 验证核心分配是否合理
        assert!(!allocated_cores.is_empty());
        assert!(allocated_cores.len() <= num_cpus::get());
    }
}
