pub mod simd;
pub mod ops;
pub mod features;

// 重新导出主要类型
pub use simd::{SimdElement, SimdOp, SimdDispatcher};
pub use ops::{SimdExecutor, GenericSimdExecutor, ScalarExecutor};
pub use features::{SimdFeatures, SimdCapabilities, SimdVector};
use std::sync::atomic::{AtomicU32, Ordering};


/// SIMD功能支持标志
static SIMD_CAPABILITIES: AtomicU32 = AtomicU32::new(0);

const AVX2_SUPPORT: u32 = 1 << 0;
const AVX512F_SUPPORT: u32 = 1 << 1;
const NEON_SUPPORT: u32 = 1 << 2;

/// 检测CPU SIMD支持情况
pub fn detect_capabilities() {
    let mut caps = 0;
    #[cfg(target_arch = "x86_64")]
    {
        if is_x86_feature_detected!("avx2") {
            caps |= AVX2_SUPPORT;
        }
        if is_x86_feature_detected!("avx512f") {
            caps |= AVX512F_SUPPORT;
        }
    }
    #[cfg(target_arch = "aarch64")]
    {
        caps |= NEON_SUPPORT;
    }
    SIMD_CAPABILITIES.store(caps, Ordering::SeqCst);
}

/// 检查特定SIMD特性是否支持
pub fn has_capability(cap: u32) -> bool {
    SIMD_CAPABILITIES.load(Ordering::Relaxed) & cap != 0
}

/// 使用SIMD执行数组操作
pub fn execute_simd_op<T>(op: SimdOp, a: &[T], b: &[T], out: &mut [T])
where
    T: SimdElement +
       Copy + std::ops::Add<Output = T> + std::ops::Sub<Output = T> +
       std::ops::Mul<Output = T> + std::ops::Div<Output = T> +
       std::cmp::PartialOrd,
{
    let dispatcher = SimdDispatcher::new();
    let executor = dispatcher.get_executor::<T>();
    executor.execute(op, a, b, out);
}

/// 快速向量加法
pub fn simd_add<T>(a: &[T], b: &[T], out: &mut [T])
where
    T: SimdElement + Copy + std::ops::Add<Output = T> + std::ops::Sub<Output = T> +
       std::ops::Mul<Output = T> + std::ops::Div<Output = T> +
       PartialOrd,
{
    execute_simd_op(SimdOp::Add, a, b, out);
}

/// 快速向量乘法
pub fn simd_mul<T>(a: &[T], b: &[T], out: &mut [T])
where
    T: SimdElement + Copy + std::ops::Add<Output = T> + std::ops::Sub<Output = T> +
       std::ops::Mul<Output = T> + std::ops::Div<Output = T> +
       PartialOrd,
{
    execute_simd_op(SimdOp::Mul, a, b, out);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simd_operations() {
        let a = vec![1.0f32; 1024];
        let b = vec![2.0f32; 1024];
        let mut c = vec![0.0f32; 1024];

        // 测试加法
        simd_add(&a, &b, &mut c);
        assert!(c.iter().all(|&x| (x - 3.0).abs() < 1e-6));

        // 测试乘法
        simd_mul(&a, &b, &mut c);
        assert!(c.iter().all(|&x| (x - 2.0).abs() < 1e-6));
    }
}
