use std::arch::x86_64::*;
use std::sync::Once;

#[cfg(target_arch = "x86_64")]
use std::arch::x86_64::*;

/// SIMD支持的特性标记
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SimdFeatures {
    /// SSE4.2支持
    pub has_sse42: bool,
    /// AVX2支持
    pub has_avx2: bool,
    /// AVX-512F支持
    pub has_avx512f: bool,
    /// ARM NEON支持（在ARM架构上）
    #[cfg(target_arch = "aarch64")]
    pub has_neon: bool,
}

/// SIMD特性检测单例
pub struct SimdCapabilities {
    features: SimdFeatures,
}

impl SimdCapabilities {
    /// 获取SIMD特性实例
    pub fn get() -> &'static SimdCapabilities {
        static INIT: Once = Once::new();
        static mut INSTANCE: Option<SimdCapabilities> = None;

        INIT.call_once(|| {
            unsafe {
                INSTANCE = Some(SimdCapabilities {
                    features: SimdFeatures::detect(),
                });
            }
        });

        unsafe { INSTANCE.as_ref().unwrap() }
    }

    /// 获取支持的特性
    pub fn features(&self) -> SimdFeatures {
        self.features
    }
}

impl SimdFeatures {
    /// 检测当前CPU支持的SIMD特性
    pub fn detect() -> Self {
        #[cfg(target_arch = "x86_64")]
        {
            Self {
                has_sse42: is_x86_feature_detected!("sse4.2"),
                has_avx2: is_x86_feature_detected!("avx2"),
                has_avx512f: is_x86_feature_detected!("avx512f"),
                #[cfg(target_arch = "aarch64")]
                has_neon: false,
            }
        }
        #[cfg(target_arch = "aarch64")]
        {
            Self {
                has_sse42: false,
                has_avx2: false,
                has_avx512f: false,
                has_neon: true,
            }
        }
        #[cfg(not(any(target_arch = "x86_64", target_arch = "aarch64")))]
        {
            Self {
                has_sse42: false,
                has_avx2: false,
                has_avx512f: false,
                #[cfg(target_arch = "aarch64")]
                has_neon: false,
            }
        }
    }
}

/// SIMD向量特征
pub trait SimdVector: Sized + Copy {
    /// 向量中元素的类型
    type Element;
    /// 向量的长度
    const LENGTH: usize;
    
    /// 全零向量
    fn zeros() -> Self;
    /// 全一向量
    fn ones() -> Self;
    /// 设置所有元素为指定值
    fn splat(value: Self::Element) -> Self;
    /// 从切片加载
    unsafe fn load(ptr: *const Self::Element) -> Self;
    /// 存储到切片
    unsafe fn store(self, ptr: *mut Self::Element);
    /// 加法
    fn add(self, other: Self) -> Self;
    /// 减法
    fn sub(self, other: Self) -> Self;
    /// 乘法
    fn mul(self, other: Self) -> Self;
    /// 除法
    fn div(self, other: Self) -> Self;
    /// 取最大值
    fn max(self, other: Self) -> Self;
    /// 取最小值
    fn min(self, other: Self) -> Self;
}

/// AVX2向量实现（f32 x 8）
#[cfg(target_arch = "x86_64")]
#[derive(Clone, Copy)]
pub struct Avx2F32Vec(__m256);

#[cfg(target_arch = "x86_64")]
impl SimdVector for Avx2F32Vec {
    type Element = f32;
    const LENGTH: usize = 8;

    #[inline(always)]
    fn zeros() -> Self {
        unsafe { Self(_mm256_setzero_ps()) }
    }

    #[inline(always)]
    fn ones() -> Self {
        unsafe { Self(_mm256_set1_ps(1.0)) }
    }

    #[inline(always)]
    fn splat(value: f32) -> Self {
        unsafe { Self(_mm256_set1_ps(value)) }
    }

    #[inline(always)]
    unsafe fn load(ptr: *const f32) -> Self {
        Self(_mm256_loadu_ps(ptr))
    }

    #[inline(always)]
    unsafe fn store(self, ptr: *mut f32) {
        _mm256_storeu_ps(ptr, self.0)
    }

    #[inline(always)]
    fn add(self, other: Self) -> Self {
        unsafe { Self(_mm256_add_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn sub(self, other: Self) -> Self {
        unsafe { Self(_mm256_sub_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn mul(self, other: Self) -> Self {
        unsafe { Self(_mm256_mul_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn div(self, other: Self) -> Self {
        unsafe { Self(_mm256_div_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn max(self, other: Self) -> Self {
        unsafe { Self(_mm256_max_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn min(self, other: Self) -> Self {
        unsafe { Self(_mm256_min_ps(self.0, other.0)) }
    }
}

/// AVX-512向量实现（f32 x 16）
#[cfg(target_arch = "x86_64")]
#[cfg(target_feature = "avx512f")]
#[derive(Clone, Copy)]
pub struct Avx512F32Vec(__m512);

#[cfg(target_arch = "x86_64")]
#[cfg(target_feature = "avx512f")]
impl SimdVector for Avx512F32Vec {
    type Element = f32;
    const LENGTH: usize = 16;

    #[inline(always)]
    fn zeros() -> Self {
        unsafe { Self(_mm512_setzero_ps()) }
    }

    #[inline(always)]
    fn ones() -> Self {
        unsafe { Self(_mm512_set1_ps(1.0)) }
    }

    #[inline(always)]
    fn splat(value: f32) -> Self {
        unsafe { Self(_mm512_set1_ps(value)) }
    }

    #[inline(always)]
    unsafe fn load(ptr: *const f32) -> Self {
        Self(_mm512_loadu_ps(ptr))
    }

    #[inline(always)]
    unsafe fn store(self, ptr: *mut f32) {
        _mm512_storeu_ps(ptr, self.0)
    }

    #[inline(always)]
    fn add(self, other: Self) -> Self {
        unsafe { Self(_mm512_add_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn sub(self, other: Self) -> Self {
        unsafe { Self(_mm512_sub_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn mul(self, other: Self) -> Self {
        unsafe { Self(_mm512_mul_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn div(self, other: Self) -> Self {
        unsafe { Self(_mm512_div_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn max(self, other: Self) -> Self {
        unsafe { Self(_mm512_max_ps(self.0, other.0)) }
    }

    #[inline(always)]
    fn min(self, other: Self) -> Self {
        unsafe { Self(_mm512_min_ps(self.0, other.0)) }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simd_features_detection() {
        let features = SimdFeatures::detect();
        println!("Detected SIMD features: {:?}", features);
    }

    #[test]
    #[cfg(target_arch = "x86_64")]
    fn test_avx2_operations() {
        if !is_x86_feature_detected!("avx2") {
            println!("AVX2 not supported, skipping test");
            return;
        }

        unsafe {
            let mut data = vec![1.0f32; 8];
            let vec1 = Avx2F32Vec::load(data.as_ptr());
            let vec2 = Avx2F32Vec::splat(2.0);
            let result = vec1.mul(vec2);
            
            let mut output = vec![0.0f32; 8];
            result.store(output.as_mut_ptr());
            
            assert!(output.iter().all(|&x| (x - 2.0).abs() < 1e-6));
        }
    }

    #[test]
    #[cfg(all(target_arch = "x86_64", target_feature = "avx512f"))]
    fn test_avx512_operations() {
        if !is_x86_feature_detected!("avx512f") {
            println!("AVX-512F not supported, skipping test");
            return;
        }

        unsafe {
            let mut data = vec![1.0f32; 16];
            let vec1 = Avx512F32Vec::load(data.as_ptr());
            let vec2 = Avx512F32Vec::splat(2.0);
            let result = vec1.mul(vec2);
            
            let mut output = vec![0.0f32; 16];
            result.store(output.as_mut_ptr());
            
            assert!(output.iter().all(|&x| (x - 2.0).abs() < 1e-6));
        }
    }
}
