use super::features::{SimdVector, SimdFeatures, SimdCapabilities};
use std::marker::PhantomData;

/// SIMD操作类型
#[derive(Debug, <PERSON>lone, Copy)]
pub enum SimdOp {
    /// 加法
    Add,
    /// 减法
    Sub,
    /// 乘法
    Mul,
    /// 除法
    Div,
    /// 最大值
    Max,
    /// 最小值
    Min,
}

/// SIMD执行器特征
pub trait SimdExecutor<T> {
    /// 执行SIMD操作
    fn execute(&self, op: SimdOp, a: &[T], b: &[T], out: &mut [T]);
    /// 获取向量长度
    fn vector_length(&self) -> usize;
    /// 检查是否支持当前操作
    fn supports_op(&self, op: SimdOp) -> bool;
}

/// 通用SIMD执行器
pub struct GenericSimdExecutor<V: SimdVector> {
    _phantom: PhantomData<V>,
}

impl<V: SimdVector> GenericSimdExecutor<V> {
    /// 创建新的执行器实例
    pub fn new() -> Self {
        Self {
            _phantom: PhantomData,
        }
    }
}

impl<V: SimdVector> SimdExecutor<V::Element> for GenericSimdExecutor<V>
where
    V::Element: std::ops::Add<Output = V::Element>
        + std::ops::Sub<Output = V::Element>
        + std::ops::Mul<Output = V::Element>
        + std::ops::Div<Output = V::Element>
        + PartialOrd
        + Copy
        + PartialEq,
{
    fn execute(&self, op: SimdOp, a: &[V::Element], b: &[V::Element], out: &mut [V::Element]) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), out.len());

        let chunks = a.len() / V::LENGTH;
        
        for i in 0..chunks {
            let start = i * V::LENGTH;
            unsafe {
                let va = V::load(a[start..].as_ptr());
                let vb = V::load(b[start..].as_ptr());
                
                let result = match op {
                    SimdOp::Add => va.add(vb),
                    SimdOp::Sub => va.sub(vb),
                    SimdOp::Mul => va.mul(vb),
                    SimdOp::Div => va.div(vb),
                    SimdOp::Max => va.max(vb),
                    SimdOp::Min => va.min(vb),
                };
                
                result.store(&mut out[start] as *mut V::Element);
            }
        }

        // 处理剩余元素
        let remaining = a.len() % V::LENGTH;
        if remaining > 0 {
            let start = a.len() - remaining;
            for i in 0..remaining {
                out[start + i] = match op {
                    SimdOp::Add => a[start + i] + b[start + i],
                    SimdOp::Sub => a[start + i] - b[start + i],
                    SimdOp::Mul => a[start + i] * b[start + i],
                    SimdOp::Div => a[start + i] / b[start + i],
                    SimdOp::Max => if a[start + i] > b[start + i] { a[start + i] } else { b[start + i] },
                    SimdOp::Min => if a[start + i] < b[start + i] { a[start + i] } else { b[start + i] },
                };
            }
        }
    }

    fn vector_length(&self) -> usize {
        V::LENGTH
    }

    fn supports_op(&self, _op: SimdOp) -> bool {
        true // 基础操作都支持
    }
}

/// SIMD操作分发器
pub struct SimdDispatcher {
    features: SimdFeatures,
}

impl SimdDispatcher {
    /// 创建新的分发器实例
    pub fn new() -> Self {
        Self {
            features: SimdCapabilities::get().features(),
        }
    }

    /// 获取f32类型的最优执行器
    pub fn get_f32_executor(&self) -> Box<dyn SimdExecutor<f32>> {
        #[cfg(target_arch = "x86_64")]
        {
            if self.features.has_avx2 {
                // 返回AVX2执行器
                Box::new(GenericSimdExecutor::<super::features::Avx2F32Vec>::new())
            } else {
                // 返回标量执行器
                Box::new(ScalarExecutor::new())
            }
        }

        #[cfg(not(target_arch = "x86_64"))]
        {
            // 返回标量执行器
            Box::new(ScalarExecutor::new())
        }
    }

    /// 获取泛型类型的标量执行器
    pub fn get_scalar_executor<T>(&self) -> Box<dyn SimdExecutor<T>>
    where
        T: Copy + std::ops::Add<Output = T> + std::ops::Sub<Output = T> + 
           std::ops::Mul<Output = T> + std::ops::Div<Output = T> + PartialOrd,
    {
        Box::new(ScalarExecutor::new())
    }
}

/// 标量执行器（fallback）
pub struct ScalarExecutor;

impl ScalarExecutor {
    pub fn new() -> Self {
        Self
    }
}

impl<T: Copy + std::ops::Add<Output = T> + std::ops::Sub<Output = T> + 
        std::ops::Mul<Output = T> + std::ops::Div<Output = T> + 
        PartialOrd> SimdExecutor<T> for ScalarExecutor {
    fn execute(&self, op: SimdOp, a: &[T], b: &[T], out: &mut [T]) {
        assert_eq!(a.len(), b.len());
        assert_eq!(a.len(), out.len());

        for i in 0..a.len() {
            out[i] = match op {
                SimdOp::Add => a[i] + b[i],
                SimdOp::Sub => a[i] - b[i],
                SimdOp::Mul => a[i] * b[i],
                SimdOp::Div => a[i] / b[i],
                SimdOp::Max => if a[i] > b[i] { a[i] } else { b[i] },
                SimdOp::Min => if a[i] < b[i] { a[i] } else { b[i] },
            };
        }
    }

    fn vector_length(&self) -> usize {
        1
    }

    fn supports_op(&self, _op: SimdOp) -> bool {
        true
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simd_dispatcher() {
        let dispatcher = SimdDispatcher::new();
        let executor = dispatcher.get_f32_executor();
        
        let a = vec![1.0f32; 1024];
        let b = vec![2.0f32; 1024];
        let mut c = vec![0.0f32; 1024];
        
        executor.execute(SimdOp::Add, &a, &b, &mut c);
        assert!(c.iter().all(|&x| (x - 3.0).abs() < 1e-6));
        
        executor.execute(SimdOp::Mul, &a, &b, &mut c);
        assert!(c.iter().all(|&x| (x - 2.0).abs() < 1e-6));
    }

    #[test]
    fn test_scalar_executor() {
        let executor = ScalarExecutor::new();
        
        let a = vec![1.0f32; 100];
        let b = vec![2.0f32; 100];
        let mut c = vec![0.0f32; 100];
        
        executor.execute(SimdOp::Add, &a, &b, &mut c);
        assert!(c.iter().all(|&x| (x - 3.0).abs() < 1e-6));
    }

    #[test]
    fn test_simd_operations() {
        let dispatcher = SimdDispatcher::new();
        let executor = dispatcher.get_f32_executor();
        
        let a = vec![1.0f32; 1024];
        let b = vec![2.0f32; 1024];
        let mut c = vec![0.0f32; 1024];
        
        // 测试所有操作
        for op in &[SimdOp::Add, SimdOp::Sub, SimdOp::Mul, SimdOp::Div, SimdOp::Max, SimdOp::Min] {
            executor.execute(*op, &a, &b, &mut c);
            
            // 验证结果
            match op {
                SimdOp::Add => assert!(c.iter().all(|&x| (x - 3.0).abs() < 1e-6)),
                SimdOp::Sub => assert!(c.iter().all(|&x| (x + 1.0).abs() < 1e-6)),
                SimdOp::Mul => assert!(c.iter().all(|&x| (x - 2.0).abs() < 1e-6)),
                SimdOp::Div => assert!(c.iter().all(|&x| (x - 0.5).abs() < 1e-6)),
                SimdOp::Max => assert!(c.iter().all(|&x| (x - 2.0).abs() < 1e-6)),
                SimdOp::Min => assert!(c.iter().all(|&x| (x - 1.0).abs() < 1e-6)),
            }
        }
    }
}
