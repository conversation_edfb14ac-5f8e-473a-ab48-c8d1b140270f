use std::ops::{Add, Sub, Mul, Div};

impl<T> RustArray<T>
where
    T: Add<Output = T> + Copy,
{
    /// 执行数组加法
    pub fn add(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape != other.shape {
            return Err(RustNumError::ShapeMismatch {
                expected: self.shape.clone(),
                got: other.shape.clone(),
            });
        }

        let mut result = Self::new(self.shape.clone())?;
        
        // 获取数据指针
        let self_ptr = self.data.as_ptr();
        let other_ptr = other.data.as_ptr();
        let result_ptr = result.data.as_ptr();

        // 如果数据是连续的，我们可以使用优化的实现
        if self.is_contiguous() && other.is_contiguous() {
            unsafe {
                for i in 0..self.len() {
                    *result_ptr.add(i) = *self_ptr.add(i) + *other_ptr.add(i);
                }
            }
        } else {
            // 处理非连续情况
            // TODO: 实现通用情况的计算
        }

        Ok(result)
    }
}

// 为数组实现加法运算符
impl<T: Add<Output = T> + Copy> Add for &RustArray<T> {
    type Output = RustArray<T>;

    fn add(self, other: Self) -> Self::Output {
        self.add(other).unwrap()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_array_add() {
        let arr1 = RustArray::<f64>::new(vec![2, 2]).unwrap();
        let arr2 = RustArray::<f64>::new(vec![2, 2]).unwrap();
        let result = arr1.add(&arr2).unwrap();
        assert_eq!(result.shape(), &[2, 2]);
    }
}
