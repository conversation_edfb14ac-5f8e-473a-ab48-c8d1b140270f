//! faer-rs 线性代数库集成模块
//! 
//! 本模块提供了 faer-rs 高性能线性代数库的集成接口，
//! 为 RustNum 项目提供专业级的矩阵运算和线性代数功能。

#[cfg(feature = "faer-comparison")]
use faer::{Mat, prelude::*};
#[cfg(feature = "faer-comparison")]
use faer::linalg::solvers::SpSolver;
#[cfg(feature = "faer-comparison")]
use std::time::Instant;

use crate::error::RustNumError;
use crate::array::Array2D;

/// faer-rs 矩阵包装器
/// 
/// 提供与 RustNum Array2D 的互操作性
#[cfg(feature = "faer-comparison")]
pub struct FaerMatrix {
    inner: Mat<f64>,
}

#[cfg(feature = "faer-comparison")]
impl FaerMatrix {
    /// 从 RustNum Array2D 创建 FaerMatrix
    pub fn from_array2d(array: &Array2D<f64>) -> Result<Self, RustNumError> {
        let (rows, cols) = array.shape();
        let data = array.data();
        
        let mat = Mat::from_fn(rows, cols, |i, j| {
            data[i * cols + j]
        });
        
        Ok(FaerMatrix { inner: mat })
    }
    
    /// 转换为 RustNum Array2D
    pub fn to_array2d(&self) -> Result<Array2D<f64>, RustNumError> {
        let rows = self.inner.nrows();
        let cols = self.inner.ncols();
        let mut data = Vec::with_capacity(rows * cols);
        
        for i in 0..rows {
            for j in 0..cols {
                data.push(self.inner[(i, j)]);
            }
        }
        
        Array2D::from_vec(data, rows, cols)
    }
    
    /// 获取内部 faer 矩阵的引用
    pub fn as_faer(&self) -> &Mat<f64> {
        &self.inner
    }
    
    /// 获取内部 faer 矩阵的可变引用
    pub fn as_faer_mut(&mut self) -> &mut Mat<f64> {
        &mut self.inner
    }
    
    /// 创建指定大小的零矩阵
    pub fn zeros(rows: usize, cols: usize) -> Self {
        let mat = Mat::zeros(rows, cols);
        FaerMatrix { inner: mat }
    }
    
    /// 创建单位矩阵
    pub fn identity(size: usize) -> Self {
        let mat = Mat::identity(size, size);
        FaerMatrix { inner: mat }
    }
    
    /// 矩阵乘法
    pub fn matmul(&self, other: &FaerMatrix) -> Result<FaerMatrix, RustNumError> {
        if self.inner.ncols() != other.inner.nrows() {
            return Err(RustNumError::DimensionMismatch {
                expected: format!("{}x{}", other.inner.nrows(), other.inner.nrows()),
                actual: format!("{}x{} * {}x{}", 
                    self.inner.nrows(), self.inner.ncols(),

                    other.inner.nrows(), other.inner.ncols()),
            });
        }
        
        let result = &self.inner * &other.inner;
        Ok(FaerMatrix { inner: result })
    }
    
    /// LU 分解
    pub fn lu_decomposition(&self) -> Result<FaerLuDecomposition, RustNumError> {
        if self.inner.nrows() != self.inner.ncols() {
            return Err(RustNumError::InvalidOperation(
                "LU decomposition requires square matrix".to_string()
            ));
        }
        
        let lu = self.inner.partial_piv_lu();
        Ok(FaerLuDecomposition { inner: lu })
    }
    
    /// QR 分解
    pub fn qr_decomposition(&self) -> Result<FaerQrDecomposition, RustNumError> {
        let qr = self.inner.qr();
        Ok(FaerQrDecomposition { inner: qr })
    }
    
    /// 获取矩阵维度
    pub fn shape(&self) -> (usize, usize) {
        (self.inner.nrows(), self.inner.ncols())
    }
}

/// faer-rs LU 分解结果
#[cfg(feature = "faer-comparison")]
pub struct FaerLuDecomposition {
    inner: faer::linalg::lu::partial_pivoting::PartialPivLu<f64>,
}

#[cfg(feature = "faer-comparison")]
impl FaerLuDecomposition {
    /// 求解线性方程组 Ax = b
    pub fn solve(&self, b: &FaerMatrix) -> Result<FaerMatrix, RustNumError> {
        let solution = self.inner.solve(&b.inner);
        Ok(FaerMatrix { inner: solution })
    }
    
    /// 计算行列式
    pub fn determinant(&self) -> f64 {
        self.inner.determinant()
    }
}

/// faer-rs QR 分解结果
#[cfg(feature = "faer-comparison")]
pub struct FaerQrDecomposition {
    inner: faer::linalg::qr::no_pivoting::Qr<f64>,
}

#[cfg(feature = "faer-comparison")]
impl FaerQrDecomposition {
    /// 求解最小二乘问题
    pub fn solve_least_squares(&self, b: &FaerMatrix) -> Result<FaerMatrix, RustNumError> {
        let solution = self.inner.solve(&b.inner);
        Ok(FaerMatrix { inner: solution })
    }
    
    /// 获取 Q 矩阵
    pub fn q_matrix(&self) -> FaerMatrix {
        let q = self.inner.compute_q();
        FaerMatrix { inner: q }
    }
    
    /// 获取 R 矩阵
    pub fn r_matrix(&self) -> FaerMatrix {
        let r = self.inner.compute_r();
        FaerMatrix { inner: r }
    }
}

/// 性能基准测试结果
#[cfg(feature = "faer-comparison")]
#[derive(Debug, Clone)]
pub struct BenchmarkResult {
    pub operation: String,
    pub matrix_size: usize,
    pub faer_time_ms: f64,
    pub rustnum_time_ms: Option<f64>,
    pub speedup_factor: Option<f64>,
}

/// faer-rs 性能基准测试套件
#[cfg(feature = "faer-comparison")]
pub struct FaerBenchmarkSuite {
    matrix_sizes: Vec<usize>,
    iterations: usize,
}

#[cfg(feature = "faer-comparison")]
impl FaerBenchmarkSuite {
    /// 创建新的基准测试套件
    pub fn new(matrix_sizes: Vec<usize>, iterations: usize) -> Self {
        Self {
            matrix_sizes,
            iterations,
        }
    }
    
    /// 运行矩阵乘法基准测试
    pub fn benchmark_matrix_multiplication(&self) -> Vec<BenchmarkResult> {
        let mut results = Vec::new();
        
        for &size in &self.matrix_sizes {
            // 创建测试矩阵
            let a = FaerMatrix::zeros(size, size);
            let b = FaerMatrix::zeros(size, size);
            
            // 预热
            for _ in 0..3 {
                let _ = a.matmul(&b);
            }
            
            // 测试 faer-rs 性能
            let start = Instant::now();
            for _ in 0..self.iterations {
                let _ = a.matmul(&b);
            }
            let faer_time = start.elapsed().as_secs_f64() * 1000.0 / self.iterations as f64;
            
            results.push(BenchmarkResult {
                operation: "Matrix Multiplication".to_string(),
                matrix_size: size,
                faer_time_ms: faer_time,
                rustnum_time_ms: None,
                speedup_factor: None,
            });
        }
        
        results
    }
    
    /// 运行 LU 分解基准测试
    pub fn benchmark_lu_decomposition(&self) -> Vec<BenchmarkResult> {
        let mut results = Vec::new();
        
        for &size in &self.matrix_sizes {
            // 创建测试矩阵（单位矩阵确保数值稳定性）
            let matrix = FaerMatrix::identity(size);
            
            // 预热
            for _ in 0..3 {
                let _ = matrix.lu_decomposition();
            }
            
            // 测试 faer-rs 性能
            let start = Instant::now();
            for _ in 0..self.iterations {
                let _ = matrix.lu_decomposition();
            }
            let faer_time = start.elapsed().as_secs_f64() * 1000.0 / self.iterations as f64;
            
            results.push(BenchmarkResult {
                operation: "LU Decomposition".to_string(),
                matrix_size: size,
                faer_time_ms: faer_time,
                rustnum_time_ms: None,
                speedup_factor: None,
            });
        }
        
        results
    }
    
    /// 运行完整的基准测试套件
    pub fn run_all_benchmarks(&self) -> Vec<BenchmarkResult> {
        let mut all_results = Vec::new();
        
        all_results.extend(self.benchmark_matrix_multiplication());
        all_results.extend(self.benchmark_lu_decomposition());
        
        all_results
    }
}

/// 便捷函数：运行快速性能测试
#[cfg(feature = "faer-comparison")]
pub fn quick_performance_test() -> Vec<BenchmarkResult> {
    let suite = FaerBenchmarkSuite::new(vec![100, 500, 1000], 5);
    suite.run_all_benchmarks()
}

/// 便捷函数：打印基准测试结果
#[cfg(feature = "faer-comparison")]
pub fn print_benchmark_results(results: &[BenchmarkResult]) {
    println!("\n=== faer-rs 性能基准测试结果 ===");
    println!("{:<20} {:<12} {:<15}", "操作类型", "矩阵大小", "faer时间(ms)");
    println!("{}", "-".repeat(50));
    
    for result in results {
        println!("{:<20} {:<12} {:<15.3}", 
            result.operation, 
            result.matrix_size, 
            result.faer_time_ms
        );
    }
}

#[cfg(not(feature = "faer-comparison"))]
compile_error!("faer-comparison feature is required for this module");

#[cfg(test)]
#[cfg(feature = "faer-comparison")]
mod tests {
    use super::*;
    
    #[test]
    fn test_faer_matrix_creation() {
        let matrix = FaerMatrix::zeros(3, 3);
        assert_eq!(matrix.shape(), (3, 3));
    }
    
    #[test]
    fn test_matrix_multiplication() {
        let a = FaerMatrix::identity(3);
        let b = FaerMatrix::identity(3);
        let result = a.matmul(&b).unwrap();
        assert_eq!(result.shape(), (3, 3));
    }
    
    #[test]
    fn test_lu_decomposition() {
        let matrix = FaerMatrix::identity(3);
        let lu = matrix.lu_decomposition().unwrap();
        assert_eq!(lu.determinant(), 1.0);
    }
    
    #[test]
    fn test_benchmark_suite() {
        let suite = FaerBenchmarkSuite::new(vec![10, 20], 2);
        let results = suite.benchmark_matrix_multiplication();
        assert_eq!(results.len(), 2);
    }
}