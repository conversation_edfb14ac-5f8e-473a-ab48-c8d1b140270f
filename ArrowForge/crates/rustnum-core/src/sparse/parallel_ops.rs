use crate::sparse::storage::CsrMatrix;
use crate::error::RustNumError;
use crate::parallel::ParallelManager;

#[cfg(feature = "parallel")]
use rayon::prelude::*;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};

/// 并行稀疏矩阵运算特征
pub trait ParallelSparseOps<T> {
    /// 并行稀疏矩阵-向量乘法
    fn parallel_mv_mul(&self, x: &[T]) -> Result<Vec<T>, RustNumError>;
    
    /// 并行稀疏矩阵-矩阵乘法
    fn parallel_matmul(&self, other: &Self) -> Result<Self, RustNumError>
    where
        Self: Sized;
        
    /// 并行稀疏矩阵转置
    fn parallel_transpose(&self) -> Self;
}

impl ParallelSparseOps<f64> for CsrMatrix<f64> {
    fn parallel_mv_mul(&self, x: &[f64]) -> Result<Vec<f64>, RustNumError> {
        if x.len() != self.shape.1 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.1],
                got: vec![x.len()],
            });
        }
        
        if !ParallelManager::should_parallelize(self.shape.0) {
            return self.mv_mul(x);
        }
        
        let chunk_size = (self.shape.0 / ParallelManager::get_config().num_threads)
            .max(ParallelManager::get_config().min_parallel_size);
        
        #[cfg(feature = "parallel")]
        let result = ParallelManager::execute(|| {
            (0..self.shape.0)
                .into_par_iter()
                .step_by(chunk_size)
                .map(|i| {
                    let end = (i + chunk_size).min(self.shape.0);
                    let mut local_result = vec![0.0; end - i];
                    
                    for row in i..end {
                        let start = self.row_ptrs[row];
                        let end = self.row_ptrs[row + 1];
                        
                        for k in start..end {
                            let col = self.col_indices[k];
                            local_result[row - i] += self.values[k] * x[col];
                        }
                    }
                    
                    (i, local_result)
                })
                .collect::<Vec<_>>()
        });
        
        #[cfg(not(feature = "parallel"))]
        let result = {
            let mut results = Vec::new();
            for i in (0..self.shape.0).step_by(chunk_size) {
                let end = (i + chunk_size).min(self.shape.0);
                let mut local_result = vec![0.0; end - i];
                
                for row in i..end {
                    let start = self.row_ptrs[row];
                    let end = self.row_ptrs[row + 1];
                    
                    for k in start..end {
                        let col = self.col_indices[k];
                        local_result[row - i] += self.values[k] * x[col];
                    }
                }
                
                results.push((i, local_result));
            }
            results
        };
        
        // 合并结果
        let mut y = vec![0.0; self.shape.0];
        for (start, local_result) in result {
            for (i, &val) in local_result.iter().enumerate() {
                y[start + i] = val;
            }
        }
        
        Ok(y)
    }
    
    fn parallel_matmul(&self, other: &Self) -> Result<Self, RustNumError> {
        if self.shape.1 != other.shape.0 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.1],
                got: vec![other.shape.0],
            });
        }
        
        if !ParallelManager::should_parallelize(self.shape.0) {
            return self.mul(other);
        }
        
        // 将B转置为CSC格式以提高访问效率
        let other_csc = other.to_csc();
        
        let chunk_size = (self.shape.0 / ParallelManager::get_config().num_threads)
            .max(ParallelManager::get_config().min_parallel_size);
        
        #[cfg(feature = "parallel")]
        let result = ParallelManager::execute(|| {
            (0..self.shape.0)
                .into_par_iter()
                .step_by(chunk_size)
                .map(|i| {
                    let end = (i + chunk_size).min(self.shape.0);
                    let mut local_values = Vec::new();
                    let mut local_col_indices = Vec::new();
                    let mut local_row_ptrs = vec![0];
                    
                    for row in i..end {
                        let mut row_values = std::collections::HashMap::new();
                        
                        // 计算第row行与B的乘积
                        for k in self.row_ptrs[row]..self.row_ptrs[row + 1] {
                            let a_val = self.values[k];
                            let col = self.col_indices[k];
                            
                            // 与B的第col行相乘
                            for b_k in other_csc.col_ptrs[col]..other_csc.col_ptrs[col + 1] {
                                let b_val = other_csc.values[b_k];
                                let b_row = other_csc.row_indices[b_k];
                                
                                *row_values.entry(b_row).or_insert(0.0) += a_val * b_val;
                            }
                        }
                        
                        // 收集非零元素
                        let mut col_val: Vec<_> = row_values.into_iter().collect();
                        col_val.sort_by_key(|&(col, _)| col);
                        
                        for (col, val) in col_val {
                            if val.abs() > 1e-10 {
                                local_values.push(val);
                                local_col_indices.push(col);
                            }
                        }
                        
                        local_row_ptrs.push(local_values.len());
                    }
                    
                    (i, local_values, local_col_indices, local_row_ptrs)
                })
                .collect::<Vec<_>>()
        });
        
        #[cfg(not(feature = "parallel"))]
        let result = {
            let mut results = Vec::new();
            for i in (0..self.shape.0).step_by(chunk_size) {
                let end = (i + chunk_size).min(self.shape.0);
                let mut local_values = Vec::new();
                let mut local_col_indices = Vec::new();
                let mut local_row_ptrs = vec![0];
                
                for row in i..end {
                    let mut row_values = std::collections::HashMap::new();
                    
                    // 计算第row行与B的乘积
                    for k in self.row_ptrs[row]..self.row_ptrs[row + 1] {
                        let a_val = self.values[k];
                        let col = self.col_indices[k];
                        
                        // 与B的第col行相乘
                        for b_k in other_csc.col_ptrs[col]..other_csc.col_ptrs[col + 1] {
                            let b_val = other_csc.values[b_k];
                            let b_row = other_csc.row_indices[b_k];
                            
                            *row_values.entry(b_row).or_insert(0.0) += a_val * b_val;
                        }
                    }
                    
                    // 收集非零元素
                    let mut col_val: Vec<_> = row_values.into_iter().collect();
                    col_val.sort_by_key(|&(col, _)| col);
                    
                    for (col, val) in col_val {
                        if val.abs() > 1e-10 {
                            local_values.push(val);
                            local_col_indices.push(col);
                        }
                    }
                    
                    local_row_ptrs.push(local_values.len());
                }
                
                results.push((i, local_values, local_col_indices, local_row_ptrs));
            }
            results
        };
        
        // 合并结果
        let mut values = Vec::new();
        let mut col_indices = Vec::new();
        let mut row_ptrs = vec![0];
        
        for (_, local_values, local_col_indices, local_row_ptrs) in result {
            let offset = values.len();
            values.extend(local_values);
            col_indices.extend(local_col_indices);
            
            for ptr in local_row_ptrs.iter().skip(1) {
                row_ptrs.push(ptr + offset);
            }
        }
        
        Ok(CsrMatrix::new(
            values,
            col_indices,
            row_ptrs,
            (self.shape.0, other.shape.1)
        ))
    }
    
    fn parallel_transpose(&self) -> Self {
        if !ParallelManager::should_parallelize(self.nnz()) {
            let csc = self.to_csc();
            return CsrMatrix::new(
                csc.values,
                csc.row_indices,
                csc.col_ptrs,
                (self.shape.1, self.shape.0)
            );
        }
        
        // 并行计算列计数
        #[cfg(feature = "parallel")]
        let col_counts = ParallelManager::execute(|| {
            self.col_indices.par_iter()
                .fold(
                    || vec![0; self.shape.1],
                    |mut counts, &col| {
                        counts[col] += 1;
                        counts
                    }
                )
                .reduce(
                    || vec![0; self.shape.1],
                    |mut a, b| {
                        for (i, &count) in b.iter().enumerate() {
                            a[i] += count;
                        }
                        a
                    }
                )
        });
        
        #[cfg(not(feature = "parallel"))]
        let col_counts = {
            let mut counts = vec![0; self.shape.1];
            for &col in &self.col_indices {
                counts[col] += 1;
            }
            counts
        };

        
        // 计算列指针
        let mut col_ptrs = vec![0; self.shape.1 + 1];
        for (i, &count) in col_counts.iter().enumerate() {
            col_ptrs[i + 1] = col_ptrs[i] + count;
        }
        
        // 分配结果数组
        let values = Arc::new(Mutex::new(vec![0.0; self.nnz()]));
        let row_indices = Arc::new(Mutex::new(vec![0; self.nnz()]));
        let work_ptrs: Vec<AtomicUsize> = col_ptrs.iter().map(|&x| AtomicUsize::new(x)).collect();
        
        // 并行填充转置矩阵
        #[cfg(feature = "parallel")]
        ParallelManager::execute(|| {
            (0..self.shape.0).into_par_iter().for_each(|row| {
                for k in self.row_ptrs[row]..self.row_ptrs[row + 1] {
                    let col = self.col_indices[k];
                    let pos = work_ptrs[col].fetch_add(1, Ordering::SeqCst);
                    values.lock().unwrap()[pos] = self.values[k];
                    row_indices.lock().unwrap()[pos] = row;
                }
            });
        });
        
        #[cfg(not(feature = "parallel"))]
        {
            for row in 0..self.shape.0 {
                for k in self.row_ptrs[row]..self.row_ptrs[row + 1] {
                    let col = self.col_indices[k];
                    let pos = work_ptrs[col].fetch_add(1, Ordering::SeqCst);
                    values.lock().unwrap()[pos] = self.values[k];
                    row_indices.lock().unwrap()[pos] = row;
                }
            }
        }
        
        let final_values = Arc::try_unwrap(values).unwrap().into_inner().unwrap();
        let final_row_indices = Arc::try_unwrap(row_indices).unwrap().into_inner().unwrap();
        
        CsrMatrix::new(
            final_values,
            final_row_indices,
            col_ptrs,
            (self.shape.1, self.shape.0)
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_parallel_mv_mul() {
        let matrix = CsrMatrix::new(
            vec![1.0, 2.0, 3.0, 4.0],
            vec![0, 1, 1, 2],
            vec![0, 2, 3, 4],
            (3, 3)
        );
        
        let x = vec![1.0, 2.0, 3.0];
        let y = matrix.parallel_mv_mul(&x).unwrap();
        
        assert_eq!(y, vec![5.0, 6.0, 12.0]);
    }
    
    #[test]
    fn test_parallel_matmul() {
        let a = CsrMatrix::new(
            vec![1.0, 2.0, 3.0],
            vec![0, 1, 1],
            vec![0, 2, 3],
            (2, 2)
        );
        
        let b = CsrMatrix::new(
            vec![1.0, 2.0, 3.0],
            vec![0, 1, 1],
            vec![0, 2, 3],
            (2, 2)
        );
        
        let c = a.parallel_matmul(&b).unwrap();
        
        let expected = CsrMatrix::new(
            vec![1.0, 8.0, 9.0],
            vec![0, 1, 1],
            vec![0, 2, 3],
            (2, 2)
        );
        
        assert_eq!(c.values, expected.values);
        assert_eq!(c.col_indices, expected.col_indices);
        assert_eq!(c.row_ptrs, expected.row_ptrs);
    }
    
    #[test]
    fn test_parallel_transpose() {
        let matrix = CsrMatrix::new(
            vec![1.0, 2.0, 3.0, 4.0],
            vec![0, 1, 1, 2],
            vec![0, 2, 3, 4],
            (3, 3)
        );
        
        let transposed = matrix.parallel_transpose();
        
        // 验证转置操作成功执行
        assert_eq!((transposed.rows(), transposed.cols()), (3, 3));
        assert_eq!(transposed.values.len(), 4);
        println!("Transpose operation completed successfully");
    }
}
