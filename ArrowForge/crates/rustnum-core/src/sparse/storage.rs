use std::ops::{Add, Mul};
use crate::error::RustNumError;
use crate::array::{RustArray, StorageOrder};
use crate::memory::MemoryPool;
use std::sync::Arc;
use parking_lot::RwLock;

/// 压缩行存储(CSR)格式
#[derive(Clone, Debug)]
pub struct CsrMatrix<T> {
    /// 非零元素值
    pub values: Vec<T>,
    /// 列索引
    pub col_indices: Vec<usize>,
    /// 行指针
    pub row_ptrs: Vec<usize>,
    /// 矩阵维度
    pub shape: (usize, usize),
}

/// 压缩列存储(CSC)格式
#[derive(Clone, Debug)]
pub struct CscMatrix<T> {
    /// 非零元素值
    pub values: Vec<T>,
    /// 行索引
    pub row_indices: Vec<usize>,
    /// 列指针
    pub col_ptrs: Vec<usize>,
    /// 矩阵维度
    pub shape: (usize, usize),
}

/// 坐标格式(COO)
#[derive(Clone, Debug)]
pub struct CooMatrix<T> {
    /// 非零元素值
    pub values: Vec<T>,
    /// 行索引
    pub row_indices: Vec<usize>,
    /// 列索引
    pub col_indices: Vec<usize>,
    /// 矩阵维度
    pub shape: (usize, usize),
}

impl<T: Copy + Default + std::ops::Add<Output = T> + std::ops::Mul<Output = T> + num_traits::Float + num_traits::Num + num_traits::NumOps + num_traits::One + num_traits::Zero + num_traits::Signed + num_traits::FromPrimitive + num_traits::ToPrimitive> CsrMatrix<T> {
    /// 创建单位矩阵
    pub fn identity(size: usize) -> Self {
        let mut values = Vec::with_capacity(size);
        let mut col_indices = Vec::with_capacity(size);
        let mut row_ptrs = vec![0; size + 1];

        for i in 0..size {
            values.push(T::one());
            col_indices.push(i);
            row_ptrs[i + 1] = i + 1;
        }

        Self {
            values,
            col_indices,
            row_ptrs,
            shape: (size, size),
        }
    }

    /// 创建新的CSR矩阵
    pub fn new(values: Vec<T>, col_indices: Vec<usize>, row_ptrs: Vec<usize>, shape: (usize, usize)) -> Self {
        Self {
            values,
            col_indices,
            row_ptrs,
            shape,
        }
    }
    
    /// 创建指定容量的空CSR矩阵
    pub fn with_capacity(rows: usize, cols: usize, capacity: usize) -> Self {
        Self {
            values: Vec::with_capacity(capacity),
            col_indices: Vec::with_capacity(capacity),
            row_ptrs: vec![0; rows + 1],
            shape: (rows, cols),
        }
    }
    
    /// 获取非零元素数量
    pub fn nonzeros(&self) -> usize {
        self.values.len()
    }
    
    /// 获取值数组的引用
    pub fn values(&self) -> &[T] {
        &self.values
    }
    
    /// 插入元素到指定位置
    pub fn insert(&mut self, _row: usize, col: usize, value: T) -> Result<(), crate::error::RustNumError> {
        // 简化实现：直接添加到末尾
        // 实际应用中需要维护CSR格式的有序性
        self.values.push(value);
        self.col_indices.push(col);
        Ok(())
    }
    
    /// 获取指定位置的元素
    pub fn get(&self, row: usize, col: usize) -> Option<T> {
        if row >= self.shape.0 || col >= self.shape.1 {
            return None;
        }
        
        for k in self.row_ptrs[row]..self.row_ptrs[row + 1] {
            if self.col_indices[k] == col {
                return Some(self.values[k]);
            }
        }
        None
    }
    
    /// 从密集矩阵创建CSR矩阵
    pub fn from_dense(dense: &RustArray<T>, threshold: T) -> Self 
    where
        T: PartialOrd,
    {
        let (m, n) = (dense.shape()[0], dense.shape()[1]);
        let mut values = Vec::new();
        let mut col_indices = Vec::new();
        let mut row_ptrs = vec![0];
        
        for i in 0..m {
            for j in 0..n {
                if let Ok(val) = dense.get(&[i, j]) {
                    if val > threshold || val < threshold {
                        values.push(val);
                        col_indices.push(j);
                    }
                }
            }
            row_ptrs.push(values.len());
        }
        
        Self {
            values,
            col_indices,
            row_ptrs,
            shape: (m, n),
        }
    }
    
    /// 转换为密集矩阵
    pub fn to_dense(&self) -> Result<RustArray<T>, RustNumError> 
     where
         T: Default + Clone,
     {
         let pool = Arc::new(RwLock::new(MemoryPool::new()));
         let mut dense = RustArray::new(vec![self.shape.0, self.shape.1], StorageOrder::RowMajor, pool)?;
         // 初始化为零值
         for i in 0..self.shape.0 {
             for j in 0..self.shape.1 {
                 dense.set(&[i, j], T::zero())?;
             }
         }
        
        for i in 0..self.shape.0 {
            let start = self.row_ptrs[i];
            let end = self.row_ptrs[i + 1];
            
            for k in start..end {
                let j = self.col_indices[k];
                dense.set(&[i, j], self.values[k])?;
            }
        }
        
        Ok(dense)
    }
    
    /// 获取矩阵行数
    pub fn rows(&self) -> usize {
        self.shape.0
    }
    
    /// 获取矩阵列数
    pub fn cols(&self) -> usize {
        self.shape.1
    }
    
    /// 获取指定列的行索引范围
    pub fn col_range(&self, _col: usize) -> std::ops::Range<usize> {
        // 对于CSR格式，需要遍历所有行来找到指定列的元素
        // 这里返回一个空范围，实际实现需要更复杂的逻辑
        0..0
    }
    
    /// 获取行索引数组的引用
    pub fn row_indices(&self) -> &[usize] {
        // CSR格式中没有直接的行索引数组，这里返回空切片
        &[]
    }
    
    /// 获取非零元素数量
    pub fn nnz(&self) -> usize {
        self.values.len()
    }
    
    /// 转换为CSC格式
    pub fn to_csc(&self) -> CscMatrix<T> {
        let nnz = self.nnz();
        let mut values = vec![T::default(); nnz];
        let mut row_indices = vec![0; nnz];
        let mut col_ptrs = vec![0; self.shape.1 + 1];
        
        // 计算每列非零元素个数
        for &col in &self.col_indices {
            col_ptrs[col + 1] += 1;
        }
        
        // 计算列指针
        for i in 1..=self.shape.1 {
            col_ptrs[i] += col_ptrs[i - 1];
        }
        
        // 填充数据
        let mut work_ptrs = col_ptrs.clone();
        for i in 0..self.shape.0 {
            for k in self.row_ptrs[i]..self.row_ptrs[i + 1] {
                let j = self.col_indices[k];
                let dest = work_ptrs[j];
                values[dest] = self.values[k];
                row_indices[dest] = i;
                work_ptrs[j] += 1;
            }
        }
        
        CscMatrix {
            values,
            row_indices,
            col_ptrs,
            shape: self.shape,
        }
    }
}

impl<T: Copy + Default + Add<Output = T> + Mul<Output = T> + num_traits::Float + num_traits::Num + num_traits::NumOps + num_traits::One + num_traits::Zero + num_traits::Signed + num_traits::FromPrimitive + num_traits::ToPrimitive> CsrMatrix<T> {
    /// 矩阵向量乘法
    pub fn mv_mul(&self, x: &[T]) -> Result<Vec<T>, RustNumError> {
        if x.len() != self.shape.1 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.1],
                got: vec![x.len()],
            });
        }
        
        let mut y = vec![T::default(); self.shape.0];
        
        for i in 0..self.shape.0 {
            for k in self.row_ptrs[i]..self.row_ptrs[i + 1] {
                let j = self.col_indices[k];
                y[i] = y[i] + self.values[k] * x[j];
            }
        }
        
        Ok(y)
    }
    
    /// 稀疏矩阵乘法
    pub fn mul(&self, other: &CsrMatrix<T>) -> Result<CsrMatrix<T>, RustNumError> {
        if self.shape.1 != other.shape.0 {
            return Err(RustNumError::ShapeMismatch {
                expected: vec![self.shape.1],
                got: vec![other.shape.0],
            });
        }
        
        let m = self.shape.0;
        let n = other.shape.1;
        let mut values = Vec::new();
        let mut col_indices = Vec::new();
        let mut row_ptrs = vec![0];
        
        // 使用临时向量存储中间结果
        let mut temp = vec![(T::default(), false); n];
        
        for i in 0..m {
            // 计算第i行的结果
            for k in self.row_ptrs[i]..self.row_ptrs[i + 1] {
                let a_val = self.values[k];
                let j = self.col_indices[k];
                
                // 与B的第j行相乘
                for p in other.row_ptrs[j]..other.row_ptrs[j + 1] {
                    let b_val = other.values[p];
                    let col = other.col_indices[p];
                    let entry = &mut temp[col];
                    
                    if !entry.1 {
                        *entry = (a_val * b_val, true);
                    } else {
                        entry.0 = entry.0 + a_val * b_val;
                    }
                }
            }
            
            // 收集非零元素
            for j in 0..n {
                if temp[j].1 {
                    values.push(temp[j].0);
                    col_indices.push(j);
                    temp[j] = (T::default(), false);
                }
            }
            
            row_ptrs.push(values.len());
        }
        
        Ok(CsrMatrix::new(values, col_indices, row_ptrs, (m, n)))
    }
}

impl<T: Copy + Default> CscMatrix<T> {
    /// 创建新的CSC矩阵
    pub fn new(values: Vec<T>, row_indices: Vec<usize>, col_ptrs: Vec<usize>, shape: (usize, usize)) -> Self {
        Self {
            values,
            row_indices,
            col_ptrs,
            shape,
        }
    }
    
    /// 创建指定容量的空CSC矩阵
    pub fn with_capacity(rows: usize, cols: usize, capacity: usize) -> Self {
        Self {
            values: Vec::with_capacity(capacity),
            row_indices: Vec::with_capacity(capacity),
            col_ptrs: vec![0; cols + 1],
            shape: (rows, cols),
        }
    }
    
    /// 插入元素到指定位置
    pub fn insert(&mut self, row: usize, _col: usize, value: T) -> Result<(), crate::error::RustNumError> {
        // 简化实现：直接添加到末尾
        // 实际应用中需要维护CSC格式的有序性
        self.values.push(value);
        self.row_indices.push(row);
        Ok(())
    }
    
    /// 创建单位矩阵
    pub fn identity(size: usize) -> Self 
    where 
        T: From<i32> + Copy + Default,
    {
        let mut values = Vec::with_capacity(size);
        let mut row_indices = Vec::with_capacity(size);
        let mut col_ptrs = Vec::with_capacity(size + 1);
        
        for i in 0..=size {
            col_ptrs.push(i);
        }
        
        for i in 0..size {
            values.push(T::from(1));
            row_indices.push(i);
        }
        
        Self {
            values,
            row_indices,
            col_ptrs,
            shape: (size, size),
        }
    }
    
    /// 获取指定位置的元素
    pub fn get(&self, row: usize, col: usize) -> Option<T> {
        if row >= self.shape.0 || col >= self.shape.1 {
            return None;
        }
        
        for k in self.col_ptrs[col]..self.col_ptrs[col + 1] {
            if self.row_indices[k] == row {
                return Some(self.values[k]);
            }
        }
        None
    }
    
    /// 获取行数
    pub fn rows(&self) -> usize {
        self.shape.0
    }
    
    /// 获取列数
    pub fn cols(&self) -> usize {
        self.shape.1
    }
}
