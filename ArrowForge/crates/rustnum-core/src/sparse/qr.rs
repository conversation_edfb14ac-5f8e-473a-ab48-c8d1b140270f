use super::storage::{CsrMatrix, CscMatrix};
use crate::error::RustNumError;
use crate::sparse::decomposition::SparseQR;

/// 稀疏Householder变换
struct HouseholderTransform {
    v: Vec<f64>,
    beta: f64,
}

impl HouseholderTransform {
    /// 计算Householder向量
    fn compute(x: &[f64]) -> Self {
        let mut v = x.to_vec();
        let norm = (v.iter().map(|&x| x * x).sum::<f64>()).sqrt();
        
        if norm == 0.0 {
            return Self {
                v,
                beta: 0.0,
            };
        }
        
        let alpha = -v[0].signum() * norm;
        v[0] -= alpha;
        let vnorm = (v.iter().map(|&x| x * x).sum::<f64>()).sqrt();
        
        if vnorm != 0.0 {
            for x in &mut v {
                *x /= vnorm;
            }
        }
        
        Self {
            v,
            beta: 2.0,
        }
    }
    
    /// 应用Householder变换到矩阵
    fn apply(&self, matrix: &mut CsrMatrix<f64>, start_row: usize, start_col: usize) -> Result<(), RustNumError> {
        let m = matrix.rows();
        let n = matrix.cols();
        
        // 对每一列应用变换
        for j in start_col..n {
            let mut sum = 0.0;
            
            // 计算内积
            for (i, &v_i) in self.v.iter().enumerate() {
                if let Some(a_ij) = matrix.get(start_row + i, j) {
                    sum += v_i * a_ij;
                }
            }
            
            sum *= self.beta;
            
            // 更新列
            for (i, &v_i) in self.v.iter().enumerate() {
                let row = start_row + i;
                if row < m {
                    let old_val = matrix.get(row, j).unwrap_or(0.0);
                    matrix.insert(row, j, old_val - sum * v_i)?;
                }
            }
        }
        
        Ok(())
    }
}

/// 稀疏QR分解实现
impl SparseQR {
    pub fn qr_decompose(matrix: &CsrMatrix<f64>) -> Result<Self, RustNumError> {
        let m = matrix.rows();
        let n = matrix.cols();
        
        let mut r = matrix.clone();
        let mut q = CscMatrix::identity(m);
        
        // 对每一列进行QR分解
        for k in 0..n.min(m) {
            // 提取当前列
            let mut col = vec![0.0; m - k];
            for i in k..m {
                col[i - k] = r.get(i, k).unwrap_or(0.0);
            }
            
            // 计算Householder变换
            let house = HouseholderTransform::compute(&col);
            
            // 应用变换到R
            house.apply(&mut r, k, k)?;
            
            // 更新Q（隐式存储Householder向量）
            for i in k..m {
                for j in k..m {
                    let qij = q.get(i, j).unwrap_or(0.0);
                    let update = house.beta * house.v[i - k] * house.v[j - k];
                    q.insert(i, j, qij - update)?;
                }
            }
        }
        
        Ok(Self { q, r })
    }
    
    /// 求解最小二乘问题min ||Ax - b||
    pub fn solve_least_squares(&self, b: &[f64]) -> Result<Vec<f64>, RustNumError> {
        let m = self.q.rows();
        let n = self.r.cols();
        
        if b.len() != m {
            return Err(RustNumError::DimensionMismatch(format!("向量长度不匹配: 期望 {}, 得到 {}", m, b.len())));
        }
        
        // 计算Q^T b
        let mut qty = vec![0.0; m];
        for i in 0..m {
            for j in 0..m {
                if let Some(qij) = self.q.get(j, i) {
                    qty[i] += qij * b[j];
                }
            }
        }
        
        // 回代求解Rx = Q^T b
        let mut x = vec![0.0; n];
        for i in (0..n).rev() {
            let mut sum = qty[i];
            for j in (i + 1)..n {
                if let Some(rij) = self.r.get(i, j) {
                    sum -= rij * x[j];
                }
            }
            if let Some(rii) = self.r.get(i, i) {
                if rii.abs() < 1e-10 {
                    return Err(RustNumError::ComputationError("Singular matrix".into()));
                }
                x[i] = sum / rii;
            }
        }
        
        Ok(x)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_sparse_qr_small() {
        // 创建小型测试矩阵
        let mut matrix: CsrMatrix<f64> = CsrMatrix::with_capacity(4, 3, 10);
        matrix.insert(0, 0, 1.0).unwrap();
        matrix.insert(0, 1, 2.0).unwrap();
        matrix.insert(1, 1, 3.0).unwrap();
        matrix.insert(2, 2, 4.0).unwrap();
        matrix.insert(3, 1, 5.0).unwrap();
        
        let qr = SparseQR::qr_decompose(&matrix).unwrap();
        
        // 简单验证QR分解成功
        assert_eq!(qr.q.rows(), 4);
        assert_eq!(qr.r.cols(), 3);
        println!("QR decomposition completed successfully");
    }
    
    #[test]
    fn test_sparse_qr_least_squares() {
        let mut matrix: CsrMatrix<f64> = CsrMatrix::with_capacity(2, 2, 4);
        matrix.insert(0, 0, 1.0).unwrap();
        matrix.insert(0, 1, 2.0).unwrap();
        matrix.insert(1, 0, 3.0).unwrap();
        matrix.insert(1, 1, 4.0).unwrap();
        
        let b = vec![5.0, 11.0];
        
        let qr = SparseQR::qr_decompose(&matrix).unwrap();
        let x = qr.solve_least_squares(&b).unwrap();
        
        // 简单验证解存在
        assert_eq!(x.len(), 2);
        println!("Solution found: {:?}", x);
    }
}
