//! Kubernetes 集成模块

use crate::error::RustNumError;
use serde::{Serialize, Deserialize};

/// Kubernetes 部署
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct KubernetesDeployment {
    pub name: String,
    pub namespace: String,
    pub replicas: u32,
}

/// Pod 规格
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PodSpec {
    pub name: String,
    pub image: String,
}

/// 服务规格
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServiceSpec {
    pub name: String,
    pub port: u16,
}
