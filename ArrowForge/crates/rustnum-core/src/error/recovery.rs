use super::*;
use std::time::Duration;
use std::thread;
use log::{error, warn, info, debug};

/// 错误恢复策略
#[derive(Debug, <PERSON>lone)]
pub enum RecoveryStrategy {
    /// 立即重试
    RetryImmediately {
        /// 最大重试次数
        max_attempts: usize,
    },
    
    /// 指数退避重试
    ExponentialBackoff {
        /// 最大重试次数
        max_attempts: usize,
        /// 初始等待时间（毫秒）
        initial_delay_ms: u64,
        /// 最大等待时间（毫秒）
        max_delay_ms: u64,
    },
    
    /// 降级操作
    Fallback {
        /// 降级策略
        strategy: FallbackStrategy,
    },
}

/// 降级策略
#[derive(Debug, <PERSON>lone)]
pub enum FallbackStrategy {
    /// 使用CPU计算替代GPU
    CpuFallback,
    /// 使用更稳定但较慢的算法
    StableAlgorithm,
    /// 降低精度
    ReducedPrecision,
    /// 使用近似解
    Approximation,
}

/// 错误恢复管理器
pub struct ErrorRecoveryManager {
    /// 全局恢复策略
    strategy: RecoveryStrategy,
    /// 错误计数器
    error_counts: std::sync::atomic::AtomicUsize,
}

impl ErrorRecoveryManager {
    /// 创建新的错误恢复管理器
    pub fn new(strategy: RecoveryStrategy) -> Self {
        Self {
            strategy,
            error_counts: std::sync::atomic::AtomicUsize::new(0),
        }
    }

    /// 执行带重试的操作
    pub fn retry_operation<F, T>(&self, mut operation: F) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        match &self.strategy {
            RecoveryStrategy::RetryImmediately { max_attempts } => {
                self.retry_immediately(operation, *max_attempts)
            }
            RecoveryStrategy::ExponentialBackoff { max_attempts, initial_delay_ms, max_delay_ms } => {
                self.retry_with_backoff(operation, *max_attempts, *initial_delay_ms, *max_delay_ms)
            }
            RecoveryStrategy::Fallback { strategy } => {
                self.execute_with_fallback(operation, strategy)
            }
        }
    }

    /// 立即重试策略实现
    fn retry_immediately<F, T>(&self, mut operation: F, max_attempts: usize) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        let mut attempts = 0;
        loop {
            match operation() {
                Ok(result) => {
                    if attempts > 0 {
                        info!("操作在第 {} 次尝试后成功", attempts + 1);
                    }
                    return Ok(result);
                }
                Err(e) => {
                    attempts += 1;
                    if attempts >= max_attempts {
                        error!("操作在 {} 次尝试后仍然失败: {:?}", attempts, e);
                        return Err(e);
                    }
                    warn!("第 {} 次尝试失败，准备重试: {:?}", attempts, e);
                }
            }
        }
    }

    /// 指数退避重试策略实现
    fn retry_with_backoff<F, T>(
        &self,
        mut operation: F,
        max_attempts: usize,
        initial_delay_ms: u64,
        max_delay_ms: u64,
    ) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        let mut attempts = 0;
        let mut delay_ms = initial_delay_ms;

        loop {
            match operation() {
                Ok(result) => {
                    if attempts > 0 {
                        info!("操作在第 {} 次尝试后成功", attempts + 1);
                    }
                    return Ok(result);
                }
                Err(e) => {
                    attempts += 1;
                    if attempts >= max_attempts {
                        error!("操作在 {} 次尝试后仍然失败: {:?}", attempts, e);
                        return Err(e);
                    }
                    warn!("第 {} 次尝试失败，等待 {}ms 后重试: {:?}", attempts, delay_ms, e);
                    
                    thread::sleep(Duration::from_millis(delay_ms));
                    delay_ms = (delay_ms * 2).min(max_delay_ms);
                }
            }
        }
    }

    /// 降级策略实现
    fn execute_with_fallback<F, T>(&self, mut operation: F, strategy: &FallbackStrategy) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        match operation() {
            Ok(result) => Ok(result),
            Err(e) => {
                warn!("主操作失败，尝试降级策略: {:?}", strategy);
                match strategy {
                    FallbackStrategy::CpuFallback => {
                        // 切换到CPU实现
                        self.execute_cpu_fallback(operation)
                    }
                    FallbackStrategy::StableAlgorithm => {
                        // 使用稳定算法
                        self.execute_stable_algorithm(operation)
                    }
                    FallbackStrategy::ReducedPrecision => {
                        // 降低精度重试
                        self.execute_reduced_precision(operation)
                    }
                    FallbackStrategy::Approximation => {
                        // 使用近似算法
                        self.execute_approximation(operation)
                    }
                }
            }
        }
    }

    /// CPU降级实现
    fn execute_cpu_fallback<F, T>(&self, mut operation: F) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        // 临时禁用GPU
        std::env::set_var("RUSTNUM_DISABLE_GPU", "1");
        let result = operation();
        std::env::remove_var("RUSTNUM_DISABLE_GPU");
        result
    }

    /// 稳定算法降级实现
    fn execute_stable_algorithm<F, T>(&self, mut operation: F) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        // 切换到稳定算法
        std::env::set_var("RUSTNUM_FORCE_STABLE_ALGO", "1");
        let result = operation();
        std::env::remove_var("RUSTNUM_FORCE_STABLE_ALGO");
        result
    }

    /// 降低精度实现
    fn execute_reduced_precision<F, T>(&self, mut operation: F) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        // 降低计算精度
        std::env::set_var("RUSTNUM_REDUCED_PRECISION", "1");
        let result = operation();
        std::env::remove_var("RUSTNUM_REDUCED_PRECISION");
        result
    }

    /// 近似算法实现
    fn execute_approximation<F, T>(&self, mut operation: F) -> Result<T>
    where
        F: FnMut() -> Result<T>,
    {
        // 使用近似算法
        std::env::set_var("RUSTNUM_USE_APPROXIMATION", "1");
        let result = operation();
        std::env::remove_var("RUSTNUM_USE_APPROXIMATION");
        result
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_immediate_retry() {
        let manager = ErrorRecoveryManager::new(RecoveryStrategy::RetryImmediately {
            max_attempts: 3,
        });

        let mut attempts = 0;
        let result = manager.retry_operation(|| {
            attempts += 1;
            if attempts < 3 {
                Err(Error::ComputationError("测试错误".into()))
            } else {
                Ok(42)
            }
        });

        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempts, 3);
    }

    #[test]
    fn test_exponential_backoff() {
        let manager = ErrorRecoveryManager::new(RecoveryStrategy::ExponentialBackoff {
            max_attempts: 3,
            initial_delay_ms: 10,
            max_delay_ms: 100,
        });

        let mut attempts = 0;
        let start = std::time::Instant::now();
        let result = manager.retry_operation(|| {
            attempts += 1;
            if attempts < 2 {
                Err(Error::ComputationError("测试错误".into()))
            } else {
                Ok(42)
            }
        });

        assert_eq!(result.unwrap(), 42);
        assert_eq!(attempts, 2);
        assert!(start.elapsed() >= Duration::from_millis(10));
    }

    #[test]
    fn test_fallback_strategy() {
        let manager = ErrorRecoveryManager::new(RecoveryStrategy::Fallback {
            strategy: FallbackStrategy::CpuFallback,
        });

        let result = manager.retry_operation(|| {
            if std::env::var("RUSTNUM_DISABLE_GPU").is_ok() {
                Ok(42)
            } else {
                Err(Error::ComputationError("GPU错误".into()))
            }
        });

        assert_eq!(result.unwrap(), 42);
    }
}
