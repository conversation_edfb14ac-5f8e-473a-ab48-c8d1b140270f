//! 模型注册表实现

use crate::error::RustNumError;
use super::{RegistryConfig, ModelArtifact};
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 模型注册表
pub struct ModelRegistry {
    config: RegistryConfig,
    models: HashMap<String, ModelMetadata>,
    versions: HashMap<String, Vec<ModelVersion>>,
}

/// 模型元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelMetadata {
    pub id: String,
    pub name: String,
    pub description: String,
    pub framework: ModelFramework,
    pub task_type: TaskType,
    pub tags: Vec<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub owner: String,
}

/// 模型版本
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelVersion {
    pub version: String,
    pub model_id: String,
    pub artifacts: Vec<ModelArtifact>,
    pub metrics: HashMap<String, f64>,
    pub parameters: HashMap<String, String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub status: VersionStatus,
}

/// 模型框架
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelFramework {
    PyTorch,
    TensorFlow,
    ONNX,
    RustNum,
    Scikit,
    XGBoost,
}

/// 任务类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TaskType {
    Classification,
    Regression,
    Clustering,
    Recommendation,
    NLP,
    ComputerVision,
    TimeSeries,
}

/// 版本状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum VersionStatus {
    Draft,
    Staging,
    Production,
    Archived,
}

impl ModelRegistry {
    pub fn new(config: RegistryConfig) -> Result<Self, RustNumError> {
        Ok(Self {
            config,
            models: HashMap::new(),
            versions: HashMap::new(),
        })
    }
    
    pub async fn register(&mut self, model: ModelMetadata, artifacts: Vec<ModelArtifact>) -> Result<String, RustNumError> {
        let model_id = model.id.clone();
        
        // 创建初始版本
        let version = ModelVersion {
            version: "1.0.0".to_string(),
            model_id: model_id.clone(),
            artifacts,
            metrics: HashMap::new(),
            parameters: HashMap::new(),
            created_at: chrono::Utc::now(),
            status: VersionStatus::Draft,
        };
        
        self.models.insert(model_id.clone(), model);
        self.versions.insert(model_id.clone(), vec![version]);
        
        Ok(model_id)
    }
    
    pub fn count_models(&self) -> usize {
        self.models.len()
    }
}
