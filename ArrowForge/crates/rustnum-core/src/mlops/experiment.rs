//! 实验跟踪实现

use super::ExperimentConfig;
use crate::error::RustNumError;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

/// 实验跟踪器
pub struct ExperimentTracker {
    config: ExperimentConfig,
    experiments: HashMap<String, ExperimentRun>,
}

/// 实验运行
#[derive(Debug, Clone)]
pub struct ExperimentRun {
    pub id: String,
    pub name: String,
    pub parameters: HashMap<String, HyperParameter>,
    pub metrics: HashMap<String, f64>,
    pub status: ExperimentStatus,
}

/// 超参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HyperParameter {
    Float(f64),
    Int(i64),
    String(String),
    Bool(bool),
}

/// 实验状态
#[derive(Debug, Clone)]
pub enum ExperimentStatus {
    Running,
    Completed,
    Failed,
}

impl ExperimentTracker {
    pub fn new(config: ExperimentConfig) -> Result<Self, RustNumError> {
        Ok(Self {
            config,
            experiments: HashMap::new(),
        })
    }
    
    pub fn start_experiment(&mut self, name: &str, parameters: HashMap<String, HyperParameter>) -> Result<String, RustNumError> {
        let id = uuid::Uuid::new_v4().to_string();
        let experiment = ExperimentRun {
            id: id.clone(),
            name: name.to_string(),
            parameters,
            metrics: HashMap::new(),
            status: ExperimentStatus::Running,
        };
        
        self.experiments.insert(id.clone(), experiment);
        Ok(id)
    }
    
    pub fn log_metric(&mut self, experiment_id: &str, key: &str, value: f64) -> Result<(), RustNumError> {
        if let Some(experiment) = self.experiments.get_mut(experiment_id) {
            experiment.metrics.insert(key.to_string(), value);
            Ok(())
        } else {
            Err(RustNumError::InvalidInput(format!("Experiment not found: {}", experiment_id)))
        }
    }
    
    pub fn count_active_experiments(&self) -> usize {
        self.experiments.values()
            .filter(|exp| matches!(exp.status, ExperimentStatus::Running))
            .count()
    }
}
