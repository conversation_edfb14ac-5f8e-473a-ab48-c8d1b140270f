//! MLOps 平台模块
//! 
//! 提供端到端的机器学习运维和模型生命周期管理

pub mod model_registry;
pub mod pipeline;
pub mod deployment;
pub mod monitoring;
pub mod versioning;
pub mod experiment;

pub use model_registry::{ModelRegistry, ModelMetadata, ModelVersion};
pub use pipeline::{MLPipeline, PipelineStage, PipelineConfig};
pub use deployment::{ModelDeployment, DeploymentStrategy, ServingConfig};
pub use monitoring::{ModelMonitor, MetricsCollector, AlertRule};
pub use versioning::{VersionControl, ModelArtifact, DataVersion};
pub use experiment::{ExperimentTracker, ExperimentRun, HyperParameter};

use crate::error::RustNumError;
use crate::ml::tensor::Tensor;
use std::collections::HashMap;
use serde::{Serialize, Deserialize};

/// MLOps 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MLOpsConfig {
    /// 模型注册表配置
    pub registry: RegistryConfig,
    /// 部署配置
    pub deployment: DeploymentConfig,
    /// 监控配置
    pub monitoring: MonitoringConfig,
    /// 实验跟踪配置
    pub experiment: ExperimentConfig,
}

/// 注册表配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegistryConfig {
    pub storage_backend: StorageBackend,
    pub metadata_store: MetadataStore,
    pub artifact_store: ArtifactStore,
}

/// 存储后端
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageBackend {
    Local { path: String },
    S3 { bucket: String, region: String },
    GCS { bucket: String, project: String },
    Azure { container: String, account: String },
}

/// 元数据存储
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MetadataStore {
    SQLite { path: String },
    PostgreSQL { connection_string: String },
    MongoDB { connection_string: String },
}

/// 制品存储
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ArtifactStore {
    FileSystem { base_path: String },
    ObjectStorage { backend: StorageBackend },
    Registry { url: String, credentials: Option<String> },
}

/// 部署配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeploymentConfig {
    pub default_strategy: DeploymentStrategy,
    pub serving_config: ServingConfig,
    pub scaling_config: ScalingConfig,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub metrics_enabled: bool,
    pub logging_enabled: bool,
    pub alerting_enabled: bool,
    pub drift_detection: DriftDetectionConfig,
}

/// 数据漂移检测配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DriftDetectionConfig {
    pub enabled: bool,
    pub threshold: f64,
    pub window_size: usize,
    pub detection_method: DriftDetectionMethod,
}

/// 漂移检测方法
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DriftDetectionMethod {
    KolmogorovSmirnov,
    ChiSquare,
    PopulationStabilityIndex,
    KLDivergence,
}

/// 实验配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExperimentConfig {
    pub tracking_enabled: bool,
    pub auto_logging: bool,
    pub artifact_logging: bool,
    pub hyperparameter_tuning: HyperparameterConfig,
}

/// 超参数配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HyperparameterConfig {
    pub enabled: bool,
    pub optimization_method: OptimizationMethod,
    pub max_trials: usize,
    pub timeout_seconds: Option<u64>,
}

/// 优化方法
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OptimizationMethod {
    RandomSearch,
    GridSearch,
    BayesianOptimization,
    HyperBand,
}

/// 扩展配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingConfig {
    pub auto_scaling: bool,
    pub min_replicas: u32,
    pub max_replicas: u32,
    pub target_cpu_utilization: f64,
    pub target_memory_utilization: f64,
    pub scale_up_cooldown: u64,
    pub scale_down_cooldown: u64,
}

/// MLOps 平台
pub struct MLOpsPlatform {
    config: MLOpsConfig,
    model_registry: ModelRegistry,
    pipeline_manager: PipelineManager,
    deployment_manager: DeploymentManager,
    monitor: ModelMonitor,
    experiment_tracker: ExperimentTracker,
}

/// 管道管理器
pub struct PipelineManager {
    pipelines: HashMap<String, MLPipeline>,
    active_runs: HashMap<String, PipelineRun>,
}

/// 部署管理器
pub struct DeploymentManager {
    deployments: HashMap<String, ModelDeployment>,
    serving_endpoints: HashMap<String, ServingEndpoint>,
}

/// 管道运行
#[derive(Debug, Clone)]
pub struct PipelineRun {
    pub id: String,
    pub pipeline_id: String,
    pub status: PipelineStatus,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub stages: Vec<StageRun>,
}

/// 管道状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PipelineStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

/// 阶段运行
#[derive(Debug, Clone)]
pub struct StageRun {
    pub stage_id: String,
    pub status: StageStatus,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub metrics: HashMap<String, f64>,
    pub artifacts: Vec<String>,
}

/// 阶段状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StageStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Skipped,
}

/// 服务端点
#[derive(Debug, Clone)]
pub struct ServingEndpoint {
    pub id: String,
    pub model_id: String,
    pub version: String,
    pub url: String,
    pub status: EndpointStatus,
    pub config: ServingConfig,
}

/// 端点状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EndpointStatus {
    Deploying,
    Ready,
    Updating,
    Failed,
    Terminating,
}

impl MLOpsPlatform {
    /// 创建新的 MLOps 平台
    pub fn new(config: MLOpsConfig) -> Result<Self, RustNumError> {
        Ok(Self {
            model_registry: ModelRegistry::new(config.registry.clone())?,
            pipeline_manager: PipelineManager::new(),
            deployment_manager: DeploymentManager::new(),
            monitor: ModelMonitor::new(config.monitoring.clone())?,
            experiment_tracker: ExperimentTracker::new(config.experiment.clone())?,
            config,
        })
    }
    
    /// 注册模型
    pub async fn register_model(&mut self, model: ModelMetadata, artifacts: Vec<ModelArtifact>) -> Result<String, RustNumError> {
        self.model_registry.register(model, artifacts).await
    }
    
    /// 创建 ML 管道
    pub fn create_pipeline(&mut self, config: PipelineConfig) -> Result<String, RustNumError> {
        self.pipeline_manager.create_pipeline(config)
    }
    
    /// 运行管道
    pub async fn run_pipeline(&mut self, pipeline_id: &str, parameters: HashMap<String, String>) -> Result<String, RustNumError> {
        self.pipeline_manager.run_pipeline(pipeline_id, parameters).await
    }
    
    /// 部署模型
    pub async fn deploy_model(&mut self, model_id: &str, version: &str, config: ServingConfig) -> Result<String, RustNumError> {
        self.deployment_manager.deploy_model(model_id, version, config).await
    }
    
    /// 获取模型预测
    pub async fn predict(&self, endpoint_id: &str, input: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        self.deployment_manager.predict(endpoint_id, input).await
    }
    
    /// 开始实验
    pub fn start_experiment(&mut self, name: &str, parameters: HashMap<String, HyperParameter>) -> Result<String, RustNumError> {
        self.experiment_tracker.start_experiment(name, parameters)
    }
    
    /// 记录指标
    pub fn log_metric(&mut self, experiment_id: &str, key: &str, value: f64) -> Result<(), RustNumError> {
        self.experiment_tracker.log_metric(experiment_id, key, value)
    }
    
    /// 获取平台状态
    pub fn get_platform_status(&self) -> PlatformStatus {
        PlatformStatus {
            registered_models: self.model_registry.count_models(),
            active_pipelines: self.pipeline_manager.count_active_pipelines(),
            running_deployments: self.deployment_manager.count_running_deployments(),
            active_experiments: self.experiment_tracker.count_active_experiments(),
        }
    }
}

/// 平台状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformStatus {
    pub registered_models: usize,
    pub active_pipelines: usize,
    pub running_deployments: usize,
    pub active_experiments: usize,
}

impl PipelineManager {
    pub fn new() -> Self {
        Self {
            pipelines: HashMap::new(),
            active_runs: HashMap::new(),
        }
    }
    
    pub fn create_pipeline(&mut self, config: PipelineConfig) -> Result<String, RustNumError> {
        let pipeline = MLPipeline::new(config)?;
        let id = pipeline.id.clone();
        self.pipelines.insert(id.clone(), pipeline);
        Ok(id)
    }
    
    pub async fn run_pipeline(&mut self, pipeline_id: &str, parameters: HashMap<String, String>) -> Result<String, RustNumError> {
        if let Some(pipeline) = self.pipelines.get(pipeline_id) {
            let run = pipeline.execute(parameters).await?;
            let run_id = run.id.clone();
            self.active_runs.insert(run_id.clone(), run);
            Ok(run_id)
        } else {
            Err(RustNumError::InvalidInput(format!("Pipeline not found: {}", pipeline_id)))
        }
    }
    
    pub fn count_active_pipelines(&self) -> usize {
        self.active_runs.values()
            .filter(|run| matches!(run.status, PipelineStatus::Running))
            .count()
    }
}

impl DeploymentManager {
    pub fn new() -> Self {
        Self {
            deployments: HashMap::new(),
            serving_endpoints: HashMap::new(),
        }
    }
    
    pub async fn deploy_model(&mut self, model_id: &str, version: &str, config: ServingConfig) -> Result<String, RustNumError> {
        let deployment = ModelDeployment::new(model_id.to_string(), version.to_string(), config.clone())?;
        let endpoint = deployment.create_endpoint().await?;
        
        let endpoint_id = endpoint.id.clone();
        self.deployments.insert(deployment.id.clone(), deployment);
        self.serving_endpoints.insert(endpoint_id.clone(), endpoint);
        
        Ok(endpoint_id)
    }
    
    pub async fn predict(&self, endpoint_id: &str, input: &Tensor<f32>) -> Result<Tensor<f32>, RustNumError> {
        if let Some(endpoint) = self.serving_endpoints.get(endpoint_id) {
            endpoint.predict(input).await
        } else {
            Err(RustNumError::InvalidInput(format!("Endpoint not found: {}", endpoint_id)))
        }
    }
    
    pub fn count_running_deployments(&self) -> usize {
        self.serving_endpoints.values()
            .filter(|endpoint| matches!(endpoint.status, EndpointStatus::Ready))
            .count()
    }
}

impl Default for MLOpsConfig {
    fn default() -> Self {
        Self {
            registry: RegistryConfig {
                storage_backend: StorageBackend::Local { path: "./models".to_string() },
                metadata_store: MetadataStore::SQLite { path: "./metadata.db".to_string() },
                artifact_store: ArtifactStore::FileSystem { base_path: "./artifacts".to_string() },
            },
            deployment: DeploymentConfig {
                default_strategy: DeploymentStrategy::BlueGreen,
                serving_config: ServingConfig::default(),
                scaling_config: ScalingConfig {
                    auto_scaling: true,
                    min_replicas: 1,
                    max_replicas: 10,
                    target_cpu_utilization: 70.0,
                    target_memory_utilization: 80.0,
                    scale_up_cooldown: 300,
                    scale_down_cooldown: 600,
                },
            },
            monitoring: MonitoringConfig {
                metrics_enabled: true,
                logging_enabled: true,
                alerting_enabled: true,
                drift_detection: DriftDetectionConfig {
                    enabled: true,
                    threshold: 0.1,
                    window_size: 1000,
                    detection_method: DriftDetectionMethod::KolmogorovSmirnov,
                },
            },
            experiment: ExperimentConfig {
                tracking_enabled: true,
                auto_logging: true,
                artifact_logging: true,
                hyperparameter_tuning: HyperparameterConfig {
                    enabled: true,
                    optimization_method: OptimizationMethod::BayesianOptimization,
                    max_trials: 100,
                    timeout_seconds: Some(3600),
                },
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_mlops_config_creation() {
        let config = MLOpsConfig::default();
        assert!(config.monitoring.metrics_enabled);
        assert!(config.experiment.tracking_enabled);
        assert!(config.deployment.scaling_config.auto_scaling);
    }
    
    #[tokio::test]
    async fn test_mlops_platform_creation() {
        let config = MLOpsConfig::default();
        let platform = MLOpsPlatform::new(config);
        assert!(platform.is_ok());
    }
    
    #[test]
    fn test_platform_status() {
        let config = MLOpsConfig::default();
        let platform = MLOpsPlatform::new(config).unwrap();
        let status = platform.get_platform_status();
        
        assert_eq!(status.registered_models, 0);
        assert_eq!(status.active_pipelines, 0);
        assert_eq!(status.running_deployments, 0);
        assert_eq!(status.active_experiments, 0);
    }
}
