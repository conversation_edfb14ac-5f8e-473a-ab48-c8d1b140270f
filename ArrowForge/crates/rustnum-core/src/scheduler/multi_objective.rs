use std::collections::HashMap;
use std::sync::Arc;
use parking_lot::RwLock;
use serde::{Serialize, Deserialize};
use crate::error::RustNumError;
#[cfg(feature = "ml_scheduler")]
use crate::scheduler::ml_scheduler::{MLScheduler, TaskFeatures, SystemLoad};

#[cfg(not(feature = "ml_scheduler"))]
use crate::scheduler::ml_scheduler::MLScheduler;

#[cfg(not(feature = "ml_scheduler"))]
#[derive(Debug, Clone)]
pub struct TaskFeatures {
    pub matrix_size: usize,
    pub estimated_flops: u64,
}
// use crate::scheduler::intelligent::TaskConfiguration; // 注释掉，使用本地定义

#[cfg(feature = "ml_scheduler")]
use crate::scheduler::pattern_recognition::{OptimizationHints, PatternRecognizer, MemoryStrategy, Priority};

#[cfg(not(feature = "ml_scheduler"))]
use crate::scheduler::pattern_recognition::Pat<PERSON><PERSON><PERSON>ogni<PERSON>;

/// 任务配置（用于多目标优化）
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TaskConfiguration {
    pub num_threads: usize,
    pub block_size: usize,
    #[cfg(feature = "ml_scheduler")]
    pub memory_strategy: MemoryStrategy,
    #[cfg(feature = "ml_scheduler")]
    pub scheduling_priority: Priority,
}

/// 优化约束
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationConstraints {
    pub max_execution_time: Option<f32>,
    pub max_memory_usage: Option<f32>,
    pub max_energy_consumption: Option<f32>,
    pub min_resource_efficiency: Option<f32>,
    pub max_threads: usize,
}

impl Default for OptimizationConstraints {
    fn default() -> Self {
        Self {
            max_execution_time: None,
            max_memory_usage: None,
            max_energy_consumption: None,
            min_resource_efficiency: None,
            max_threads: usize::MAX,
        }
    }
}

/// Pareto点：包含目标函数值和对应的解决方案
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParetoPoint<T, S> {
    objectives: Vec<T>,
    solution: S,
}

impl<T, S> ParetoPoint<T, S> {
    pub fn new(objectives: Vec<T>, solution: S) -> Self {
        Self { objectives, solution }
    }

    pub fn get_solution(&self) -> &S {
        &self.solution
    }

    pub fn get_objectives(&self) -> &[T] {
        &self.objectives
    }
}

/// Pareto前沿：存储一组非支配解
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ParetoFront<T, S> {
    points: Vec<ParetoPoint<T, S>>,
}

impl<T: PartialOrd + Clone, S: Clone> ParetoFront<T, S> {
    pub fn new() -> Self {
        Self { points: Vec::new() }
    }

    pub fn add_point(&mut self, new_point: ParetoPoint<T, S>) {
        let mut dominated_indices = Vec::new();
        let mut is_dominated_by_existing = false;

        for (i, existing_point) in self.points.iter().enumerate() {
            let mut dominates_existing = true;
            let mut is_equal = true;

            for j in 0..new_point.objectives.len() {
                match new_point.objectives[j].partial_cmp(&existing_point.objectives[j]) {
                    Some(std::cmp::Ordering::Greater) => dominates_existing = false,
                    Some(std::cmp::Ordering::Less) => is_equal = false,
                    Some(std::cmp::Ordering::Equal) => {},
                    None => { /* Handle NaN or other non-comparable cases if necessary */ dominates_existing = false; is_equal = false; }
                }
            }

            if dominates_existing && !is_equal {
                dominated_indices.push(i);
            } else if !dominates_existing && !is_equal {
                let mut new_dominates_existing = true;
                for j in 0..new_point.objectives.len() {
                    if existing_point.objectives[j].partial_cmp(&new_point.objectives[j]) == Some(std::cmp::Ordering::Greater) {
                        new_dominates_existing = false;
                        break;
                    }
                }
                if new_dominates_existing {
                    is_dominated_by_existing = true;
                    break;
                }
            }
        }

        if !is_dominated_by_existing {
            // 移除被新点支配的旧点
            for i in dominated_indices.into_iter().rev() {
                self.points.remove(i);
            }
            // 添加新点
            self.points.push(new_point);
        }
    }

    pub fn get_front(&self) -> &[ParetoPoint<T, S>] {
        &self.points
    }
}

/// 优化目标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizationObjectives {
    pub execution_time: f32,
    pub memory_usage: f32,
    pub energy_consumption: f32,
    pub resource_efficiency: f32,
}

/// 配置空间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfigurationSpace {
    pub thread_count: Vec<usize>,
    pub block_size: Vec<usize>,
    #[cfg(feature = "ml_scheduler")]
    pub memory_strategies: Vec<MemoryStrategy>,
    #[cfg(feature = "ml_scheduler")]
    pub priorities: Vec<Priority>,
}

impl Default for ConfigurationSpace {
    fn default() -> Self {
        Self {
            thread_count: vec![1, 2, 4, 8, 16],
            block_size: vec![32, 64, 128, 256],
            #[cfg(feature = "ml_scheduler")]
            memory_strategies: vec![
                MemoryStrategy::Default,
                MemoryStrategy::PreAllocate,
                MemoryStrategy::LazyAllocation,
            ],
            #[cfg(feature = "ml_scheduler")]
            priorities: vec![
                Priority::Low,
                Priority::Normal,
                Priority::High,
                Priority::RealTime,
            ],
        }
    }
}

/// 多目标优化器
pub struct MultiObjectiveOptimizer {
    ml_scheduler: Arc<MLScheduler>,
    pattern_recognizer: Arc<RwLock<PatternRecognizer>>,
    configuration_space: ConfigurationSpace,
    pareto_front: ParetoFront<f32, TaskConfiguration>,
    optimization_history: HashMap<String, Vec<(TaskConfiguration, OptimizationObjectives)>>,
}

impl MultiObjectiveOptimizer {
    pub fn new(
        ml_scheduler: Arc<MLScheduler>,
        pattern_recognizer: Arc<RwLock<PatternRecognizer>>,
    ) -> Self {
        Self {
            ml_scheduler,
            pattern_recognizer,
            configuration_space: ConfigurationSpace::default(),
            pareto_front: ParetoFront::new(),
            optimization_history: HashMap::new(),
        }
    }
    
    /// 优化任务配置
    #[cfg(feature = "ml_scheduler")]
    pub fn optimize(
        &mut self,
        task_features: &TaskFeatures,
        constraints: &OptimizationConstraints,
    ) -> Result<Vec<TaskConfiguration>, RustNumError> {
        // 1. 识别工作负载模式
        let pattern = {
            let recognizer = self.pattern_recognizer.read();
            recognizer.identify_pattern(task_features, &SystemLoad {
                cpu_usage: 0.5,
                memory_usage: 0.5,
                io_wait: 0.1,
            })
        };
        
        let hints = {
            // 2. 获取模式特定的优化提示
            let pattern_recognizer_guard = self.pattern_recognizer.read();
            pattern_recognizer_guard
                .get_optimization_hints(&pattern)
                .ok_or(RustNumError::ValueError("Invalid configuration".to_string()))?.clone()
        };

        // 3. 构建候选配置
        let candidates = self.generate_candidates(&hints, constraints);
        
        // 4. 评估每个候选配置
        let mut objectives = Vec::with_capacity(candidates.len());
        for config in &candidates {
            let obj = self.evaluate_configuration(task_features, config)?;
            objectives.push(obj);
        }
        
        // 5. 更新Pareto前沿
        self.update_pareto_front(&candidates, &objectives);
        
        // 6. 选择最优配置
        let selected = self.select_configurations(&candidates, &objectives, constraints);
        
        // 7. 更新优化历史
        self.update_history(task_features, &selected, &objectives);
        
        Ok(selected)
    }
    
    /// 优化任务配置（简化版本）
    #[cfg(not(feature = "ml_scheduler"))]
    pub fn optimize(
        &mut self,
        task_features: &TaskFeatures,
        constraints: &OptimizationConstraints,
    ) -> Result<Vec<TaskConfiguration>, RustNumError> {
        // 简化版本：直接生成候选配置
        let candidates = self.generate_candidates(&(), constraints);
        
        // 评估每个候选配置
        let mut objectives = Vec::with_capacity(candidates.len());
        for config in &candidates {
            let obj = self.evaluate_configuration(task_features, config)?;
            objectives.push(obj);
        }
        
        // 更新Pareto前沿
        self.update_pareto_front(&candidates, &objectives);
        
        // 选择最优配置
        let selected = self.select_configurations(&candidates, &objectives, constraints);
        
        Ok(selected)
    }
    
    /// 生成候选配置
    #[cfg(feature = "ml_scheduler")]
    fn generate_candidates(
        &self,
        hints: &OptimizationHints,
        constraints: &OptimizationConstraints,
    ) -> Vec<TaskConfiguration> {
        let mut candidates = Vec::new();
        
        for &thread_count in self.configuration_space.thread_count.iter()
            .filter(|&&t| t >= hints.recommended_thread_count.min 
                && t <= hints.recommended_thread_count.max
                && t <= constraints.max_threads)
        {
            for &block_size in self.configuration_space.block_size.iter()
                .filter(|&&b| b >= hints.recommended_block_size.min 
                    && b <= hints.recommended_block_size.max)
            {
                for memory_strategy in &self.configuration_space.memory_strategies {
                    for priority in &self.configuration_space.priorities {
                        candidates.push(TaskConfiguration {
                            num_threads: thread_count,
                            block_size,
                            memory_strategy: memory_strategy.clone(),
                            scheduling_priority: priority.clone(),
                        });
                    }
                }
            }
        }
        
        candidates
    }
    
    /// 生成候选配置（简化版本）
    #[cfg(not(feature = "ml_scheduler"))]
    fn generate_candidates(
        &self,
        _hints: &(),
        constraints: &OptimizationConstraints,
    ) -> Vec<TaskConfiguration> {
        let mut candidates = Vec::new();
        
        for &thread_count in self.configuration_space.thread_count.iter()
            .filter(|&&t| t <= constraints.max_threads)
        {
            for &block_size in &self.configuration_space.block_size {
                candidates.push(TaskConfiguration {
                    num_threads: thread_count,
                    block_size,
                });
            }
        }
        
        candidates
    }
    
    /// 评估配置的目标函数值
    fn evaluate_configuration(
        &self,
        features: &TaskFeatures,
        config: &TaskConfiguration,
    ) -> Result<OptimizationObjectives, RustNumError> {
        // 简单的执行时间估算，基于矩阵大小和操作复杂度
        let predicted_time = (features.matrix_size as f32 * features.estimated_flops as f32 / 1e9) as f32;
         let memory_usage = self.estimate_memory_usage(features, config);
         let energy_consumption = self.estimate_energy_consumption(features, config);
         let efficiency = self.estimate_resource_efficiency(features, config);
         
         Ok(OptimizationObjectives {
             execution_time: predicted_time,
             memory_usage,
             energy_consumption,
             resource_efficiency: efficiency,
         })
    }
    
    /// 更新Pareto前沿
    fn update_pareto_front(
        &mut self,
        candidates: &[TaskConfiguration],
        objectives: &[OptimizationObjectives],
    ) {
        for (config, obj) in candidates.iter().zip(objectives) {
            let point = ParetoPoint::new(
                vec![
                    obj.execution_time,
                    obj.memory_usage,
                    obj.energy_consumption,
                    -obj.resource_efficiency, // 转换为最小化问题
                ],
                config.clone(),
            );
            self.pareto_front.add_point(point);
        }
    }
    
    /// 根据约束条件选择最优配置
    fn select_configurations(
        &self,
        candidates: &[TaskConfiguration],
        objectives: &[OptimizationObjectives],
        constraints: &OptimizationConstraints,
    ) -> Vec<TaskConfiguration> {
        let mut selected = Vec::new();
        
        for (config, obj) in candidates.iter().zip(objectives) {
            // 检查约束条件
            let mut meets_constraints = true;
            if let Some(max_time) = constraints.max_execution_time {
                if obj.execution_time > max_time + f32::EPSILON {
                    meets_constraints = false;
                }
            }
            if let Some(max_memory) = constraints.max_memory_usage {
                if obj.memory_usage > max_memory + f32::EPSILON {
                    meets_constraints = false;
                }
            }
            if let Some(max_energy) = constraints.max_energy_consumption {
                if obj.energy_consumption > max_energy + f32::EPSILON {
                    meets_constraints = false;
                }
            }
            if let Some(min_efficiency) = constraints.min_resource_efficiency {
                if obj.resource_efficiency < min_efficiency - f32::EPSILON {
                    meets_constraints = false;
                }
            }

            if meets_constraints
            {
                selected.push(config.clone());
            }
        }
        
        // 如果没有满足约束的解，选择Pareto前沿上的解
        if selected.is_empty() {
            selected = self.pareto_front.get_front()
                .iter()
                .map(|point| point.get_solution().clone())
                .collect();
        }
        
        selected
    }
    
    /// 估算内存使用
    fn estimate_memory_usage(&self, _features: &TaskFeatures, config: &TaskConfiguration) -> f32 {
        let base_memory = _features.matrix_size as f32 * 8.0f32; // 假设每个元素8字节

        #[cfg(feature = "ml_scheduler")]
        let strategy_factor = match config.memory_strategy {
            MemoryStrategy::Default => 1.0f32,
            MemoryStrategy::PreAllocate => 1.2f32, // 预分配可能增加峰值内存
            MemoryStrategy::LazyAllocation => 0.8f32, // 懒分配可能降低峰值内存
        };
        
        #[cfg(not(feature = "ml_scheduler"))]
        let strategy_factor = 1.0f32; // 默认策略因子

        base_memory * strategy_factor
    }
    
    /// 估算能耗
    fn estimate_energy_consumption(&self, _features: &TaskFeatures, config: &TaskConfiguration) -> f32 {
         let base_energy = _features.matrix_size as f32 * 0.001f32; // 基础能耗
        
        let thread_factor = 1.0f32 - (config.num_threads as f32 / 32.0f32).min(0.5f32); // 更多线程可能降低单位能耗
        let block_factor = 1.0f32 + (config.block_size as f32 / 1024.0f32).min(0.2f32); // 更大块大小可能增加能耗

        base_energy * thread_factor * block_factor
    }
    
    fn estimate_resource_efficiency(&self, _features: &TaskFeatures, config: &TaskConfiguration) -> f32 {
        let cpu_utilization_factor = 1.0f32 - (config.num_threads as f32 / 64.0f32).min(0.5f32); // 更多线程可能导致CPU利用率下降
        let cache_hit_rate_factor = 1.0f32 + (config.block_size as f32 / 512.0f32).min(0.3f32); // 更大块大小可能提高缓存命中率

        // 综合考虑各种因素，这里只是一个简化模型
        cpu_utilization_factor * cache_hit_rate_factor
    }
    
    /// 更新优化历史
    fn update_history(
        &mut self,
        features: &TaskFeatures,
        configs: &[TaskConfiguration],
        objectives: &[OptimizationObjectives],
    ) {
        let key = format!("{}x{}", features.matrix_size, features.matrix_size);
        
        let history = self.optimization_history
            .entry(key)
            .or_default();
            
        for (config, obj) in configs.iter().zip(objectives) {
            history.push((config.clone(), obj.clone()));
        }
        
        // 保持历史记录在合理范围内
        if history.len() > 100 {
            history.drain(0..50);
        }
    }
}
