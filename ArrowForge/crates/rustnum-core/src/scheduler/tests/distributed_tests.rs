#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::sleep;
    use std::time::Duration;

    #[tokio::test]
    async fn test_node_registration() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let capabilities = NodeCapabilities {
            cpu_cores: 8,
            memory_gb: 16,
            has_gpu: false,
            network_bandwidth_gbps: 10.0,
        };
        
        let node_id = coordinator.register_node(capabilities.clone()).await;
        
        assert!(coordinator.nodes.contains_key(&node_id));
        assert_eq!(coordinator.nodes.get(&node_id).unwrap().capabilities.cpu_cores, 8);
    }

    #[tokio::test]
    async fn test_model_update_handling() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let node_id = Uuid::new_v4();
        let model_diff = ModelDiff {
            weights: vec![1.0, 2.0, 3.0],
            bias: vec![0.1, 0.2],
            iteration: 1,
        };
        
        let event = LearningEvent::ModelUpdate {
            source_node: node_id,
            model_diff,
            timestamp: Instant::now(),
        };
        
        coordinator.publish_event(event).await;
        
        // 验证版本更新
        assert!(coordinator.model_versions.get(&node_id).is_some());
    }

    #[tokio::test]
    async fn test_performance_report_handling() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let node_id = coordinator.register_node(NodeCapabilities {
            cpu_cores: 4,
            memory_gb: 8,
            has_gpu: false,
            network_bandwidth_gbps: 1.0,
        }).await;
        
        let metrics = PerformanceMetrics {
            execution_time: Duration::from_millis(100),
            cpu_usage: vec![0.5, 0.6, 0.4, 0.5],
            memory_usage: 0.7,
            energy_consumption: 100.0,
            throughput: 1000.0,
        };
        
        let config = TaskConfiguration::default();
        
        let event = LearningEvent::PerformanceReport {
            node_id,
            metrics,
            config,
        };
        
        coordinator.publish_event(event).await;
        
        // 验证节点负载更新
        let node = coordinator.nodes.get(&node_id).unwrap();
        assert_eq!(node.current_load.cpu_usage.len(), 4);
    }

    #[tokio::test]
    async fn test_workload_pattern_recognition() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let node_id = coordinator.register_node(NodeCapabilities {
            cpu_cores: 8,
            memory_gb: 16,
            has_gpu: true,
            network_bandwidth_gbps: 10.0,
        }).await;
        
        let pattern = WorkloadPattern {
            pattern_type: PatternType::BatchProcessing,
            characteristics: PatternCharacteristics {
                avg_task_duration: Duration::from_secs(10),
                cpu_intensity: 0.8,
                memory_intensity: 0.6,
                io_intensity: 0.3,
                parallelism_potential: 0.9,
                data_locality: 0.7,
            },
            optimization_hints: OptimizationHints {
                recommended_thread_count: Range { min: 4, max: 8 },
                recommended_block_size: Range { min: 128, max: 256 },
                memory_strategy: MemoryStrategy::PreAllocate,
                scheduling_priority: Priority::High,
                prefetch_strategy: PrefetchStrategy::Aggressive,
            },
        };
        
        let event = LearningEvent::WorkloadPattern {
            node_id,
            pattern: pattern.clone(),
            confidence: 0.9,
        };
        
        coordinator.publish_event(event).await;
    }

    #[tokio::test]
    async fn test_heartbeat_mechanism() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let node_id = coordinator.register_node(NodeCapabilities {
            cpu_cores: 4,
            memory_gb: 8,
            has_gpu: false,
            network_bandwidth_gbps: 1.0,
        }).await;
        
        coordinator.start_heartbeat().await;
        
        // 等待足够长的时间使心跳检测生效
        sleep(Duration::from_secs(70)).await;
        
        // 验证超时节点被移除
        assert!(!coordinator.nodes.contains_key(&node_id));
    }

    #[tokio::test]
    async fn test_concurrent_model_updates() {
        let coordinator = DistributedLearningCoordinator::new().await;
        
        let mut handles = Vec::new();
        
        // 模拟多个节点并发更新
        for i in 0..5 {
            let coord = coordinator.clone();
            let handle = tokio::spawn(async move {
                let node_id = Uuid::new_v4();
                let model_diff = ModelDiff {
                    weights: vec![i as f32],
                    bias: vec![i as f32 * 0.1],
                    iteration: i as u64,
                };
                
                let event = LearningEvent::ModelUpdate {
                    source_node: node_id,
                    model_diff,
                    timestamp: Instant::now(),
                };
                
                coord.publish_event(event).await;
            });
            
            handles.push(handle);
        }
        
        // 等待所有更新完成
        for handle in handles {
            handle.await.unwrap();
        }
        
        // 验证模型版本更新
        assert!(coordinator.aggregator.read().await.current_iteration() > 0);
    }
}
