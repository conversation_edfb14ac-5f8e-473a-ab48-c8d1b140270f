#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use approx::assert_relative_eq;

    fn create_sample_task_features() -> TaskFeatures {
        TaskFeatures {
            matrix_size: 1000,
            sparsity: 0.1,
            operation_type: OperationType::MatrixMultiply,
            memory_footprint: 8 * 1000 * 1000, // 8MB
            data_locality: 0.8,
            parallelism_degree: 4,
        }
    }

    fn create_sample_task_history() -> TaskHistory {
        TaskHistory {
            features: create_sample_task_features(),
            execution_time: 0.5,
            cpu_utilization: vec![0.7, 0.6, 0.8, 0.7],
            memory_usage: 0.4,
            energy_consumption: 100.0,
        }
    }

    #[test]
    fn test_ml_scheduler_prediction() {
        let scheduler = MLScheduler::new();
        
        // 添加一些训练数据
        for _ in 0..100 {
            let mut history = create_sample_task_history();
            history.features.matrix_size = rand::random::<usize>() % 2000 + 500;
            history.execution_time = history.features.matrix_size as f64 * 0.001;
            scheduler.update_model(history);
        }
        
        // 测试预测
        let features = create_sample_task_features();
        let predicted_time = scheduler.predict_execution_time(&features);
        
        // 验证预测结果是否在合理范围内
        assert!(predicted_time > 0.0);
        assert!(predicted_time < 10.0);
    }

    #[test]
    fn test_configuration_recommendation() {
        let scheduler = MLScheduler::new();
        let features = create_sample_task_features();
        
        // 获取推荐配置
        let config = scheduler.recommend_configuration(&features);
        
        // 验证配置的合理性
        assert!(config.num_threads >= 1);
        assert!(config.num_threads <= 16);
        assert!(config.block_size.is_power_of_two());
        assert!(config.block_size >= 32);
        assert!(config.block_size <= 256);
    }

    #[test]
    fn test_workload_analyzer() {
        let mut analyzer = WorkloadAnalyzer::new();
        
        // 创建一系列历史记录
        let histories: Vec<_> = (0..10)
            .map(|_| create_sample_task_history())
            .collect();
        
        // 分析模式
        analyzer.analyze_patterns(&histories);
        
        // 预测未来负载
        let future_load = analyzer.predict_future_load(Duration::from_secs(60));
        
        // 验证预测结果
        assert!(future_load.cpu_usage.iter().all(|&x| x >= 0.0 && x <= 1.0));
        assert!(future_load.memory_usage >= 0.0 && future_load.memory_usage <= 1.0);
    }

    #[test]
    fn test_adaptive_optimizer() {
        let optimizer = AdaptiveOptimizer::new();
        let features = create_sample_task_features();
        
        // 测试优化策略
        let config = optimizer.optimize_strategy(&features);
        
        // 验证策略的适应性
        let history = create_sample_task_history();
        optimizer.update(history);
        
        let new_config = optimizer.optimize_strategy(&features);
        
        // 验证配置会根据历史记录进行调整
        assert_ne!(
            (config.num_threads, config.block_size),
            (new_config.num_threads, new_config.block_size)
        );
    }

    #[test]
    fn test_anomaly_detection() {
        let analyzer = WorkloadAnalyzer::new();
        
        let normal_load = SystemLoad {
            cpu_usage: vec![0.5, 0.6, 0.4, 0.5],
            memory_usage: 0.6,
            io_wait: 0.1,
        };
        
        let anomaly_load = SystemLoad {
            cpu_usage: vec![0.95, 0.98, 0.97, 0.96],
            memory_usage: 0.9,
            io_wait: 0.4,
        };
        
        // 检测正常负载
        let normal_anomalies = analyzer.detect_anomalies(&normal_load);
        assert!(normal_anomalies.is_empty());
        
        // 检测异常负载
        let anomalies = analyzer.detect_anomalies(&anomaly_load);
        assert!(!anomalies.is_empty());
    }

    #[test]
    fn test_model_update_performance() {
        let scheduler = MLScheduler::new();
        let start_time = std::time::Instant::now();
        
        // 测试模型更新性能
        for _ in 0..1000 {
            let history = create_sample_task_history();
            scheduler.update_model(history);
        }
        
        let duration = start_time.elapsed();
        
        // 确保更新性能在可接受范围内
        assert!(duration < Duration::from_secs(5));
    }

    #[test]
    fn test_feature_scaling() {
        let scheduler = MLScheduler::new();
        
        // 添加一些极端值的训练数据
        let mut extreme_features = create_sample_task_features();
        extreme_features.matrix_size = 1_000_000;
        
        let history = TaskHistory {
            features: extreme_features,
            execution_time: 100.0,
            cpu_utilization: vec![0.9, 0.9, 0.9, 0.9],
            memory_usage: 0.9,
            energy_consumption: 1000.0,
        };
        
        scheduler.update_model(history);
        
        // 测试正常范围的输入
        let normal_features = create_sample_task_features();
        let prediction = scheduler.predict_execution_time(&normal_features);
        
        // 确保预测结果在合理范围内
        assert!(prediction > 0.0 && prediction < 1000.0);
    }
}
