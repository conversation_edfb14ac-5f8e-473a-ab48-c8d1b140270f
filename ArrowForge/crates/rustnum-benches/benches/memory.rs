//! Memory allocation benchmarks

use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId};
use rustnum_core::memory::MemoryPool;

fn bench_memory_allocation(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_allocation");
    let pool = MemoryPool::new();
    
    // 测试不同大小的内存分配
    let sizes = [
        32,       // 小对象
        512,      // 小对象
        4096,     // 小对象边界
        16384,    // 中等对象
        262144,   // 中等对象
        1048576,  // 中等对象边界
        4194304,  // 大对象
    ];
    
    for size in sizes {
        // 测试内存池分配
        group.bench_with_input(
            BenchmarkId::new("pool_alloc", size), 
            &size,
            |b, &size| {
                b.iter(|| {
                    let ptr = pool.allocate(size, 8).unwrap();
                    unsafe {
                        pool.deallocate(ptr, size, 8);
                    }
                });
            }
        );
        
        // 测试系统分配器
        group.bench_with_input(
            BenchmarkId::new("system_alloc", size),
            &size,
            |b, &size| {
                b.iter(|| {
                    let layout = std::alloc::Layout::from_size_align(size, 8).unwrap();
                    unsafe {
                        let ptr = std::alloc::alloc(layout);
                        std::alloc::dealloc(ptr, layout);
                    }
                });
            }
        );
    }
    
    group.finish();
}

fn bench_memory_reuse(c: &mut Criterion) {
    let mut group = c.benchmark_group("memory_reuse");
    let pool = MemoryPool::new();
    
    // 测试内存重用场景
    group.bench_function("pool_reuse_small", |b| {
        let mut ptrs = Vec::new();
        b.iter(|| {
            // 分配100个小对象
            for _ in 0..100 {
                ptrs.push(pool.allocate(64, 8).unwrap());
            }
            // 释放所有对象
            for ptr in ptrs.drain(..) {
                unsafe {
                    pool.deallocate(ptr, 64, 8);
                }
            }
        });
    });
    
    group.bench_function("pool_reuse_medium", |b| {
        let mut ptrs = Vec::new();
        b.iter(|| {
            // 分配10个中等对象
            for _ in 0..10 {
                ptrs.push(pool.allocate(65536, 8).unwrap());
            }
            // 释放所有对象
            for ptr in ptrs.drain(..) {
                unsafe {
                    pool.deallocate(ptr, 65536, 8);
                }
            }
        });
    });
    
    group.finish();
}

fn bench_parallel_allocation(c: &mut Criterion) {
    let mut group = c.benchmark_group("parallel_allocation");
    let pool = MemoryPool::new();
    
    group.bench_function("parallel_small_allocs", |b| {
        use std::sync::Arc;
        use rayon::prelude::*;
        
        let pool = Arc::new(pool);
        b.iter(|| {
            (0..1000).into_par_iter().for_each(|_| {
                let pool = Arc::clone(&pool);
                let ptr = pool.allocate(64, 8).unwrap();
                unsafe {
                    pool.deallocate(ptr, 64, 8);
                }
            });
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    bench_memory_allocation,
    bench_memory_reuse,
    bench_parallel_allocation
);
criterion_main!(benches);
