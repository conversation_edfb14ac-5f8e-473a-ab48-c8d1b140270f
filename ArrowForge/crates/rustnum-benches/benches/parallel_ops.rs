use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>llel<PERSON>, <PERSON><PERSON><PERSON>ger, ParallelConfig};

fn parallel_matmul_benchmark(c: &mut Criterion) {
    let sizes = [128, 256, 512, 1024, 2048];
    
    // 配置并行环境
    ParallelManager::initialize(Some(ParallelConfig {
        num_threads: num_cpus::get(),
        enable_blas_threads: false, // 禁用BLAS多线程以便比较
        min_parallel_size: 1024,
        auto_parallel: true,
    }));
    
    let mut group = c.benchmark_group("Matrix Multiplication");
    for &n in sizes.iter() {
        let a = RustArray::from_fn((n, n), |i, j| (i + j) as f64);
        let b = RustArray::from_fn((n, n), |i, j| (i * j) as f64);
        
        group.bench_function(format!("Serial {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).matmul(black_box(&b))
            });
        });
        
        group.bench_function(format!("Parallel {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).parallel_matmul(black_box(&b)).unwrap()
            });
        });
    }
    group.finish();
}

fn parallel_map_benchmark(c: &mut Criterion) {
    let sizes = [100_000, 1_000_000, 10_000_000];
    
    let mut group = c.benchmark_group("Element-wise Operations");
    for &n in sizes.iter() {
        let a = RustArray::from_fn((n,), |i| i as f64);
        
        group.bench_function(format!("Serial Map {n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).map(|x| x.sin() * x.cos())
            });
        });
        
        group.bench_function(format!("Parallel Map {n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).parallel_map(|x| x.sin() * x.cos())
            });
        });
    }
    group.finish();
}

fn parallel_reduce_benchmark(c: &mut Criterion) {
    let sizes = [100_000, 1_000_000, 10_000_000];
    
    let mut group = c.benchmark_group("Reduction Operations");
    for &n in sizes.iter() {
        let a = RustArray::from_fn((n,), |i| i as f64);
        
        group.bench_function(format!("Serial Sum {n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).sum()
            });
        });
        
        group.bench_function(format!("Parallel Sum {n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).parallel_reduce(0.0, |acc, x| acc + x)
            });
        });
    }
    group.finish();
}

criterion_group! {
    name = benches;
    config = Criterion::default().sample_size(10);
    targets = parallel_matmul_benchmark, parallel_map_benchmark, parallel_reduce_benchmark
}
criterion_main!(benches);
