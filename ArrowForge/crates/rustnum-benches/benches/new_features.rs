use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::sparse::decomposition::{SparseLU, SparseQR};
use rustnum_core::gpu::{GpuMemoryManager, CudaBackend};
use rustnum_core::scheduler::intelligent::IntelligentScheduler;

fn bench_sparse_lu(c: &mut Criterion) {
    let mut group = c.benchmark_group("Sparse LU Decomposition");
    
    // 创建稀疏矩阵用于测试
    group.bench_function("sparse_lu_1000x1000", |b| {
        b.iter(|| {
            // 创建1000x1000的稀疏矩阵并进行LU分解
            let sparse_lu = SparseLU::new(1000, 1000);
            black_box(sparse_lu);
        });
    });
    
    group.finish();
}

fn bench_gpu_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("GPU Operations");
    
    // GPU内存管理基准测试
    group.bench_function("gpu_memory_allocation", |b| {
        b.iter(|| {
            // 测试GPU内存分配性能
            let gpu_manager = GpuMemoryManager::new();
            black_box(gpu_manager);
        });
    });
    
    // CUDA后端基准测试
    group.bench_function("cuda_backend_init", |b| {
        b.iter(|| {
            // 测试CUDA后端初始化性能
            let cuda_backend = CudaBackend::new();
            black_box(cuda_backend);
        });
    });
    
    group.finish();
}

fn bench_intelligent_scheduler(c: &mut Criterion) {
    let mut group = c.benchmark_group("Task Scheduling");
    
    // 智能调度器初始化基准测试
    group.bench_function("scheduler_init", |b| {
        b.iter(|| {
            // 测试调度器初始化性能
            let scheduler = IntelligentScheduler::new();
            black_box(scheduler);
        });
    });
    
    // 任务调度性能测试
    group.bench_function("task_scheduling_1000", |b| {
        let scheduler = IntelligentScheduler::new();
        b.iter(|| {
            // 测试1000个任务的调度性能
            for i in 0..1000 {
                let task_id = format!("task_{}", i);
                black_box(task_id);
            }
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    bench_sparse_lu,
    bench_gpu_operations,
    bench_intelligent_scheduler
);
criterion_main!(benches);
