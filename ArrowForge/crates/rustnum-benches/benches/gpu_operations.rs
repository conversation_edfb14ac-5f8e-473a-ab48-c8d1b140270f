use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::gpu::{GpuEngine, OptimizedGpuEngine};
use rand::Rng;

fn generate_random_matrix(rows: usize, cols: usize) -> Vec<f32> {
    let mut rng = rand::thread_rng();
    (0..rows * cols).map(|_| rng.gen()).collect()
}

fn bench_matrix_multiply(c: &mut Criterion) {
    let mut group = c.benchmark_group("GPU Matrix Multiply");
    
    // 测试不同矩阵大小
    for size in [128, 256, 512, 1024, 2048].iter() {
        // 基准版本
        group.bench_function(format!("basic_{}x{}", size, size), |b| {
            let engine = GpuEngine::new().unwrap();
            let a = generate_random_matrix(*size, *size);
            let b = generate_random_matrix(*size, *size);
            
            b.iter(|| {
                black_box(engine.matrix_multiply(&a, &b, *size, *size, *size).unwrap())
            });
        });
        
        // 优化版本
        group.bench_function(format!("optimized_{}x{}", size, size), |b| {
            let engine = OptimizedGpuEngine::new().unwrap();
            let a = generate_random_matrix(*size, *size);
            let b = generate_random_matrix(*size, *size);
            
            b.iter(|| {
                black_box(engine.matrix_multiply_optimized(&a, &b, *size, *size, *size).unwrap())
            });
        });
    }
    
    group.finish();
}

fn bench_matrix_transpose(c: &mut Criterion) {
    let mut group = c.benchmark_group("GPU Matrix Transpose");
    
    for size in [128, 256, 512, 1024, 2048].iter() {
        group.bench_function(format!("optimized_{}x{}", size, size), |b| {
            let engine = OptimizedGpuEngine::new().unwrap();
            let input = generate_random_matrix(*size, *size);
            
            b.iter(|| {
                black_box(engine.matrix_transpose_optimized(&input, *size, *size).unwrap())
            });
        });
    }
    
    group.finish();
}

fn bench_reduction(c: &mut Criterion) {
    let mut group = c.benchmark_group("GPU Reduction");
    
    for size in [1024, 10240, 102400, 1024000].iter() {
        group.bench_function(format!("reduce_sum_{}", size), |b| {
            let engine = OptimizedGpuEngine::new().unwrap();
            let input = generate_random_matrix(1, *size);
            
            b.iter(|| {
                black_box(engine.reduce_sum(&input).unwrap())
            });
        });
    }
    
    group.finish();
}

criterion_group! {
    name = gpu_benches;
    config = Criterion::default()
        .sample_size(10)  // 减少样本数量，因为GPU计算较慢
        .measurement_time(std::time::Duration::from_secs(20));
    targets = bench_matrix_multiply, bench_matrix_transpose, bench_reduction
}

criterion_main!(gpu_benches);
