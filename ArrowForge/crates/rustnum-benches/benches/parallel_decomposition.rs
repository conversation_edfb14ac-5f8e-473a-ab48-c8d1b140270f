use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::{Rust<PERSON>rra<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ParallelConfig};

fn parallel_decomposition_benchmark(c: &mut Criterion) {
    let sizes = [256, 512, 1024, 2048];
    
    // 配置并行环境
    ParallelManager::initialize(Some(ParallelConfig {
        num_threads: num_cpus::get(),
        enable_blas_threads: false, // 禁用BLAS多线程以便比较
        min_parallel_size: 1024,
        auto_parallel: true,
    }));
    
    let mut group = c.benchmark_group("Matrix Decomposition");
    
    // LU分解基准测试
    for &n in sizes.iter() {
        let a = RustArray::from_fn((n, n), |i, j| {
            (i + j) as f64 / (n as f64)
        });
        
        group.bench_function(format!("Serial LU {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).lu_decomposition().unwrap()
            });
        });
        
        group.bench_function(format!("Parallel LU {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).parallel_lu().unwrap()
            });
        });
    }
    
    // Cholesky分解基准测试
    for &n in sizes.iter() {
        // 创建对称正定矩阵
        let mut a = RustArray::from_fn((n, n), |i, j| {
            if i <= j {
                (i + j + 1) as f64 / (n as f64)
            } else {
                0.0
            }
        });
        // 确保正定性
        for i in 0..n {
            a[[i, i]] += n as f64;
        }
        
        group.bench_function(format!("Serial Cholesky {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).cholesky().unwrap()
            });
        });
        
        group.bench_function(format!("Parallel Cholesky {n}x{n}"), |bencher| {
            bencher.iter(|| {
                black_box(&a).parallel_cholesky().unwrap()
            });
        });
    }
    
    group.finish();
}

criterion_group! {
    name = benches;
    config = Criterion::default().sample_size(10);
    targets = parallel_decomposition_benchmark
}
