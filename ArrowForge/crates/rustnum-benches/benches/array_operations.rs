use criterion::{black_box, criterion_group, criterion_main, Criterion};
use rustnum_core::RustArray;
use pyo3::Python;
use numpy::PyArray1;

fn array_creation_benchmark(c: &mut Criterion) {
    let mut group = c.benchmark_group("array_creation");
    
    // RustNum array creation
    group.bench_function("rustnum_create_1m", |b| {
        b.iter(|| {
            let arr = RustArray::<f64>::new(vec![1_000_000]).unwrap();
            black_box(arr);
        });
    });

    // NumPy array creation (for comparison)
    group.bench_function("numpy_create_1m", |b| {
        Python::with_gil(|py| {
            b.iter(|| {
                let arr = PyArray1::<f64>::zeros(py, [1_000_000], false);
                black_box(arr);
            });
        });
    });

    group.finish();
}

fn array_operations_benchmark(c: &mut Criterion) {
    let mut group = c.benchmark_group("array_operations");
    
    // 准备测试数组
    let arr1 = RustArray::<f64>::new(vec![1_000_000]).unwrap();
    let arr2 = RustArray::<f64>::new(vec![1_000_000]).unwrap();

    // 基本操作性能测试
    group.bench_function("rustnum_add_1m", |b| {
        b.iter(|| {
            // 实现加法操作
            let result = arr1.add(&arr2).unwrap();
            black_box(result);
        });
    });

    group.finish();
}

criterion_group!(
    benches,
    array_creation_benchmark,
    array_operations_benchmark
);
criterion_main!(benches);
