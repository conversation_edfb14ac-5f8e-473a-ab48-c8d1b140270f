#!/usr/bin/env python3

import os
import re
import glob

def fix_syntax_errors(file_path):
    """修复Rust文件中的各种语法错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_count = 0
        
        # 1. 修复缺少右括号的assert语句
        patterns = [
            (r'assert!\([^)]+$', lambda m: m.group(0) + ')'),
            (r'assert!\([^)]+\n', lambda m: m.group(0).rstrip('\n') + ');\n'),
        ]
        
        # 2. 修复缺少分号的语句
        content = re.sub(r'(let\s+\w+\s*=\s*[^;]+)\n', r'\1;\n', content)
        content = re.sub(r'(Arc::new\([^)]+\))\n', r'\1;\n', content)
        content = re.sub(r'(\w+\.\w+\([^)]*\))\n(?!\s*[;}])', r'\1;\n', content)
        
        # 3. 修复Ok(StructName {) 模式
        content = re.sub(r'Ok\((\w+)\s*\{\)', r'\1 {', content)
        content = re.sub(r'let\s+(\w+)\s*=\s*Ok\(([^)]+)\)\s*;', r'let \1 = \2;', content)
        
        # 4. 修复缺少右括号的函数调用
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 检查是否有未闭合的括号
            open_parens = line.count('(')
            close_parens = line.count(')')
            
            if open_parens > close_parens:
                # 检查下一行是否以分号或右括号开始
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if not next_line.startswith((';', ')', '}')):
                        # 添加缺少的右括号
                        missing_parens = open_parens - close_parens
                        line += ')' * missing_parens
                        if not line.endswith(';') and not line.endswith('{'):
                            line += ';'
                        fixes_count += 1
            
            # 修复assert语句
            if 'assert!' in line and not line.strip().endswith(');'):
                if line.count('(') > line.count(')'):
                    missing_parens = line.count('(') - line.count(')')
                    line += ')' * missing_parens
                if not line.strip().endswith(';'):
                    line += ';'
                fixes_count += 1
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # 5. 修复特定的语法模式
        specific_fixes = [
            # 修复缺少分号的Arc::new调用
            (r'(Arc::new\([^)]+\))(?!;)', r'\1;'),
            # 修复缺少分号的变量赋值
            (r'(let\s+\w+\s*=\s*[^;\n]+)(?=\n)', r'\1;'),
            # 修复函数调用后缺少分号
            (r'(\w+\.\w+\([^)]*\))(?!;|\.|\n)', r'\1;'),
        ]
        
        for pattern, replacement in specific_fixes:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                fixes_count += len(re.findall(pattern, content))
                content = new_content
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return fixes_count
        
        return 0
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return 0

def main():
    # 查找所有Rust文件
    rust_files = []
    for root, dirs, files in os.walk('src'):
        for file in files:
            if file.endswith('.rs'):
                rust_files.append(os.path.join(root, file))
    
    total_fixes = 0
    files_processed = 0
    
    for file_path in rust_files:
        fixes = fix_syntax_errors(file_path)
        if fixes > 0:
            print(f"修复了 {file_path} 中的 {fixes} 个语法错误")
            total_fixes += fixes
        files_processed += 1
    
    print(f"\n总计处理了 {files_processed} 个文件，修复了 {total_fixes} 个语法错误")

if __name__ == '__main__':
    main()