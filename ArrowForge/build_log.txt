warning: virtual workspace defaulting to `resolver = "1"` despite one or more workspace members being on edition 2021 which implies `resolver = "2"`
note: to keep the current resolver, specify `workspace.resolver = "1"` in the workspace root's manifest
note: to use the edition 2021 resolver, specify `workspace.resolver = "2"` in the workspace root's manifest
note: for more details see https://doc.rust-lang.org/cargo/reference/resolver.html#resolver-versions
warning: unused import: `num_traits::Float`
 --> crates/rustnum-core/src/lib.rs:3:5
  |
3 | use num_traits::Float;
  |     ^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused imports: `DefaultSimdExecutor`, `SimdElement`, `SimdExecutor`, and `SimdOp`
 --> crates/rustnum-core/src/lib.rs:5:25
  |
5 | ...:simd::{SimdElement, SimdOp, Sim<PERSON><PERSON>xecutor, DefaultSimdExecutor};
  |            ^^^^^^^^^^^  ^^^^^^  ^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^

warning: unused import: `num_traits::Float`
 --> crates/rustnum-core/src/array/mod.rs:6:5
  |
6 | use num_traits::Float;
  |     ^^^^^^^^^^^^^^^^^

warning: unused import: `DefaultSimdExecutor`
 --> crates/rustnum-core/src/simd/mod.rs:2:60
  |
2 | ...nt, SimdOp, SimdExecutor, DefaultSimdExecutor};
  |                              ^^^^^^^^^^^^^^^^^^^

warning: unused import: `parking_lot::RwLock`
 --> crates/rustnum-core/src/sparse/decomposition.rs:3:5
  |
3 | use parking_lot::RwLock;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `CscMatrix`
 --> crates/rustnum-core/src/sparse/parallel_ops.rs:1:41
  |
1 | use crate::sparse::storage::{CsrMatrix, CscMatrix};
  |                                         ^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> crates/rustnum-core/src/sparse/qr.rs:5:5
  |
5 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `crate::RustArray`
 --> crates/rustnum-core/src/solvers/iterative.rs:1:5
  |
1 | use crate::RustArray;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `c_float`
 --> crates/rustnum-core/src/backend/bindings.rs:3:30
  |
3 | use libc::{c_char, c_double, c_float, c_int};
  |                              ^^^^^^^

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:5:1
   |
5  |   /// BLAS Level 1 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
6  | / extern "C" {
7  | |     // 向量点积
8  | |     fn ddot_(
9  | |         n: *const c_int,
...  |
24 | |     );
25 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment
   = note: `#[warn(unused_doc_comments)]` on by default

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:27:1
   |
27 |   /// BLAS Level 2 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
28 | / extern "C" {
29 | |     // 矩阵向量乘法
30 | |     fn dgemv_(
31 | |         trans: *const c_char,
...  |
42 | |     );
43 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:45:1
   |
45 |   /// BLAS Level 3 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
46 | / extern "C" {
47 | |     // 矩阵乘法
48 | |     fn dgemm_(
49 | |         transa: *const c_char,
...  |
62 | |     );
63 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment

warning: unused doc comment
   --> crates/rustnum-core/src/backend/bindings.rs:65:1
    |
65  |   /// LAPACK函数签名
    |   ^^^^^^^^^^^^^^^^^^
66  | / extern "C" {
67  | |     // SVD分解
68  | |     fn dgesvd_(
69  | |         jobu: *const c_char,
...   |
101 | |     );
102 | | }
    | |_- rustdoc does not generate documentation for extern blocks
    |
    = help: use `//` for a plain comment

warning: unexpected `cfg` condition value: `cuda`
   --> crates/rustnum-core/src/backend/config.rs:145:32
    |
145 | ...   let has_cuda = if cfg!(feature = "cuda") { cuda_is_available() } e...
    |                              ^^^^^^^^^^^^^^^^
    |
    = note: expected values for `feature` are: `async-trait`, `blas`, `blas-src`, `dashmap`, `default`, `distributed`, `futures`, `intel-mkl-src`, `lapack`, `lapack-src`, `mkl`, `ml_scheduler`, `ndarray`, `ndarray-stats`, `openblas`, `openblas-src`, `opencl`, `opencl3`, `optimization`, `packed_simd`, `rustfft`, `rustlearn`, `simd`, `smartcore`, `tokio`, and `uuid`
    = help: consider adding `cuda` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: `#[warn(unexpected_cfgs)]` on by default

warning: unused import: `SimdExecutor`
 --> crates/rustnum-core/src/simd/mod.rs:2:46
  |
2 | use crate::simd::simd::{SimdElement, SimdOp, SimdExecutor, DefaultSimdExec...
  |                                              ^^^^^^^^^^^^

warning: unused variable: `matrix`
   --> crates/rustnum-core/src/sparse/decomposition.rs:167:9
    |
167 |         matrix: &CsrMatrix<f64>,
    |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_matrix`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `transa`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:67:9
   |
67 |         transa: bool,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_transa`

warning: unused variable: `transb`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:68:9
   |
68 |         transb: bool,
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_transb`

warning: unused variable: `m`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:69:9
   |
69 |         m: usize,
   |         ^ help: if this is intentional, prefix it with an underscore: `_m`

warning: unused variable: `n`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:70:9
   |
70 |         n: usize,
   |         ^ help: if this is intentional, prefix it with an underscore: `_n`

warning: unused variable: `k`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:71:9
   |
71 |         k: usize,
   |         ^ help: if this is intentional, prefix it with an underscore: `_k`

warning: unused variable: `alpha`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:72:9
   |
72 |         alpha: Self::Scalar,
   |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_alpha`

warning: unused variable: `a`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:73:9
   |
73 |         a: &[Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_a`

warning: unused variable: `lda`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:74:9
   |
74 |         lda: usize,
   |         ^^^ help: if this is intentional, prefix it with an underscore: `_lda`

warning: unused variable: `b`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:75:9
   |
75 |         b: &[Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_b`

warning: unused variable: `ldb`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:76:9
   |
76 |         ldb: usize,
   |         ^^^ help: if this is intentional, prefix it with an underscore: `_ldb`

warning: unused variable: `beta`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:77:9
   |
77 |         beta: Self::Scalar,
   |         ^^^^ help: if this is intentional, prefix it with an underscore: `_beta`

warning: unused variable: `c`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:78:9
   |
78 |         c: &mut [Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_c`

warning: unused variable: `ldc`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:79:9
   |
79 |         ldc: usize,
   |         ^^^ help: if this is intentional, prefix it with an underscore: `_ldc`

warning: unused variable: `jobu`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:86:9
   |
86 |         jobu: char,
   |         ^^^^ help: if this is intentional, prefix it with an underscore: `_jobu`

warning: unused variable: `jobvt`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:87:9
   |
87 |         jobvt: char,
   |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_jobvt`

warning: unused variable: `m`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:88:9
   |
88 |         m: usize,
   |         ^ help: if this is intentional, prefix it with an underscore: `_m`

warning: unused variable: `n`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:89:9
   |
89 |         n: usize,
   |         ^ help: if this is intentional, prefix it with an underscore: `_n`

warning: unused variable: `a`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:90:9
   |
90 |         a: &mut [Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_a`

warning: unused variable: `lda`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:91:9
   |
91 |         lda: usize,
   |         ^^^ help: if this is intentional, prefix it with an underscore: `_lda`

warning: unused variable: `s`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:92:9
   |
92 |         s: &mut [Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_s`

warning: unused variable: `u`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:93:9
   |
93 |         u: &mut [Self::Scalar],
   |         ^ help: if this is intentional, prefix it with an underscore: `_u`

warning: unused variable: `ldu`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:94:9
   |
94 |         ldu: usize,
   |         ^^^ help: if this is intentional, prefix it with an underscore: `_ldu`

warning: unused variable: `vt`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:95:9
   |
95 |         vt: &mut [Self::Scalar],
   |         ^^ help: if this is intentional, prefix it with an underscore: `_vt`

warning: unused variable: `ldvt`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:96:9
   |
96 |         ldvt: usize,
   |         ^^^^ help: if this is intentional, prefix it with an underscore: `_ldvt`

warning: unused variable: `work`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:97:9
   |
97 |         work: &mut [Self::Scalar],
   |         ^^^^ help: if this is intentional, prefix it with an underscore: `_work`

warning: unused variable: `lwork`
  --> crates/rustnum-core/src/backend/blas_lapack.rs:98:9
   |
98 |         lwork: usize,
   |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_lwork`

warning: unused variable: `jobvl`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:105:9
    |
105 |         jobvl: char,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_jobvl`

warning: unused variable: `jobvr`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:106:9
    |
106 |         jobvr: char,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_jobvr`

warning: unused variable: `n`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:107:9
    |
107 |         n: usize,
    |         ^ help: if this is intentional, prefix it with an underscore: `_n`

warning: unused variable: `a`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:108:9
    |
108 |         a: &mut [Self::Scalar],
    |         ^ help: if this is intentional, prefix it with an underscore: `_a`

warning: unused variable: `lda`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:109:9
    |
109 |         lda: usize,
    |         ^^^ help: if this is intentional, prefix it with an underscore: `_lda`

warning: unused variable: `wr`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:110:9
    |
110 |         wr: &mut [Self::Scalar],
    |         ^^ help: if this is intentional, prefix it with an underscore: `_wr`

warning: unused variable: `wi`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:111:9
    |
111 |         wi: &mut [Self::Scalar],
    |         ^^ help: if this is intentional, prefix it with an underscore: `_wi`

warning: unused variable: `vl`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:112:9
    |
112 |         vl: &mut [Self::Scalar],
    |         ^^ help: if this is intentional, prefix it with an underscore: `_vl`

warning: unused variable: `ldvl`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:113:9
    |
113 |         ldvl: usize,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_ldvl`

warning: unused variable: `vr`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:114:9
    |
114 |         vr: &mut [Self::Scalar],
    |         ^^ help: if this is intentional, prefix it with an underscore: `_vr`

warning: unused variable: `ldvr`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:115:9
    |
115 |         ldvr: usize,
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_ldvr`

warning: unused variable: `work`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:116:9
    |
116 |         work: &mut [Self::Scalar],
    |         ^^^^ help: if this is intentional, prefix it with an underscore: `_work`

warning: unused variable: `lwork`
   --> crates/rustnum-core/src/backend/blas_lapack.rs:117:9
    |
117 |         lwork: usize,
    |         ^^^^^ help: if this is intentional, prefix it with an underscore: `_lwork`

warning: field `layout` is never read
  --> crates/rustnum-core/src/array/mod.rs:33:5
   |
29 | pub struct ArrayView<T> {
   |            --------- field in this struct
...
33 |     layout: Layout,
   |     ^^^^^^
   |
   = note: `#[warn(dead_code)]` on by default

warning: field `pool` is never read
  --> crates/rustnum-core/src/array/mod.rs:48:5
   |
38 | pub struct RustArray<T> {
   |            --------- field in this struct
...
48 |     pool: Arc<MemoryPool>,
   |     ^^^^

warning: constant `NEON_SUPPORT` is never used
  --> crates/rustnum-core/src/simd/mod.rs:12:7
   |
12 | const NEON_SUPPORT: u32 = 1 << 2;
   |       ^^^^^^^^^^^^

warning: field `prev_num_threads` is never read
   --> crates/rustnum-core/src/parallel.rs:123:5
    |
122 | pub struct BlasThreadGuard {
    |            --------------- field in this struct
123 |     prev_num_threads: i32,
    |     ^^^^^^^^^^^^^^^^

warning: function `extract_dense_block` is never used
   --> crates/rustnum-core/src/sparse/decomposition.rs:276:8
    |
276 |     fn extract_dense_block(snode: &Supernode, matrix: &CsrMatrix<f64>) 
    |        ^^^^^^^^^^^^^^^^^^^

warning: function `daxpy_` is never used
  --> crates/rustnum-core/src/backend/bindings.rs:17:8
   |
17 |     fn daxpy_(
   |        ^^^^^^

warning: function `dgemv_` is never used
  --> crates/rustnum-core/src/backend/bindings.rs:30:8
   |
30 |     fn dgemv_(
   |        ^^^^^^

warning: function `dgeev_` is never used
  --> crates/rustnum-core/src/backend/bindings.rs:86:8
   |
86 |     fn dgeev_(
   |        ^^^^^^

warning: fields `has_avx`, `has_avx2`, `has_avx512`, and `has_fma` are never read
   --> crates/rustnum-core/src/backend/config.rs:123:21
    |
122 |             pub struct CpuFeatures {
    |                        ----------- fields in this struct
123 |                 pub has_avx: bool,
    |                     ^^^^^^^
124 |                 pub has_avx2: bool,
    |                     ^^^^^^^^
125 |                 pub has_avx512: bool,
    |                     ^^^^^^^^^^
126 |                 pub has_fma: bool,
    |                     ^^^^^^^

warning: fields `ptr`, `capacity`, and `allocator` are never read
  --> crates/rustnum-core/src/memory/mod.rs:60:5
   |
58 | pub struct Buffer<T> {
   |            ------ fields in this struct
59 |     /// 原始指针
60 |     ptr: NonNull<T>,
   |     ^^^
61 |     /// 容量
62 |     capacity: usize,
   |     ^^^^^^^^
63 |     /// 分配器
64 |     allocator: SmartAllocator,
   |     ^^^^^^^^^

warning: fields `pool`, `alignment`, and `prealloc_strategy` are never read
  --> crates/rustnum-core/src/memory/allocator.rs:10:5
   |
8  | pub struct SmartAllocator {
   |            -------------- fields in this struct
9  |     /// 内存池
10 |     pool: MemoryPool,
   |     ^^^^
11 |     /// 对齐要求
12 |     alignment: usize,
   |     ^^^^^^^^^
13 |     /// 预分配策略
14 |     prealloc_strategy: PreallocStrategy,
   |     ^^^^^^^^^^^^^^^^^

warning: struct `GlobalPool` is never constructed
   --> crates/rustnum-core/src/memory/pool.rs:147:12
    |
147 | pub struct GlobalPool;
    |            ^^^^^^^^^^

warning: associated function `new` is never used
   --> crates/rustnum-core/src/memory/pool.rs:150:14
    |
149 | impl GlobalPool {
    | --------------- associated function in this implementation
150 |     const fn new() -> Self {
    |              ^^^

warning: multiple fields are never read
  --> crates/rustnum-core/src/memory/stats.rs:4:9
   |
2  | pub struct AllocationStats {
   |            --------------- fields in this struct
3  |     /// 总分配次数
4  |     pub total_allocations: usize,
   |         ^^^^^^^^^^^^^^^^^
5  |     /// 总释放次数
6  |     pub total_deallocations: usize,
   |         ^^^^^^^^^^^^^^^^^^^
7  |     /// 总分配字节数
8  |     pub total_bytes_allocated: usize,
   |         ^^^^^^^^^^^^^^^^^^^^^
9  |     /// 总释放字节数
10 |     pub total_bytes_freed: usize,
   |         ^^^^^^^^^^^^^^^^^
11 |     /// 当前活跃分配数
12 |     pub active_allocations: usize,
   |         ^^^^^^^^^^^^^^^^^^
13 |     /// 当前使用的内存量
14 |     pub current_bytes_used: usize,
   |         ^^^^^^^^^^^^^^^^^^
15 |     /// 峰值内存使用量
16 |     pub peak_bytes_used: usize,
   |         ^^^^^^^^^^^^^^^
17 |     /// 小对象分配次数
18 |     pub small_allocations: usize,
   |         ^^^^^^^^^^^^^^^^^
19 |     /// 中等对象分配次数
20 |     pub medium_allocations: usize,
   |         ^^^^^^^^^^^^^^^^^^
21 |     /// 大对象分配次数
22 |     pub large_allocations: usize,
   |         ^^^^^^^^^^^^^^^^^
   |
   = note: `AllocationStats` has derived impls for the traits `Debug` and `Clone`, but these are intentionally ignored during dead code analysis

warning: methods `record_allocation`, `record_deallocation`, `fragmentation_ratio`, `average_allocation_size`, and `generate_report` are never used
  --> crates/rustnum-core/src/memory/stats.rs:27:12
   |
25 | impl AllocationStats {
   | -------------------- methods in this implementation
26 |     /// 记录新的分配
27 |     pub fn record_allocation(&mut self, size: usize) {
   |            ^^^^^^^^^^^^^^^^^
...
45 |     pub fn record_deallocation(&mut self, size: usize) {
   |            ^^^^^^^^^^^^^^^^^^^
...
53 |     pub fn fragmentation_ratio(&self) -> f64 {
   |            ^^^^^^^^^^^^^^^^^^^
...
63 |     pub fn average_allocation_size(&self) -> usize {
   |            ^^^^^^^^^^^^^^^^^^^^^^^
...
72 |     pub fn generate_report(&self) -> String {
   |            ^^^^^^^^^^^^^^^

warning: unused `Result` that must be used
   --> crates/rustnum-core/src/sparse/decomposition.rs:253:25
    |
253 |                         l.insert(row, col, val);
    |                         ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: this `Result` may be an `Err` variant, which should be handled
    = note: `#[warn(unused_must_use)]` on by default
help: use `let _ = ...` to ignore the resulting value
    |
253 |                         let _ = l.insert(row, col, val);
    |                         +++++++

warning: unused `Result` that must be used
   --> crates/rustnum-core/src/sparse/decomposition.rs:266:25
    |
266 |                         u.insert(row, col, val);
    |                         ^^^^^^^^^^^^^^^^^^^^^^^
    |
    = note: this `Result` may be an `Err` variant, which should be handled
help: use `let _ = ...` to ignore the resulting value
    |
266 |                         let _ = u.insert(row, col, val);
    |                         +++++++

warning: creating a shared reference to mutable static
  --> crates/rustnum-core/src/backend/config.rs:66:18
   |
66 |         unsafe { SINGLETON.as_ref().unwrap() }
   |                  ^^^^^^^^^^^^^^^^^^ shared reference to mutable static
   |
   = note: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>
   = note: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives
   = note: `#[warn(static_mut_refs)]` on by default

warning: `rustnum-core` (lib) generated 73 warnings (run `cargo fix --lib -p rustnum-core` to apply 9 suggestions)
   Compiling rustnum-python v0.1.0 (/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/crates/rustnum-python)
error[E0432]: unresolved imports `rustnum_core::StorageOrder`, `rustnum_core::create_default_pool`
  --> crates/rustnum-python/src/lib.rs:23:28
   |
23 |         use rustnum_core::{StorageOrder, create_default_pool};
   |                            ^^^^^^^^^^^^  ^^^^^^^^^^^^^^^^^^^ no `create_default_pool` in the root
   |                            |
   |                            no `StorageOrder` in the root
   |
   = help: consider importing this enum instead:
           rustnum_core::array::StorageOrder

For more information about this error, try `rustc --explain E0432`.
error: could not compile `rustnum-python` (lib) due to 1 previous error
