#!/bin/bash
set -e

# 加载 Rust 环境（如果存在）
if [ -f ~/.cargo/env ]; then
    source ~/.cargo/env
fi

# 颜色输出函数
red() { echo -e "\033[31m$1\033[0m"; }
green() { echo -e "\033[32m$1\033[0m"; }
yellow() { echo -e "\033[33m$1\033[0m"; }
blue() { echo -e "\033[34m$1\033[0m"; }
cyan() { echo -e "\033[36m$1\033[0m"; }

echo "$(cyan '=== RustNum 快速编译检查 ===')"
echo "Rust版本: $(rustc --version)"
echo "Cargo版本: $(cargo --version)"
echo

START_TIME=$(date +%s)
ERROR_COUNT=0
WARNING_COUNT=0

# 错误处理函数
handle_error() {
    red "❌ $1"
    ERROR_COUNT=$((ERROR_COUNT + 1))
}

# 成功处理函数
handle_success() {
    green "✅ $1"
}

# 警告处理函数
handle_warning() {
    yellow "⚠️  $1"
    WARNING_COUNT=$((WARNING_COUNT + 1))
}

# 检查函数
check_module() {
    local module_name=$1
    local module_path=$2
    local check_flags=$3
    
    echo "$(blue "[检查]") $module_name..."
    cd "$module_path"
    
    if cargo check $check_flags --quiet 2>/dev/null; then
        handle_success "$module_name 检查通过"
    else
        # 重新运行以显示错误信息
        echo "  -> 详细错误信息:"
        if ! cargo check $check_flags; then
            handle_error "$module_name 检查失败"
        fi
    fi
    
    cd - > /dev/null
}

# 检查核心模块
echo "$(blue '[1/4]') 检查核心模块 (rustnum-core)..."
check_module "rustnum-core" "crates/rustnum-core" "--lib --no-default-features"

# 检查Python绑定
echo "$(blue '[2/4]') 检查Python绑定 (rustnum-python)..."
check_module "rustnum-python" "crates/rustnum-python" "--lib"

# 检查分布式模块
echo "$(blue '[3/4]') 检查分布式模块 (rustnum-distributed)..."
check_module "rustnum-distributed" "crates/rustnum-distributed" "--lib"

# 检查基准测试
echo "$(blue '[4/4]') 检查基准测试 (rustnum-benches)..."
check_module "rustnum-benches" "crates/rustnum-benches" ""

# 工作空间级别检查
echo "$(blue '[额外]') 工作空间级别检查..."
echo "  -> 检查工作空间依赖..."
if cargo check --workspace --quiet 2>/dev/null; then
    handle_success "工作空间检查通过"
else
    echo "  -> 详细错误信息:"
    if ! cargo check --workspace; then
        handle_error "工作空间检查失败"
    fi
fi

# 计算耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo
echo "$(cyan '=== 检查结果摘要 ===')"
echo "总耗时: ${DURATION}秒"
echo "错误数量: $ERROR_COUNT"
echo "警告数量: $WARNING_COUNT"
echo

if [ $ERROR_COUNT -eq 0 ]; then
    echo "$(green '🎉 所有模块编译检查通过！')"
    echo
    echo "$(blue '建议下一步:')"
    echo "  - 运行 './scripts/build_isolated.sh' 进行完整编译"
    echo "  - 运行 './scripts/test_isolated.sh' 进行测试验证"
    echo "  - 开始功能开发或调试"
    exit 0
else
    echo "$(red "❌ 发现 $ERROR_COUNT 个编译错误")"
    echo
    echo "$(blue '建议修复步骤:')"
    echo "  1. 查看上述错误信息"
    echo "  2. 修复编译错误"
    echo "  3. 重新运行此脚本验证"
    echo "  4. 参考 'docs/模块隔离编译规划方案.md' 获取帮助"
    exit 1
fi

echo
echo "=== 快速检查完成 ==="
echo "🎉 所有模块编译检查通过！"
echo
echo "模块隔离编译策略验证成功："
echo "✅ 核心模块（最小化配置）编译正常"
echo "✅ Python 绑定模块编译正常"
echo "✅ 分布式计算模块编译正常"
echo "✅ 基准测试模块编译正常"
echo
echo "下一步："
echo "1. 可以开始进行功能开发和测试"
echo "2. 建议运行单元测试验证功能正确性"
echo "3. 可以开始性能基准测试"
