#!/bin/bash
set -e

# 加载 Rust 环境（如果存在）
if [ -f ~/.cargo/env ]; then
    source ~/.cargo/env
fi

# 颜色输出函数
red() { echo -e "\033[31m$1\033[0m"; }
green() { echo -e "\033[32m$1\033[0m"; }
yellow() { echo -e "\033[33m$1\033[0m"; }
blue() { echo -e "\033[34m$1\033[0m"; }
cyan() { echo -e "\033[36m$1\033[0m"; }
magenta() { echo -e "\033[35m$1\033[0m"; }

TEST_TYPE=${1:-all}
VERBOSE=${2:-false}

echo "$(cyan '=== RustNum 模块隔离测试 ===')"
echo "测试类型: $TEST_TYPE"
echo "详细输出: $VERBOSE"
echo "Rust版本: $(rustc --version)"
echo

START_TIME=$(date +%s)
TEST_COUNT=0
PASSED_COUNT=0
FAILED_COUNT=0
WARNING_COUNT=0

# 错误处理函数
handle_error() {
    red "❌ $1"
    FAILED_COUNT=$((FAILED_COUNT + 1))
}

# 成功处理函数
handle_success() {
    green "✅ $1"
    PASSED_COUNT=$((PASSED_COUNT + 1))
}

# 警告处理函数
handle_warning() {
    yellow "⚠️  $1"
    WARNING_COUNT=$((WARNING_COUNT + 1))
}

# 测试函数
run_test() {
    local test_name=$1
    local test_path=$2
    local test_flags=$3
    
    echo "$(blue "[测试]") $test_name..."
    cd "$test_path"
    
    TEST_COUNT=$((TEST_COUNT + 1))
    
    if [ "$VERBOSE" = "true" ]; then
        # 详细输出模式
        if cargo test $test_flags; then
            handle_success "$test_name 测试通过"
        else
            handle_error "$test_name 测试失败"
        fi
    else
        # 静默模式，只在失败时显示详细信息
        if cargo test $test_flags --quiet 2>/dev/null; then
            handle_success "$test_name 测试通过"
        else
            echo "  -> 详细测试输出:"
            if ! cargo test $test_flags; then
                handle_error "$test_name 测试失败"
            fi
        fi
    fi
    
    cd - > /dev/null
}

# 运行基准测试
run_benchmark() {
    local bench_name=$1
    local bench_path=$2
    
    echo "$(magenta "[基准]") $bench_name..."
    cd "$bench_path"
    
    if cargo bench --no-run 2>/dev/null; then
        handle_success "$bench_name 基准测试编译通过"
    else
        handle_warning "$bench_name 基准测试编译失败"
    fi
    
    cd - > /dev/null
}

# 根据测试类型执行不同的测试
case "$TEST_TYPE" in
    "core")
        echo "$(blue '[单独]') 测试核心模块..."
        run_test "rustnum-core" "crates/rustnum-core" "--lib"
        ;;
    "python")
        echo "$(blue '[单独]') 测试Python绑定..."
        run_test "rustnum-python" "crates/rustnum-python" "--lib"
        ;;
    "distributed")
        echo "$(blue '[单独]') 测试分布式模块..."
        run_test "rustnum-distributed" "crates/rustnum-distributed" ""
        ;;
    "bench")
        echo "$(blue '[单独]') 测试基准模块..."
        run_benchmark "rustnum-benches" "crates/rustnum-benches"
        ;;
    "unit")
        echo "$(blue '[单元测试]') 运行所有单元测试..."
        run_test "rustnum-core" "crates/rustnum-core" "--lib"
        run_test "rustnum-distributed" "crates/rustnum-distributed" "--lib"
        ;;
    "integration")
        echo "$(blue '[集成测试]') 运行集成测试..."
        echo "  -> 工作空间集成测试..."
        if [ "$VERBOSE" = "true" ]; then
            if cargo test --workspace; then
                handle_success "工作空间集成测试通过"
            else
                handle_error "工作空间集成测试失败"
            fi
        else
            if cargo test --workspace --quiet 2>/dev/null; then
                handle_success "工作空间集成测试通过"
            else
                echo "  -> 详细测试输出:"
                if ! cargo test --workspace; then
                    handle_error "工作空间集成测试失败"
                fi
            fi
        fi
        TEST_COUNT=$((TEST_COUNT + 1))
        ;;
    "all")
        echo "$(blue '[完整测试]') 运行所有测试..."
        
        # 单元测试
        echo "$(blue '[1/4]') 核心模块单元测试..."
        run_test "rustnum-core" "crates/rustnum-core" "--lib"
        
        echo "$(blue '[2/4]') 分布式模块测试..."
        run_test "rustnum-distributed" "crates/rustnum-distributed" ""
        
        echo "$(blue '[3/4]') 工作空间集成测试..."
        echo "  -> 运行集成测试..."
        if [ "$VERBOSE" = "true" ]; then
            if cargo test --workspace; then
                handle_success "工作空间集成测试通过"
            else
                handle_error "工作空间集成测试失败"
            fi
        else
            if cargo test --workspace --quiet 2>/dev/null; then
                handle_success "工作空间集成测试通过"
            else
                echo "  -> 详细测试输出:"
                if ! cargo test --workspace; then
                    handle_error "工作空间集成测试失败"
                fi
            fi
        fi
        TEST_COUNT=$((TEST_COUNT + 1))
        
        echo "$(blue '[4/4]') 基准测试编译检查..."
        run_benchmark "rustnum-benches" "crates/rustnum-benches"
        ;;
    *)
        red "❌ 未知的测试类型: $TEST_TYPE"
        echo "支持的测试类型: core, python, distributed, bench, unit, integration, all"
        exit 1
        ;;
esac

# 计算耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo
echo "$(cyan '=== 测试结果摘要 ===')"
echo "测试类型: $TEST_TYPE"
echo "总耗时: ${DURATION}秒"
echo "总测试数: $TEST_COUNT"
echo "通过数量: $PASSED_COUNT"
echo "失败数量: $FAILED_COUNT"
echo "警告数量: $WARNING_COUNT"

if [ $TEST_COUNT -gt 0 ]; then
    SUCCESS_RATE=$(( (PASSED_COUNT * 100) / TEST_COUNT ))
    echo "成功率: ${SUCCESS_RATE}%"
fi

echo

if [ $FAILED_COUNT -eq 0 ]; then
    echo "$(green '🚀 所有测试通过！')"
    echo
    echo "$(blue '项目状态:')"
    echo "  ✅ 模块隔离编译成功"
    echo "  ✅ 单元测试通过"
    echo "  ✅ 集成测试通过"
    echo "  ✅ 准备好进行功能开发"
    echo
    echo "$(blue '建议下一步:')"
    echo "  - 开始新功能开发"
    echo "  - 运行性能基准测试"
    echo "  - 更新文档和示例"
    echo "  - 准备发布版本"
    exit 0
else
    echo "$(red "❌ 发现 $FAILED_COUNT 个测试失败")"
    echo
    echo "$(blue '建议修复步骤:')"
    echo "  1. 查看上述失败的测试详情"
    echo "  2. 修复相关代码问题"
    echo "  3. 重新运行失败的测试: ./scripts/test_isolated.sh <test_type>"
    echo "  4. 运行完整测试验证: ./scripts/test_isolated.sh all"
    echo "  5. 参考 'docs/模块隔离编译规划方案.md' 获取帮助"
    exit 1
fi