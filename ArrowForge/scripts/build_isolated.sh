#!/bin/bash
set -e

# 设置环境变量以解决版本兼容性问题
export CARGO_RESOLVER_INCOMPATIBLE_RUST_VERSIONS=fallback

BUILD_TYPE=${1:-debug}
PARALLEL=${2:-true}
FEATURE_SET=${3:-minimal}

echo "=== RustNum 智能模块隔离编译 ==="
echo "构建类型: ${BUILD_TYPE}"
echo "并行编译: ${PARALLEL}"
echo "特性集合: ${FEATURE_SET}"
echo "Rust版本: $(rustc --version)"
echo

# 颜色输出函数
red() { echo -e "\033[31m$1\033[0m"; }
green() { echo -e "\033[32m$1\033[0m"; }
yellow() { echo -e "\033[33m$1\033[0m"; }
blue() { echo -e "\033[34m$1\033[0m"; }

# 错误处理函数
handle_error() {
    red "❌ 编译失败: $1"
    exit 1
}

# 成功处理函数
handle_success() {
    green "✅ $1"
}

# 编译参数设置
if [ "$BUILD_TYPE" = "release" ]; then
    BUILD_FLAGS="--release"
else
    BUILD_FLAGS=""
fi

# 特性集合配置
case "$FEATURE_SET" in
    "minimal")
        CORE_FEATURES="--no-default-features"
        ;;
    "standard")
        CORE_FEATURES="--features=openblas,simd"
        ;;
    "performance")
        CORE_FEATURES="--features=intel-mkl,simd,parallel"
        ;;
    "full")
        CORE_FEATURES="--all-features"
        ;;
    *)
        CORE_FEATURES="--no-default-features"
        ;;
esac

echo "$(blue '[第一阶段]') 编译核心模块 (rustnum-core)..."
cd crates/rustnum-core

echo "  -> 使用特性: ${CORE_FEATURES}"
if cargo build $BUILD_FLAGS $CORE_FEATURES; then
    handle_success "核心模块编译完成"
else
    handle_error "核心模块编译失败"
fi

# 运行核心模块测试
echo "  -> 运行核心模块测试..."
if cargo test $CORE_FEATURES --lib; then
    handle_success "核心模块测试通过"
else
    yellow "⚠️  核心模块测试有警告，但继续编译"
fi

cd ../..

echo
echo "$(blue '[第二阶段]') 编译功能模块..."

if [ "$PARALLEL" = "true" ]; then
    echo "  -> 启用并行编译模式"
    
    # Python绑定模块
    (
        echo "  -> 编译 rustnum-python..."
        cd crates/rustnum-python
        if cargo build $BUILD_FLAGS; then
            handle_success "rustnum-python 编译完成"
        else
            handle_error "rustnum-python 编译失败"
        fi
    ) &
    PYTHON_PID=$!
    
    # 分布式计算模块
    (
        echo "  -> 编译 rustnum-distributed..."
        cd crates/rustnum-distributed
        if cargo build $BUILD_FLAGS; then
            handle_success "rustnum-distributed 编译完成"
        else
            handle_error "rustnum-distributed 编译失败"
        fi
        
        # 运行分布式模块测试
        echo "  -> 运行分布式模块测试..."
        if cargo test; then
            handle_success "rustnum-distributed 测试通过"
        else
            yellow "⚠️  rustnum-distributed 测试有警告"
        fi
    ) &
    DISTRIBUTED_PID=$!
    
    # 基准测试模块
    (
        echo "  -> 检查 rustnum-benches..."
        cd crates/rustnum-benches
        if cargo check; then
            handle_success "rustnum-benches 检查完成"
        else
            handle_error "rustnum-benches 检查失败"
        fi
    ) &
    BENCHES_PID=$!
    
    # 等待所有并行任务完成
    echo "  -> 等待并行编译完成..."
    wait $PYTHON_PID || handle_error "Python绑定编译失败"
    wait $DISTRIBUTED_PID || handle_error "分布式模块编译失败"
    wait $BENCHES_PID || handle_error "基准测试检查失败"
    
else
    echo "  -> 使用串行编译模式"
    
    # Python绑定模块
    echo "  -> 编译 rustnum-python..."
    cd crates/rustnum-python
    if cargo build $BUILD_FLAGS; then
        handle_success "rustnum-python 编译完成"
    else
        handle_error "rustnum-python 编译失败"
    fi
    cd ../..
    
    # 分布式计算模块
    echo "  -> 编译 rustnum-distributed..."
    cd crates/rustnum-distributed
    if cargo build $BUILD_FLAGS; then
        handle_success "rustnum-distributed 编译完成"
    else
        handle_error "rustnum-distributed 编译失败"
    fi
    
    # 运行分布式模块测试
    echo "  -> 运行分布式模块测试..."
    if cargo test; then
        handle_success "rustnum-distributed 测试通过"
    else
        yellow "⚠️  rustnum-distributed 测试有警告"
    fi
    cd ../..
    
    # 基准测试模块
    echo "  -> 检查 rustnum-benches..."
    cd crates/rustnum-benches
    if cargo check; then
        handle_success "rustnum-benches 检查完成"
    else
        handle_error "rustnum-benches 检查失败"
    fi
    cd ../..
fi

echo
echo "$(blue '[第三阶段]') 集成验证..."
echo "  -> 运行工作空间集成测试..."
if cargo test --workspace; then
    handle_success "集成测试通过"
else
    yellow "⚠️  集成测试有警告，但编译成功"
fi

echo
echo "$(green '🎉 模块隔离编译完成！')"
echo "构建摘要:"
echo "  - 构建类型: ${BUILD_TYPE}"
echo "  - 特性集合: ${FEATURE_SET}"
echo "  - 并行模式: ${PARALLEL}"
echo "  - 所有模块编译成功"
echo
echo "$(blue '下一步:')"
echo "  - 运行 './scripts/quick_check.sh' 进行快速验证"
echo "  - 运行 './scripts/test_isolated.sh' 进行完整测试"
echo "  - 查看 'docs/模块隔离编译规划方案.md' 了解更多信息"
