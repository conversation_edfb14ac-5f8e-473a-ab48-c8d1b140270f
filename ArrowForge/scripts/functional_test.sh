#!/bin/bash

# RustNum 功能验证脚本
# 验证核心功能是否正常工作

set -e

echo "🚀 RustNum 功能验证测试开始..."
echo "=================================="

# 加载 Rust 环境
source ~/.cargo/env

# 进入项目根目录
cd "$(dirname "$0")/.."

echo
echo "📊 1. 编译验证"
echo "----------------"

# 验证核心模块编译
echo "✓ 验证核心模块编译..."
cd crates/rustnum-core
if cargo build --lib --no-default-features; then
    echo "✅ 核心模块编译成功"
else
    echo "❌ 核心模块编译失败"
    exit 1
fi
cd ../..

# 验证分布式模块编译
echo "✓ 验证分布式模块编译..."
cd crates/rustnum-distributed
if cargo build; then
    echo "✅ 分布式模块编译成功"
else
    echo "❌ 分布式模块编译失败"
    exit 1
fi
cd ../..

echo
echo "🧪 2. 单元测试验证"
echo "-------------------"

# 运行核心模块测试
echo "✓ 运行核心模块测试..."
cd crates/rustnum-core
if cargo test --lib; then
    echo "✅ 核心模块测试通过"
else
    echo "❌ 核心模块测试失败"
    exit 1
fi
cd ../..

# 运行分布式模块测试
echo "✓ 运行分布式模块测试..."
cd crates/rustnum-distributed
if cargo test; then
    echo "✅ 分布式模块测试通过"
else
    echo "❌ 分布式模块测试失败"
    exit 1
fi
cd ../..

echo
echo "⚡ 3. 性能验证"
echo "---------------"

# 创建简单的性能测试
echo "✓ 创建性能测试..."
cat > /tmp/rustnum_perf_test.rs << 'EOF'
use std::time::Instant;

fn main() {
    println!("🔥 RustNum 性能验证测试");
    
    // 测试基本数组操作
    let start = Instant::now();
    let mut data: Vec<f64> = (0..1000000).map(|i| i as f64).collect();
    
    // 简单的数学运算
    for i in 0..data.len() {
        data[i] = data[i] * 2.0 + 1.0;
    }
    
    let duration = start.elapsed();
    println!("✅ 百万元素数组运算耗时: {:?}", duration);
    
    // 测试内存分配
    let start = Instant::now();
    let mut vectors = Vec::new();
    for _ in 0..1000 {
        vectors.push(vec![0.0f64; 1000]);
    }
    let duration = start.elapsed();
    println!("✅ 百万元素内存分配耗时: {:?}", duration);
    
    println!("🎉 性能测试完成！");
}
EOF

# 编译并运行性能测试
if rustc /tmp/rustnum_perf_test.rs -o /tmp/rustnum_perf_test && /tmp/rustnum_perf_test; then
    echo "✅ 性能验证通过"
else
    echo "❌ 性能验证失败"
    exit 1
fi

echo
echo "🔧 4. 功能特性验证"
echo "-------------------"

# 验证 SIMD 特性
echo "✓ 验证 SIMD 特性..."
cd crates/rustnum-core
if cargo build --features="simd"; then
    echo "✅ SIMD 特性编译成功"
else
    echo "❌ SIMD 特性编译失败"
    exit 1
fi

# 验证优化特性
echo "✓ 验证优化特性..."
if cargo build --features="optimization"; then
    echo "✅ 优化特性编译成功"
else
    echo "❌ 优化特性编译失败"
    exit 1
fi

# 验证所有特性
echo "✓ 验证所有特性..."
if cargo build --all-features; then
    echo "✅ 所有特性编译成功"
else
    echo "❌ 所有特性编译失败"
    exit 1
fi
cd ../..

echo
echo "📈 5. 集成测试验证"
echo "-------------------"

# 运行工作空间级别的测试（排除 Python 绑定）
echo "✓ 运行集成测试..."
if cargo test --workspace --exclude rustnum-python --exclude rustnum-benches; then
    echo "✅ 集成测试通过"
else
    echo "❌ 集成测试失败"
    exit 1
fi

echo
echo "🎉 功能验证测试完成！"
echo "=================================="
echo
echo "📊 验证结果总结："
echo "✅ 模块编译：全部通过"
echo "✅ 单元测试：全部通过"
echo "✅ 性能验证：全部通过"
echo "✅ 功能特性：全部通过"
echo "✅ 集成测试：全部通过"
echo
echo "🚀 RustNum 项目功能验证成功！"
echo "   所有核心功能正常工作，可以开始进行功能开发。"

# 清理临时文件
rm -f /tmp/rustnum_perf_test.rs /tmp/rustnum_perf_test
