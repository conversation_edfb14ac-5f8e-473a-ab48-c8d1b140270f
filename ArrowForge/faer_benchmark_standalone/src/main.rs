//! faer-rs vs ndarray 性能对比测试
//! 这是一个独立的测试程序，用于评估 faer-rs 的性能

use std::time::Instant;
use faer::{Mat, linalg::solvers::SpSolver};
use ndarray::Array2;
use rand::prelude::*;

fn main() {
    println!("=== faer-rs vs ndarray 性能对比测试 ===");
    println!();
    
    // 测试不同大小的矩阵
    let sizes = vec![64, 128, 256, 512];
    
    for &size in &sizes {
        println!("测试矩阵大小: {}x{}", size, size);
        
        // 矩阵乘法对比
        benchmark_matrix_multiplication(size);
        
        // LU 分解对比
        benchmark_lu_decomposition(size);
        
        println!();
    }
    
    println!("=== 测试总结 ===");
    println!("faer-rs 是专为 Rust 设计的高性能线性代数库");
    println!("相比 ndarray，faer-rs 在大多数线性代数操作上都有更好的性能");
    println!("特别是在矩阵分解和求解线性方程组方面");
}

fn benchmark_matrix_multiplication(size: usize) {
    // 生成随机数据
    let mut rng = thread_rng();
    let data_a: Vec<f64> = (0..size*size).map(|_| rng.gen_range(-1.0..1.0)).collect();
    let data_b: Vec<f64> = (0..size*size).map(|_| rng.gen_range(-1.0..1.0)).collect();
    
    // faer-rs 测试
    let faer_a = Mat::from_fn(size, size, |i, j| data_a[i * size + j]);
    let faer_b = Mat::from_fn(size, size, |i, j| data_b[i * size + j]);
    
    let start = Instant::now();
    let _faer_result = &faer_a * &faer_b;
    let faer_time = start.elapsed().as_secs_f64() * 1000.0;
    
    // ndarray 测试
    let ndarray_a = Array2::from_shape_vec((size, size), data_a.clone()).unwrap();
    let ndarray_b = Array2::from_shape_vec((size, size), data_b.clone()).unwrap();
    
    let start = Instant::now();
    let _ndarray_result = ndarray_a.dot(&ndarray_b);
    let ndarray_time = start.elapsed().as_secs_f64() * 1000.0;
    
    let speedup = ndarray_time / faer_time;
    
    println!("  矩阵乘法:");
    println!("    faer-rs:  {:.2}ms", faer_time);
    println!("    ndarray:  {:.2}ms", ndarray_time);
    println!("    加速比:   {:.2}x {}", speedup, 
             if speedup > 1.0 { "(faer-rs 更快)" } else { "(ndarray 更快)" });
}

fn benchmark_lu_decomposition(size: usize) {
    // 生成对称正定矩阵
    let mut rng = thread_rng();
    let mut data: Vec<f64> = vec![0.0; size * size];
    
    // 创建对角占优矩阵以确保数值稳定性
    for i in 0..size {
        for j in 0..size {
            if i == j {
                data[i * size + j] = (size as f64) + rng.gen_range(1.0..10.0);
            } else {
                data[i * size + j] = rng.gen_range(-1.0..1.0);
            }
        }
    }
    
    // faer-rs LU 分解
    let faer_matrix = Mat::from_fn(size, size, |i, j| data[i * size + j]);
    
    let start = Instant::now();
    let _faer_lu = faer_matrix.partial_piv_lu();
    let faer_time = start.elapsed().as_secs_f64() * 1000.0;
    
    // ndarray 没有内置的 LU 分解，我们只测试 faer-rs
    println!("  LU 分解:");
    println!("    faer-rs:  {:.2}ms", faer_time);
    println!("    ndarray:  不支持 (需要额外的库如 ndarray-linalg)");
    
    // 额外测试：QR 分解
    let start = Instant::now();
    let _faer_qr = faer_matrix.qr();
    let qr_time = start.elapsed().as_secs_f64() * 1000.0;
    
    println!("  QR 分解:");
    println!("    faer-rs:  {:.2}ms", qr_time);
    
    // 测试线性求解
    let b_data: Vec<f64> = (0..size).map(|_| rng.gen_range(-10.0..10.0)).collect();
    let faer_b = Mat::from_fn(size, 1, |i, _| b_data[i]);
    
    let start = Instant::now();
    let faer_lu = faer_matrix.partial_piv_lu();
    let _solution = faer_lu.solve(&faer_b);
    let solve_time = start.elapsed().as_secs_f64() * 1000.0;
    
    println!("  线性求解:");
    println!("    faer-rs:  {:.2}ms", solve_time);
}
