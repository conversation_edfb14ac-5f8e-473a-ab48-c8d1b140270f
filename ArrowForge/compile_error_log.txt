warning: virtual workspace defaulting to `resolver = "1"` despite one or more workspace members being on edition 2021 which implies `resolver = "2"`
note: to keep the current resolver, specify `workspace.resolver = "1"` in the workspace root's manifest
note: to use the edition 2021 resolver, specify `workspace.resolver = "2"` in the workspace root's manifest
note: for more details see https://doc.rust-lang.org/cargo/reference/resolver.html#resolver-versions
       Fresh unicode-ident v1.0.18
       Fresh autocfg v1.5.0
       Fresh cfg-if v1.0.1
       Fresh once_cell v1.19.0
       Fresh stable_deref_trait v1.2.0
       Fresh shlex v1.3.0
       Fresh smallvec v1.15.1
       Fresh writeable v0.6.1
       Fresh vcpkg v0.2.15
       Fresh litemap v0.8.0
       Fresh proc-macro2 v1.0.95
       Fresh cc v1.2.27
       Fresh pkg-config v0.3.32
       Fresh bitflags v2.9.1
       Fresh foreign-types-shared v0.1.1
       Fresh zeroize v1.8.1
       Fresh openssl-probe v0.1.6
       Fresh utf8_iter v1.0.4
       Fresh adler2 v2.0.1
       Fresh percent-encoding v2.3.1
       Fresh linux-raw-sys v0.9.4
       Fresh crc32fast v1.4.2
       Fresh log v0.4.27
       Fresh rawpointer v0.2.1
       Fresh base64 v0.22.1
       Fresh option-ext v0.2.0
       Fresh scopeguard v1.2.0
       Fresh futures-sink v0.3.31
       Fresh pin-project-lite v0.2.16
       Fresh futures-core v0.3.31
       Fresh memchr v2.7.5
       Fresh slab v0.4.10
       Fresh futures-io v0.3.31
       Fresh heck v0.5.0
       Fresh futures-task v0.3.31
       Fresh gcc v0.3.55
       Fresh pin-utils v0.1.0
       Fresh either v1.15.0
       Fresh strength_reduce v0.2.4
       Fresh hashbrown v0.12.3
       Fresh itoa v1.0.15
       Fresh bytes v1.10.1
       Fresh unindent v0.2.4
       Fresh ryu v1.0.20
       Fresh crossbeam v0.2.12
       Fresh hashbrown v0.14.5
       Fresh indoc v2.0.6
       Fresh rustc-hash v2.1.1
       Fresh lazy_static v1.5.0
       Fresh lapack-src v0.9.0
       Fresh blas-src v0.9.0
       Fresh quote v1.0.40
       Fresh libc v0.2.174
       Fresh libm v0.2.15
       Fresh target-lexicon v0.13.2
       Fresh rustls-pki-types v1.12.0
       Fresh foreign-types v0.3.2
       Fresh form_urlencoded v1.2.1
       Fresh miniz_oxide v0.8.9
       Fresh futures-channel v0.3.31
       Fresh itertools v0.10.5
       Fresh raw-cpuid v11.5.0
       Fresh syn v2.0.104
       Fresh num-traits v0.2.19
       Fresh icu_properties_data v2.0.1
       Fresh icu_normalizer_data v2.0.0
       Fresh crossbeam-utils v0.8.21
       Fresh rustix v1.0.7
       Fresh getrandom v0.2.16
       Fresh zerocopy v0.8.26
       Fresh rustls-pemfile v2.2.0
       Fresh filetime v0.2.25
       Fresh flate2 v1.1.2
       Fresh dirs-sys v0.4.1
       Fresh matrixmultiply v0.3.10
       Fresh anyhow v1.0.98
       Fresh parking_lot_core v0.9.11
       Fresh lock_api v0.4.13
       Fresh paste v1.0.15
       Fresh rand v0.4.6
       Fresh lapack-sys v0.14.0
       Fresh getrandom v0.3.3
       Fresh signal-hook-registry v1.4.5
       Fresh blas-sys v0.7.1
       Fresh mio v1.0.4
       Fresh socket2 v0.5.10
       Fresh memoffset v0.9.1
       Fresh typenum v1.18.0
       Fresh indexmap v1.9.3
       Fresh cache-size v0.7.0
       Fresh num_cpus v1.17.0
       Fresh synstructure v0.13.2
       Fresh zerovec-derive v0.11.1
       Fresh displaydoc v0.2.5
       Fresh num-integer v0.1.46
       Fresh num-complex v0.4.6
       Fresh openssl-macros v0.1.1
       Fresh openssl-sys v0.9.109
       Fresh serde_derive v1.0.219
       Fresh xattr v1.5.0
       Fresh ppv-lite86 v0.2.21
       Fresh thiserror-impl v2.0.12
       Fresh rand_core v0.6.4
       Fresh rustls-native-certs v0.7.3
       Fresh crossbeam-epoch v0.9.18
       Fresh futures-macro v0.3.31
       Fresh dirs v5.0.1
       Fresh approx v0.5.1
       Fresh crossbeam-channel v0.5.15
       Fresh thiserror-impl v1.0.69
       Fresh libmimalloc-sys v0.1.43
       Fresh tokio-macros v2.5.0
       Fresh parking_lot v0.12.4
       Fresh rand v0.3.23
       Fresh noisy_float v0.2.0
       Fresh crossbeam-queue v0.3.12
       Fresh dashmap v5.5.3
       Fresh async-trait v0.1.88
       Fresh zerofrom-derive v0.1.6
       Fresh yoke-derive v0.8.0
       Fresh pyo3-build-config v0.25.1
       Fresh serde v1.0.219
       Fresh openssl v0.10.73
       Fresh num-bigint v0.4.6
       Fresh tar v0.4.44
       Fresh rand_chacha v0.3.1
       Fresh thiserror v2.0.12
       Fresh crossbeam-deque v0.8.6
       Fresh num-iter v0.1.45
       Fresh futures-util v0.3.31
       Fresh primal-check v0.3.4
       Fresh simba v0.9.0
       Fresh transpose v0.2.3
       Fresh tokio v1.45.1
       Fresh blas v0.22.0
       Fresh lapack v0.19.0
       Fresh ndarray v0.16.1
       Fresh mimalloc v0.1.47
       Fresh thiserror v1.0.69
       Fresh zerofrom v0.1.6
       Fresh native-tls v0.2.14
       Fresh num-rational v0.4.2
       Fresh rand v0.8.5
       Fresh rayon-core v1.12.1
       Fresh ndarray v0.15.6
       Fresh futures-executor v0.3.31
       Fresh serde_json v1.0.140
       Fresh crossbeam v0.8.4
       Fresh rustfft v6.4.0
       Fresh uuid v1.17.0
       Fresh rustlearn v0.5.0
       Fresh yoke v0.8.0
       Fresh rand_distr v0.4.3
       Fresh num v0.4.3
       Fresh rayon v1.10.0
       Fresh ndarray-stats v0.5.1
       Fresh nalgebra v0.33.2
       Fresh futures v0.3.31
       Fresh zerovec v0.11.2
       Fresh zerotrie v0.2.2
       Fresh pyo3-macros-backend v0.25.1
       Fresh pyo3-ffi v0.25.1
       Fresh smartcore v0.2.1
       Fresh tinystr v0.8.1
       Fresh potential_utf v0.1.2
       Fresh pyo3-macros v0.25.1
       Fresh icu_locale_core v2.0.0
       Fresh icu_collections v2.0.0
       Fresh pyo3 v0.25.1
       Fresh icu_provider v2.0.0
       Fresh numpy v0.25.0
       Fresh icu_properties v2.0.1
       Fresh icu_normalizer v2.0.0
       Fresh idna_adapter v1.2.1
       Fresh idna v1.0.3
       Fresh url v2.5.4
       Fresh ureq v2.12.1
       Fresh openblas-build v0.10.11
       Fresh openblas-src v0.10.11
   Compiling rustnum-core v0.1.0 (/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/crates/rustnum-core)
     Running `/home/<USER>/.rustup/toolchains/nightly-x86_64-unknown-linux-gnu/bin/rustc --crate-name rustnum_core --edition=2021 crates/rustnum-core/src/lib.rs --error-format=json --json=diagnostic-rendered-ansi,artifacts,future-incompat --crate-type lib --emit=dep-info,metadata,link -C embed-bitcode=no -C debuginfo=2 --cfg 'feature="async-trait"' --cfg 'feature="blas"' --cfg 'feature="blas-src"' --cfg 'feature="dashmap"' --cfg 'feature="default"' --cfg 'feature="distributed"' --cfg 'feature="futures"' --cfg 'feature="lapack"' --cfg 'feature="lapack-src"' --cfg 'feature="ml_scheduler"' --cfg 'feature="ndarray"' --cfg 'feature="ndarray-stats"' --cfg 'feature="openblas"' --cfg 'feature="openblas-src"' --cfg 'feature="optimization"' --cfg 'feature="rustfft"' --cfg 'feature="rustlearn"' --cfg 'feature="smartcore"' --cfg 'feature="tokio"' --cfg 'feature="uuid"' --check-cfg 'cfg(docsrs,test)' --check-cfg 'cfg(feature, values("async-trait", "blas", "blas-src", "dashmap", "default", "distributed", "futures", "intel-mkl-src", "lapack", "lapack-src", "mkl", "ml_scheduler", "ndarray", "ndarray-stats", "openblas", "openblas-src", "opencl", "opencl3", "optimization", "packed_simd", "rustfft", "rustlearn", "simd", "smartcore", "tokio", "uuid"))' -C metadata=d2f755093a5f2e31 -C extra-filename=-b69e154e6f51a654 --out-dir /home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps -C incremental=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/incremental -L dependency=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps --extern approx=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libapprox-70e9101cb2033557.rmeta --extern async_trait=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libasync_trait-78be5103ca69a275.so --extern blas=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libblas-6ed12ce2e3e255e7.rmeta --extern blas_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libblas_src-6bc1ad6719e8921b.rmeta --extern cache_size=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcache_size-a565c90eb8a23614.rmeta --extern cfg_if=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcfg_if-42dc2972d6f135a9.rmeta --extern crossbeam=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcrossbeam-c21ad1039a682f9f.rmeta --extern dashmap=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libdashmap-d3f6ddcc2c477c08.rmeta --extern futures=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libfutures-6f5754d93955b6d5.rmeta --extern lapack=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblapack-1f7d6698d2e5adeb.rmeta --extern lapack_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblapack_src-b0b63873a55c18de.rmeta --extern lazy_static=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblazy_static-87c02066b6621a70.rmeta --extern libc=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblibc-e8393a0cf6983fc5.rmeta --extern mimalloc=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libmimalloc-da6dae53d254fbce.rmeta --extern ndarray=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libndarray-e882b4b0768dfdc1.rmeta --extern ndarray_stats=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libndarray_stats-47a2f45855434288.rmeta --extern num_complex=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_complex-caf28a1d1b9d6183.rmeta --extern num_traits=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_traits-36a32ea4161082f7.rmeta --extern num_cpus=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_cpus-c8d3acf9597acf09.rmeta --extern openblas_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libopenblas_src-c33e54c1608d6644.rmeta --extern parking_lot=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libparking_lot-95e0b943fbf4f234.rmeta --extern rand_distr=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librand_distr-c6e247b14a4fa03c.rmeta --extern rayon=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librayon-02805c91402e5af5.rmeta --extern rustfft=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librustfft-83029f04fdd86740.rmeta --extern rustlearn=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librustlearn-9a8fe0428d510dd3.rmeta --extern serde=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libserde-d5591ef3a11a1cb6.rmeta --extern serde_json=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libserde_json-1cefaf0334a867ba.rmeta --extern smartcore=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libsmartcore-bb6c76c4642cca3b.rmeta --extern thiserror=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libthiserror-b413a2b99f6b7aa6.rmeta --extern tokio=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libtokio-811d87bf366c3ef9.rmeta --extern uuid=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libuuid-decf897d2eeb8344.rmeta -L native=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/build/libmimalloc-sys-d2b0e153a19b0228/out -L native=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/build/rustlearn-b4236c2cf0176577/out`
error[E0252]: the name `NonNull` is defined multiple times
 --> crates/rustnum-core/src/array/array_impl.rs:4:5
  |
1 | use std::ptr::NonNull;
  |     ----------------- previous import of the type `NonNull` here
...
4 | use std::ptr::NonNull;
  |     ^^^^^^^^^^^^^^^^^ `NonNull` reimported here
  |
  = note: `NonNull` must be defined only once in the type namespace of this module

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:180:17
    |
180 |     pub fn view(&self) -> ArrayView<'_, T> {
    |                 ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:191:18
    |
191 |     pub fn shape(&self) -> &[usize] {
    |                  ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:196:20
    |
196 |     pub fn strides(&self) -> &[isize] {
    |                    ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:201:26
    |
201 |     pub fn is_contiguous(&self) -> bool {
    |                          ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:206:18
    |
206 |     pub fn order(&self) -> StorageOrder {
    |                  ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:216:22
    |
216 |     pub fn transpose(&self) -> RustArray<T> {
    |                      ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error: `self` parameter is only allowed in associated functions
   --> crates/rustnum-core/src/array/array_impl.rs:229:20
    |
229 |     pub fn reshape(&self, new_shape: Vec<usize>) -> Result<RustArray<T>, RustNumError>
    |                    ^^^^^ not semantically valid as function parameter
    |
    = note: associated functions are those in `impl` or `trait` definitions

error[E0432]: unresolved import `super::array_impl`
 --> crates/rustnum-core/src/array/mod.rs:6:16
  |
6 | pub use super::array_impl::{RustArray, Buffer, Layout, StorageOrder};
  |                ^^^^^^^^^^ could not find `array_impl` in the crate root

error[E0412]: cannot find type `RwLock` in this scope
  --> crates/rustnum-core/src/array/array_impl.rs:72:22
   |
72 |     memory_pool: Arc<RwLock<MemoryPool>>,
   |                      ^^^^^^ not found in this scope
   |
help: consider importing one of these items
   |
1  + use std::sync::RwLock;
   |
1  + use parking_lot::RwLock;
   |
1  + use tokio::sync::RwLock;
   |

error[E0412]: cannot find type `RwLock` in this scope
  --> crates/rustnum-core/src/array/array_impl.rs:80:15
   |
80 |     pool: Arc<RwLock<MemoryPool>>,
   |               ^^^^^^ not found in this scope
   |
help: consider importing one of these items
   |
1  + use std::sync::RwLock;
   |
1  + use parking_lot::RwLock;
   |
1  + use tokio::sync::RwLock;
   |

error[E0412]: cannot find type `RwLock` in this scope
  --> crates/rustnum-core/src/array/array_impl.rs:84:57
   |
84 |     pub fn new(size: usize, alignment: usize, pool: Arc<RwLock<MemoryPool>>) -> Result<Self, RustNumError> {
   |                                                         ^^^^^^ not found in this scope
   |
help: consider importing one of these items
   |
1  + use std::sync::RwLock;
   |
1  + use parking_lot::RwLock;
   |
1  + use tokio::sync::RwLock;
   |

error[E0412]: cannot find type `RwLock` in this scope
   --> crates/rustnum-core/src/array/array_impl.rs:151:71
    |
151 |     pub fn new(shape: &[usize], order: StorageOrder, memory_pool: Arc<RwLock<MemoryPool>>) -> Result<Self, RustNumError> {
    |                                                                       ^^^^^^ not found in this scope
    |
help: consider importing one of these items
    |
1   + use std::sync::RwLock;
    |
1   + use parking_lot::RwLock;
    |
1   + use tokio::sync::RwLock;
    |

error[E0412]: cannot find type `T` in this scope
   --> crates/rustnum-core/src/array/array_impl.rs:180:41
    |
180 |     pub fn view(&self) -> ArrayView<'_, T> {
    |                                         ^ not found in this scope
    |
help: you might be missing a type parameter
    |
180 |     pub fn view<T>(&self) -> ArrayView<'_, T> {
    |                +++

error[E0412]: cannot find type `T` in this scope
   --> crates/rustnum-core/src/array/array_impl.rs:216:42
    |
216 |     pub fn transpose(&self) -> RustArray<T> {
    |                                          ^ not found in this scope
    |
help: you might be missing a type parameter
    |
216 |     pub fn transpose<T>(&self) -> RustArray<T> {
    |                     +++

error[E0412]: cannot find type `T` in this scope
   --> crates/rustnum-core/src/array/array_impl.rs:231:9
    |
231 |         T: Copy + Default,
    |         ^ not found in this scope
    |
help: you might be missing a type parameter
    |
229 |     pub fn reshape<T>(&self, new_shape: Vec<usize>) -> Result<RustArray<T>, RustNumError>
    |                   +++

error[E0412]: cannot find type `T` in this scope
   --> crates/rustnum-core/src/array/array_impl.rs:229:70
    |
229 |     pub fn reshape(&self, new_shape: Vec<usize>) -> Result<RustArray<T>, RustNumError>
    |                                                                      ^ not found in this scope
    |
help: you might be missing a type parameter
    |
229 |     pub fn reshape<T>(&self, new_shape: Vec<usize>) -> Result<RustArray<T>, RustNumError>
    |                   +++

warning: unused import: `std::ptr::NonNull`
 --> crates/rustnum-core/src/array/array_impl.rs:4:5
  |
4 | use std::ptr::NonNull;
  |     ^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

Some errors have detailed explanations: E0252, E0412, E0432.
For more information about an error, try `rustc --explain E0252`.
warning: `rustnum-core` (lib) generated 1 warning
error: could not compile `rustnum-core` (lib) due to 17 previous errors; 1 warning emitted

Caused by:
  process didn't exit successfully: `/home/<USER>/.rustup/toolchains/nightly-x86_64-unknown-linux-gnu/bin/rustc --crate-name rustnum_core --edition=2021 crates/rustnum-core/src/lib.rs --error-format=json --json=diagnostic-rendered-ansi,artifacts,future-incompat --crate-type lib --emit=dep-info,metadata,link -C embed-bitcode=no -C debuginfo=2 --cfg 'feature="async-trait"' --cfg 'feature="blas"' --cfg 'feature="blas-src"' --cfg 'feature="dashmap"' --cfg 'feature="default"' --cfg 'feature="distributed"' --cfg 'feature="futures"' --cfg 'feature="lapack"' --cfg 'feature="lapack-src"' --cfg 'feature="ml_scheduler"' --cfg 'feature="ndarray"' --cfg 'feature="ndarray-stats"' --cfg 'feature="openblas"' --cfg 'feature="openblas-src"' --cfg 'feature="optimization"' --cfg 'feature="rustfft"' --cfg 'feature="rustlearn"' --cfg 'feature="smartcore"' --cfg 'feature="tokio"' --cfg 'feature="uuid"' --check-cfg 'cfg(docsrs,test)' --check-cfg 'cfg(feature, values("async-trait", "blas", "blas-src", "dashmap", "default", "distributed", "futures", "intel-mkl-src", "lapack", "lapack-src", "mkl", "ml_scheduler", "ndarray", "ndarray-stats", "openblas", "openblas-src", "opencl", "opencl3", "optimization", "packed_simd", "rustfft", "rustlearn", "simd", "smartcore", "tokio", "uuid"))' -C metadata=d2f755093a5f2e31 -C extra-filename=-b69e154e6f51a654 --out-dir /home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps -C incremental=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/incremental -L dependency=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps --extern approx=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libapprox-70e9101cb2033557.rmeta --extern async_trait=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libasync_trait-78be5103ca69a275.so --extern blas=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libblas-6ed12ce2e3e255e7.rmeta --extern blas_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libblas_src-6bc1ad6719e8921b.rmeta --extern cache_size=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcache_size-a565c90eb8a23614.rmeta --extern cfg_if=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcfg_if-42dc2972d6f135a9.rmeta --extern crossbeam=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libcrossbeam-c21ad1039a682f9f.rmeta --extern dashmap=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libdashmap-d3f6ddcc2c477c08.rmeta --extern futures=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libfutures-6f5754d93955b6d5.rmeta --extern lapack=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblapack-1f7d6698d2e5adeb.rmeta --extern lapack_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblapack_src-b0b63873a55c18de.rmeta --extern lazy_static=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblazy_static-87c02066b6621a70.rmeta --extern libc=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/liblibc-e8393a0cf6983fc5.rmeta --extern mimalloc=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libmimalloc-da6dae53d254fbce.rmeta --extern ndarray=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libndarray-e882b4b0768dfdc1.rmeta --extern ndarray_stats=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libndarray_stats-47a2f45855434288.rmeta --extern num_complex=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_complex-caf28a1d1b9d6183.rmeta --extern num_traits=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_traits-36a32ea4161082f7.rmeta --extern num_cpus=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libnum_cpus-c8d3acf9597acf09.rmeta --extern openblas_src=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libopenblas_src-c33e54c1608d6644.rmeta --extern parking_lot=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libparking_lot-95e0b943fbf4f234.rmeta --extern rand_distr=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librand_distr-c6e247b14a4fa03c.rmeta --extern rayon=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librayon-02805c91402e5af5.rmeta --extern rustfft=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librustfft-83029f04fdd86740.rmeta --extern rustlearn=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/librustlearn-9a8fe0428d510dd3.rmeta --extern serde=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libserde-d5591ef3a11a1cb6.rmeta --extern serde_json=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libserde_json-1cefaf0334a867ba.rmeta --extern smartcore=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libsmartcore-bb6c76c4642cca3b.rmeta --extern thiserror=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libthiserror-b413a2b99f6b7aa6.rmeta --extern tokio=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libtokio-811d87bf366c3ef9.rmeta --extern uuid=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/deps/libuuid-decf897d2eeb8344.rmeta -L native=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/build/libmimalloc-sys-d2b0e153a19b0228/out -L native=/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/target/debug/build/rustlearn-b4236c2cf0176577/out` (exit status: 1)
