warning: virtual workspace defaulting to `resolver = "1"` despite one or more workspace members being on edition 2021 which implies `resolver = "2"`
note: to keep the current resolver, specify `workspace.resolver = "1"` in the workspace root's manifest
note: to use the edition 2021 resolver, specify `workspace.resolver = "2"` in the workspace root's manifest
note: for more details see https://doc.rust-lang.org/cargo/reference/resolver.html#resolver-versions
   Compiling rustnum-core v0.1.0 (/home/<USER>/CascadeProjects/ArrowSciCompute/RustNum/crates/rustnum-core)
error[E0432]: unresolved import `libc`
 --> crates/rustnum-core/src/backend/bindings.rs:3:5
  |
3 | use libc::{c_int, c_double, c_float, c_char};
  |     ^^^^ use of unresolved module or unlinked crate `libc`
  |
  = help: if you wanted to use a crate named `libc`, use `cargo add libc` to add it to your `Cargo.toml`

error[E0432]: unresolved import `crate::error::Result`
 --> crates/rustnum-core/src/backend/bindings.rs:2:5
  |
2 | use crate::error::Result;
  |     ^^^^^^^^^^^^^^^^^^^^ no `Result` in `error`
  |
help: consider importing one of these items instead
  |
2 - use crate::error::Result;
2 + use std::fmt::Result;
  |
2 - use crate::error::Result;
2 + use std::io::Result;
  |
2 - use crate::error::Result;
2 + use std::result::Result;
  |
2 - use crate::error::Result;
2 + use std::thread::Result;
  |
    and 5 other candidates

error[E0432]: unresolved import `crate::error::Result`
 --> crates/rustnum-core/src/backend/blas_lapack.rs:2:5
  |
2 | use crate::error::Result;
  |     ^^^^^^^^^^^^^^^^^^^^ no `Result` in `error`
  |
help: consider importing one of these items instead
  |
2 - use crate::error::Result;
2 + use std::fmt::Result;
  |
2 - use crate::error::Result;
2 + use std::io::Result;
  |
2 - use crate::error::Result;
2 + use std::result::Result;
  |
2 - use crate::error::Result;
2 + use std::thread::Result;
  |
    and 5 other candidates

error[E0432]: unresolved import `memory::Memory`
  --> crates/rustnum-core/src/lib.rs:16:9
   |
16 | pub use memory::Memory; // Assuming Memory is in allocator.rs based on the directory structure
   |         ^^^^^^^^^^^^^^ no `Memory` in `memory`

error[E0433]: failed to resolve: use of undeclared type `Error`
   --> crates/rustnum-core/src/backend/bindings.rs:111:24
    |
111 |             return Err(Error::DimensionError("Vectors must have same length".into()));
    |                        ^^^^^ use of undeclared type `Error`
    |
help: consider importing one of these items
    |
1   + use std::error::Error;
    |
1   + use std::fmt::Error;
    |
1   + use std::io::Error;
    |
1   + use core::error::Error;
    |
      and 9 other candidates

error[E0433]: failed to resolve: use of undeclared type `Error`
   --> crates/rustnum-core/src/backend/bindings.rs:234:17
    |
234 |             Err(Error::ComputationError(format!("SVD failed with error code {}", info)))
    |                 ^^^^^ use of undeclared type `Error`
    |
help: consider importing one of these items
    |
1   + use std::error::Error;
    |
1   + use std::fmt::Error;
    |
1   + use std::io::Error;
    |
1   + use core::error::Error;
    |
      and 9 other candidates

error[E0412]: cannot find type `CpuFeatures` in this scope
   --> crates/rustnum-core/src/backend/config.rs:129:29
    |
129 | fn detect_cpu_features() -> CpuFeatures {
    |                             ^^^^^^^^^^^ not found in this scope

error[E0422]: cannot find struct, variant or union type `CpuFeatures` in this scope
   --> crates/rustnum-core/src/backend/config.rs:130:5
    |
130 |     CpuFeatures {
    |     ^^^^^^^^^^^ not found in this scope

warning: unused import: `DefaultSimdExecutor`
 --> crates/rustnum-core/src/simd/mod.rs:2:60
  |
2 | use crate::simd::simd::{SimdElement, SimdOp, SimdExecutor, DefaultSimdExecutor};
  |                                                            ^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `parking_lot::RwLock`
 --> crates/rustnum-core/src/sparse/decomposition.rs:3:5
  |
3 | use parking_lot::RwLock;
  |     ^^^^^^^^^^^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> crates/rustnum-core/src/sparse/decomposition.rs:5:5
  |
5 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `CscMatrix`
 --> crates/rustnum-core/src/sparse/parallel_ops.rs:1:41
  |
1 | use crate::sparse::storage::{CsrMatrix, CscMatrix};
  |                                         ^^^^^^^^^

warning: unused import: `std::sync::Arc`
 --> crates/rustnum-core/src/sparse/qr.rs:5:5
  |
5 | use std::sync::Arc;
  |     ^^^^^^^^^^^^^^

warning: unused import: `crate::RustArray`
 --> crates/rustnum-core/src/solvers/iterative.rs:1:5
  |
1 | use crate::RustArray;
  |     ^^^^^^^^^^^^^^^^

warning: unused import: `super::*`
 --> crates/rustnum-core/src/backend/bindings.rs:1:5
  |
1 | use super::*;
  |     ^^^^^^^^

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:5:1
   |
5  |   /// BLAS Level 1 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
6  | / extern "C" {
7  | |     // 向量点积
8  | |     fn ddot_(
9  | |         n: *const c_int,
...  |
24 | |     );
25 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment
   = note: `#[warn(unused_doc_comments)]` on by default

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:27:1
   |
27 |   /// BLAS Level 2 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
28 | / extern "C" {
29 | |     // 矩阵向量乘法
30 | |     fn dgemv_(
31 | |         trans: *const c_char,
...  |
42 | |     );
43 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment

warning: unused doc comment
  --> crates/rustnum-core/src/backend/bindings.rs:45:1
   |
45 |   /// BLAS Level 3 函数签名
   |   ^^^^^^^^^^^^^^^^^^^^^^^^^
46 | / extern "C" {
47 | |     // 矩阵乘法
48 | |     fn dgemm_(
49 | |         transa: *const c_char,
...  |
62 | |     );
63 | | }
   | |_- rustdoc does not generate documentation for extern blocks
   |
   = help: use `//` for a plain comment

warning: unused doc comment
   --> crates/rustnum-core/src/backend/bindings.rs:65:1
    |
65  |   /// LAPACK函数签名
    |   ^^^^^^^^^^^^^^^^^^
66  | / extern "C" {
67  | |     // SVD分解
68  | |     fn dgesvd_(
69  | |         jobu: *const c_char,
...   |
101 | |     );
102 | | }
    | |_- rustdoc does not generate documentation for extern blocks
    |
    = help: use `//` for a plain comment

warning: unused import: `super::*`
 --> crates/rustnum-core/src/backend/blas_lapack.rs:1:5
  |
1 | use super::*;
  |     ^^^^^^^^

warning: unexpected `cfg` condition value: `cuda`
   --> crates/rustnum-core/src/backend/config.rs:139:7
    |
139 | #[cfg(feature = "cuda")]
    |       ^^^^^^^^^^^^^^^^
    |
    = note: expected values for `feature` are: `async-trait`, `blas`, `blas-src`, `dashmap`, `default`, `distributed`, `futures`, `intel-mkl-src`, `lapack`, `lapack-src`, `mkl`, `ml_scheduler`, `ndarray`, `ndarray-stats`, `openblas`, `openblas-src`, `opencl`, `opencl3`, `optimization`, `packed_simd`, `rustfft`, `rustlearn`, `simd`, `smartcore`, `tokio`, and `uuid`
    = help: consider adding `cuda` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration
    = note: `#[warn(unexpected_cfgs)]` on by default

warning: unexpected `cfg` condition value: `cuda`
   --> crates/rustnum-core/src/backend/config.rs:111:29
    |
111 |         let has_cuda = cfg!(feature = "cuda") && cuda_is_available();
    |                             ^^^^^^^^^^^^^^^^
    |
    = note: expected values for `feature` are: `async-trait`, `blas`, `blas-src`, `dashmap`, `default`, `distributed`, `futures`, `intel-mkl-src`, `lapack`, `lapack-src`, `mkl`, `ml_scheduler`, `ndarray`, `ndarray-stats`, `openblas`, `openblas-src`, `opencl`, `opencl3`, `optimization`, `packed_simd`, `rustfft`, `rustlearn`, `simd`, `smartcore`, `tokio`, and `uuid`
    = help: consider adding `cuda` as a feature in `Cargo.toml`
    = note: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration

error[E0599]: no function or associated item named `extract_dense_block` found for struct `SparseLU` in the current scope
   --> crates/rustnum-core/src/sparse/decomposition.rs:173:37
    |
111 | pub struct SparseLU {
    | ------------------- function or associated item `extract_dense_block` not found for this struct
...
173 |         let mut dense_block = Self::extract_dense_block(snode, matrix)?;
    |                                     ^^^^^^^^^^^^^^^^^^^ function or associated item not found in `SparseLU`
    |
note: if you're trying to build a new `SparseLU`, consider using `SparseLU::lu_decompose` which returns `Result<SparseLU, RustNumError>`
   --> crates/rustnum-core/src/sparse/decomposition.rs:118:5
    |
118 |     pub fn lu_decompose(matrix: &CsrMatrix<f64>) -> Result<Self, RustNumError> {
    |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

error[E0599]: no variant or associated item named `Singular` found for enum `RustNumError` in the current scope
   --> crates/rustnum-core/src/sparse/qr.rs:140:46
    |
140 |                     return Err(RustNumError::Singular);
    |                                              ^^^^^^^^ variant or associated item not found in `RustNumError`
    |
   ::: crates/rustnum-core/src/error.rs:4:1
    |
4   | pub enum RustNumError {
    | --------------------- variant or associated item `Singular` not found for this enum

error[E0599]: no method named `get` found for reference `&RustArray<T>` in the current scope
   --> crates/rustnum-core/src/sparse/storage.rs:112:42
    |
112 |                 if let Some(val) = dense.get(&[i, j]) {
    |                                          ^^^ method not found in `&RustArray<T>`
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `get`, perhaps you need to implement one of them:
            candidate #1: `SliceIndex`
            candidate #2: `ndarray::IndexLonger`
            candidate #3: `rustlearn::prelude::IndexableMatrix`
            candidate #4: `smartcore::linalg::BaseMatrix`
            candidate #5: `smartcore::linalg::BaseVector`

error[E0599]: no method named `set` found for struct `RustArray` in the current scope
   --> crates/rustnum-core/src/sparse/storage.rs:140:24
    |
140 |                  dense.set(&[i, j], T::default())?;
    |                        ^^^ method not found in `RustArray<_>`
    |
   ::: crates/rustnum-core/src/array/mod.rs:37:1
    |
37  | pub struct RustArray<T> {
    | ----------------------- method `set` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `set`, perhaps you need to implement one of them:
            candidate #1: `bitflags::Flags`
            candidate #2: `rustlearn::prelude::IndexableMatrix`
            candidate #3: `smartcore::linalg::BaseMatrix`
            candidate #4: `smartcore::linalg::BaseVector`

error[E0599]: no method named `set` found for struct `RustArray` in the current scope
   --> crates/rustnum-core/src/sparse/storage.rs:150:23
    |
150 |                 dense.set(&[i, j], self.values[k].clone())?;
    |                       ^^^ method not found in `RustArray<_>`
    |
   ::: crates/rustnum-core/src/array/mod.rs:37:1
    |
37  | pub struct RustArray<T> {
    | ----------------------- method `set` not found for this struct
    |
    = help: items from traits can only be used if the trait is implemented and in scope
    = note: the following traits define an item `set`, perhaps you need to implement one of them:
            candidate #1: `bitflags::Flags`
            candidate #2: `rustlearn::prelude::IndexableMatrix`
            candidate #3: `smartcore::linalg::BaseMatrix`
            candidate #4: `smartcore::linalg::BaseVector`

error[E0425]: cannot find function `cuda_is_available` in this scope
   --> crates/rustnum-core/src/backend/config.rs:111:50
    |
111 |         let has_cuda = cfg!(feature = "cuda") && cuda_is_available();
    |                                                  ^^^^^^^^^^^^^^^^^ not found in this scope

error[E0425]: cannot find function `mkl_is_available` in this scope
   --> crates/rustnum-core/src/backend/config.rs:114:48
    |
114 |         let has_mkl = cfg!(feature = "mkl") && mkl_is_available();
    |                                                ^^^^^^^^^^^^^^^^ not found in this scope

warning: unused import: `SimdExecutor`
 --> crates/rustnum-core/src/simd/mod.rs:2:46
  |
2 | use crate::simd::simd::{SimdElement, SimdOp, SimdExecutor, DefaultSimdExecutor};
  |                                              ^^^^^^^^^^^^

warning: unused variable: `b`
   --> crates/rustnum-core/src/sparse/decomposition.rs:145:25
    |
145 |     pub fn solve(&self, b: &[f64]) -> Result<Vec<f64>, RustNumError> {
    |                         ^ help: if this is intentional, prefix it with an underscore: `_b`
    |
    = note: `#[warn(unused_variables)]` on by default

warning: unused variable: `supernodes`
   --> crates/rustnum-core/src/sparse/decomposition.rs:150:49
    |
150 |     pub fn preallocate(matrix: &CsrMatrix<f64>, supernodes: &[Supernode]) 
    |                                                 ^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_supernodes`

warning: unused variable: `pattern_size`
   --> crates/rustnum-core/src/sparse/decomposition.rs:239:13
    |
239 |         let pattern_size = snode.nonzero_pattern.len();
    |             ^^^^^^^^^^^^ help: if this is intentional, prefix it with an underscore: `_pattern_size`

Some errors have detailed explanations: E0412, E0422, E0425, E0432, E0433, E0599.
For more information about an error, try `rustc --explain E0412`.
warning: `rustnum-core` (lib) generated 18 warnings
error: could not compile `rustnum-core` (lib) due to 15 previous errors; 18 warnings emitted
