//! 线性代数性能测试
//! 
//! 测试高性能线性代数运算的功能和性能

use std::time::Instant;

// 模拟高性能线性代数操作
mod linalg_test {
    use std::time::Instant;
    
    #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
    pub struct Matrix<T> {
        data: Vec<T>,
        rows: usize,
        cols: usize,
    }
    
    #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
    pub enum Strategy {
        Scalar,
        Blocked { block_size: usize },
        Auto,
    }
    
    impl<T> Matrix<T> 
    where 
        T: Copy + Default + std::ops::Add<Output = T> + std::ops::Sub<Output = T> + 
           std::ops::Mul<Output = T> + std::ops::Div<Output = T> + PartialEq + PartialOrd
    {
        pub fn new(rows: usize, cols: usize) -> Self {
            Self {
                data: vec![T::default(); rows * cols],
                rows,
                cols,
            }
        }
        
        pub fn fill(rows: usize, cols: usize, value: T) -> Self {
            Self {
                data: vec![value; rows * cols],
                rows,
                cols,
            }
        }
        
        pub fn shape(&self) -> (usize, usize) {
            (self.rows, self.cols)
        }
        
        pub fn data(&self) -> &[T] {
            &self.data
        }
        
        pub fn data_mut(&mut self) -> &mut [T] {
            &mut self.data
        }
        
        // 标量矩阵乘法
        pub fn matmul_scalar(&self, other: &Self) -> Result<Self, String> {
            if self.cols != other.rows {
                return Err(format!("Shape mismatch: ({}, {}) x ({}, {})", 
                                 self.rows, self.cols, other.rows, other.cols));
            }
            
            let mut result = Self::new(self.rows, other.cols);
            let result_data = result.data_mut();
            
            for i in 0..self.rows {
                for j in 0..other.cols {
                    let mut sum = T::default();
                    for k in 0..self.cols {
                        sum = sum + self.data[i * self.cols + k] * other.data[k * other.cols + j];
                    }
                    result_data[i * other.cols + j] = sum;
                }
            }
            
            Ok(result)
        }
        
        // 分块矩阵乘法
        pub fn matmul_blocked(&self, other: &Self, block_size: usize) -> Result<Self, String> {
            if self.cols != other.rows {
                return Err(format!("Shape mismatch: ({}, {}) x ({}, {})", 
                                 self.rows, self.cols, other.rows, other.cols));
            }
            
            let mut result = Self::new(self.rows, other.cols);
            let result_data = result.data_mut();
            
            // 分块矩阵乘法以提高缓存效率
            for i0 in (0..self.rows).step_by(block_size) {
                let i_end = (i0 + block_size).min(self.rows);
                for j0 in (0..other.cols).step_by(block_size) {
                    let j_end = (j0 + block_size).min(other.cols);
                    for k0 in (0..self.cols).step_by(block_size) {
                        let k_end = (k0 + block_size).min(self.cols);
                        
                        // 计算当前块
                        for i in i0..i_end {
                            for j in j0..j_end {
                                let mut sum = result_data[i * other.cols + j];
                                for k in k0..k_end {
                                    sum = sum + self.data[i * self.cols + k] * other.data[k * other.cols + j];
                                }
                                result_data[i * other.cols + j] = sum;
                            }
                        }
                    }
                }
            }
            
            Ok(result)
        }
        
        // 智能矩阵乘法
        pub fn matmul(&self, other: &Self, strategy: Strategy) -> Result<Self, String> {
            let actual_strategy = match strategy {
                Strategy::Auto => {
                    let size = self.rows * self.cols * other.cols;
                    if size > 100000 {
                        Strategy::Blocked { block_size: 64 }
                    } else if size > 10000 {
                        Strategy::Blocked { block_size: 32 }
                    } else {
                        Strategy::Scalar
                    }
                }
                s => s,
            };
            
            match actual_strategy {
                Strategy::Scalar => self.matmul_scalar(other),
                Strategy::Blocked { block_size } => self.matmul_blocked(other, block_size),
                Strategy::Auto => unreachable!(),
            }
        }
        
        // 矩阵转置
        pub fn transpose(&self) -> Self {
            let mut result = Self::new(self.cols, self.rows);
            let result_data = result.data_mut();
            
            for i in 0..self.rows {
                for j in 0..self.cols {
                    result_data[j * self.rows + i] = self.data[i * self.cols + j];
                }
            }
            
            result
        }
        
        // 分块转置
        pub fn transpose_blocked(&self, block_size: usize) -> Self {
            let mut result = Self::new(self.cols, self.rows);
            let result_data = result.data_mut();
            
            for i0 in (0..self.rows).step_by(block_size) {
                let i_end = (i0 + block_size).min(self.rows);
                for j0 in (0..self.cols).step_by(block_size) {
                    let j_end = (j0 + block_size).min(self.cols);
                    
                    for i in i0..i_end {
                        for j in j0..j_end {
                            result_data[j * self.rows + i] = self.data[i * self.cols + j];
                        }
                    }
                }
            }
            
            result
        }
        
        // 向量内积
        pub fn dot(&self, other: &Self) -> Result<T, String> {
            if self.rows != 1 || other.rows != 1 || self.cols != other.cols {
                return Err("Dot product requires 1D vectors of same length".into());
            }
            
            let mut result = T::default();
            for i in 0..self.cols {
                result = result + self.data[i] * other.data[i];
            }
            
            Ok(result)
        }
        
        // 矩阵向量乘法
        pub fn matvec(&self, vec: &Self) -> Result<Self, String> {
            if vec.rows != 1 || self.cols != vec.cols {
                return Err("Matrix-vector multiplication shape mismatch".into());
            }
            
            let mut result = Self::new(self.rows, 1);
            let result_data = result.data_mut();
            
            for i in 0..self.rows {
                let mut sum = T::default();
                for j in 0..self.cols {
                    sum = sum + self.data[i * self.cols + j] * vec.data[j];
                }
                result_data[i] = sum;
            }
            
            Ok(result)
        }
    }
    
    // 性能基准测试
    pub fn benchmark_matrix_operations() {
        println!("🚀 线性代数性能基准测试");
        println!("========================");
        
        // 测试不同大小的矩阵
        let sizes = vec![64, 128, 256, 512];
        
        for &size in &sizes {
            println!("\n📏 矩阵大小: {}x{}", size, size);
            
            let a = Matrix::<f32>::fill(size, size, 2.0);
            let b = Matrix::<f32>::fill(size, size, 3.0);
            
            // 测试标量矩阵乘法
            let start = Instant::now();
            let _result_scalar = a.matmul(&b, Strategy::Scalar).unwrap();
            let scalar_time = start.elapsed();
            
            // 测试分块矩阵乘法
            let start = Instant::now();
            let _result_blocked = a.matmul(&b, Strategy::Blocked { block_size: 32 }).unwrap();
            let blocked_time = start.elapsed();
            
            // 测试自动选择
            let start = Instant::now();
            let _result_auto = a.matmul(&b, Strategy::Auto).unwrap();
            let auto_time = start.elapsed();
            
            println!("   📊 矩阵乘法性能:");
            println!("      标量实现: {:?}", scalar_time);
            println!("      分块实现: {:?}", blocked_time);
            println!("      自动选择: {:?}", auto_time);
            
            if blocked_time < scalar_time {
                let speedup = scalar_time.as_nanos() as f64 / blocked_time.as_nanos() as f64;
                println!("      🚀 分块加速比: {:.2}x", speedup);
            }
            
            // 测试转置
            let start = Instant::now();
            let _transposed = a.transpose();
            let transpose_time = start.elapsed();
            
            let start = Instant::now();
            let _transposed_blocked = a.transpose_blocked(32);
            let transpose_blocked_time = start.elapsed();
            
            println!("   📊 矩阵转置性能:");
            println!("      标准转置: {:?}", transpose_time);
            println!("      分块转置: {:?}", transpose_blocked_time);
            
            if transpose_blocked_time < transpose_time {
                let speedup = transpose_time.as_nanos() as f64 / transpose_blocked_time.as_nanos() as f64;
                println!("      🚀 分块转置加速比: {:.2}x", speedup);
            }
        }
        
        println!("\n🎉 性能测试完成！");
    }
}

fn main() {
    use linalg_test::*;
    
    println!("🎯 RustNum 线性代数功能验证测试");
    println!("==============================");
    println!();
    
    // 功能正确性测试
    println!("✅ 1. 功能正确性验证");
    
    // 测试矩阵乘法
    let a = Matrix::<f32>::fill(2, 3, 2.0);
    let b = Matrix::<f32>::fill(3, 2, 3.0);
    
    let result = a.matmul(&b, Strategy::Scalar).unwrap();
    println!("   矩阵乘法 (2x3) * (3x2) = (2x2)");
    println!("   结果形状: {:?}", result.shape());
    println!("   期望值: 18.0, 实际值: {:.1}", result.data()[0]);
    
    // 验证结果
    for &val in result.data() {
        assert!((val - 18.0).abs() < 1e-6);
    }
    println!("   ✅ 矩阵乘法正确");
    
    // 测试转置
    let matrix = Matrix::<f32>::fill(2, 3, 5.0);
    let transposed = matrix.transpose();
    println!("   矩阵转置 (2x3) -> (3x2)");
    println!("   转置后形状: {:?}", transposed.shape());
    assert_eq!(transposed.shape(), (3, 2));
    println!("   ✅ 矩阵转置正确");
    
    // 测试向量内积
    let vec_a = Matrix::<f32>::fill(1, 4, 2.0);
    let vec_b = Matrix::<f32>::fill(1, 4, 3.0);
    let dot_result = vec_a.dot(&vec_b).unwrap();
    println!("   向量内积: 2*3*4 = {:.1}", dot_result);
    assert!((dot_result - 24.0).abs() < 1e-6);
    println!("   ✅ 向量内积正确");
    
    // 测试矩阵向量乘法
    let matrix = Matrix::<f32>::fill(2, 3, 2.0);
    let vector = Matrix::<f32>::fill(1, 3, 3.0);
    let matvec_result = matrix.matvec(&vector).unwrap();
    println!("   矩阵向量乘法 (2x3) * (3x1) = (2x1)");
    println!("   结果形状: {:?}", matvec_result.shape());
    assert_eq!(matvec_result.shape(), (2, 1));
    for &val in matvec_result.data() {
        assert!((val - 18.0).abs() < 1e-6);
    }
    println!("   ✅ 矩阵向量乘法正确");
    
    println!();
    
    // 策略选择测试
    println!("🧠 2. 智能策略选择测试");
    
    // 小矩阵 - 应该选择标量实现
    let small_a = Matrix::<f32>::fill(4, 4, 1.0);
    let small_b = Matrix::<f32>::fill(4, 4, 2.0);
    let small_result = small_a.matmul(&small_b, Strategy::Auto).unwrap();
    println!("   小矩阵 (4x4): 自动选择策略");
    println!("   结果: {:.1}", small_result.data()[0]);
    
    // 大矩阵 - 应该选择分块实现
    let large_a = Matrix::<f32>::fill(128, 128, 1.0);
    let large_b = Matrix::<f32>::fill(128, 128, 2.0);
    let large_result = large_a.matmul(&large_b, Strategy::Auto).unwrap();
    println!("   大矩阵 (128x128): 自动选择策略");
    println!("   结果: {:.1}", large_result.data()[0]);
    
    println!("   ✅ 智能策略选择正常工作");
    
    println!();
    
    // 性能基准测试
    println!("⚡ 3. 性能基准测试");
    benchmark_matrix_operations();
    
    println!();
    println!("🎉 线性代数功能验证测试完成！");
    println!("✅ 功能正确性: 通过");
    println!("✅ 智能策略选择: 正常工作");
    println!("✅ 性能基准测试: 完成");
    println!();
    println!("📝 总结:");
    println!("   - 矩阵乘法、转置、向量运算功能正确");
    println!("   - 分块算法提供缓存友好的性能优化");
    println!("   - 智能策略选择机制工作正常");
    println!("   - 为高性能线性代数库奠定了基础");
}
