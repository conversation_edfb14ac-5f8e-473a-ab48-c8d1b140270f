# RustNum 模块隔离编译 Makefile
# 提供便捷的编译、测试和开发命令

.PHONY: help check build test clean install dev release docs bench format lint audit

# 默认目标
help:
	@echo "RustNum 模块隔离编译工具"
	@echo ""
	@echo "可用命令:"
	@echo "  make check          - 快速编译检查所有模块"
	@echo "  make build          - 编译所有模块（debug模式）"
	@echo "  make build-release  - 编译所有模块（release模式）"
	@echo "  make test           - 运行所有测试"
	@echo "  make test-core      - 只测试核心模块"
	@echo "  make test-python    - 只测试Python绑定"
	@echo "  make test-dist      - 只测试分布式模块"
	@echo "  make bench          - 运行基准测试"
	@echo "  make docs           - 生成文档"
	@echo "  make format         - 格式化代码"
	@echo "  make lint           - 代码检查"
	@echo "  make audit          - 安全审计"
	@echo "  make clean          - 清理构建产物"
	@echo "  make dev            - 开发模式（快速检查+测试）"
	@echo "  make release        - 发布模式（完整编译+测试）"
	@echo "  make install        - 安装开发工具"
	@echo ""
	@echo "特性编译:"
	@echo "  make build-minimal  - 最小化特性编译"
	@echo "  make build-standard - 标准特性编译"
	@echo "  make build-perf     - 性能特性编译"
	@echo "  make build-full     - 完整特性编译"
	@echo ""
	@echo "环境信息:"
	@rustc --version 2>/dev/null || echo "  Rust未安装"
	@cargo --version 2>/dev/null || echo "  Cargo未安装"

# 快速检查
check:
	@echo "🔍 运行快速编译检查..."
	@chmod +x scripts/quick_check.sh
	@./scripts/quick_check.sh

# 编译命令
build:
	@echo "🔨 编译所有模块（debug模式）..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh debug true minimal

build-release:
	@echo "🔨 编译所有模块（release模式）..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh release true standard

build-minimal:
	@echo "🔨 最小化特性编译..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh debug true minimal

build-standard:
	@echo "🔨 标准特性编译..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh debug true standard

build-perf:
	@echo "🔨 性能特性编译..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh debug true performance

build-full:
	@echo "🔨 完整特性编译..."
	@chmod +x scripts/build_isolated.sh
	@./scripts/build_isolated.sh debug true full

# 测试命令
test:
	@echo "🧪 运行所有测试..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh all

test-core:
	@echo "🧪 测试核心模块..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh core

test-python:
	@echo "🧪 测试Python绑定..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh python

test-dist:
	@echo "🧪 测试分布式模块..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh distributed

test-unit:
	@echo "🧪 运行单元测试..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh unit

test-integration:
	@echo "🧪 运行集成测试..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh integration

# 基准测试
bench:
	@echo "📊 运行基准测试..."
	@chmod +x scripts/test_isolated.sh
	@./scripts/test_isolated.sh bench

bench-run:
	@echo "📊 运行完整基准测试..."
	@cd crates/rustnum-benches && cargo bench

# 文档生成
docs:
	@echo "📚 生成文档..."
	@cargo doc --workspace --no-deps
	@echo "文档已生成到 target/doc/"

docs-open:
	@echo "📚 生成并打开文档..."
	@cargo doc --workspace --no-deps --open

# 代码质量
format:
	@echo "🎨 格式化代码..."
	@cargo fmt --all
	@echo "代码格式化完成"

format-check:
	@echo "🎨 检查代码格式..."
	@cargo fmt --all -- --check

lint:
	@echo "🔍 运行代码检查..."
	@cargo clippy --workspace --all-targets --all-features -- -D warnings

lint-fix:
	@echo "🔧 自动修复代码问题..."
	@cargo clippy --workspace --all-targets --all-features --fix --allow-dirty

# 安全审计
audit:
	@echo "🔒 运行安全审计..."
	@cargo audit || (echo "请安装 cargo-audit: cargo install cargo-audit" && exit 1)

# 依赖管理
deps-check:
	@echo "📦 检查未使用的依赖..."
	@cargo machete || (echo "请安装 cargo-machete: cargo install cargo-machete" && exit 1)

deps-update:
	@echo "📦 更新依赖..."
	@cargo update

# 清理
clean:
	@echo "🧹 清理构建产物..."
	@cargo clean
	@echo "清理完成"

clean-all: clean
	@echo "🧹 深度清理..."
	@rm -rf target/
	@rm -rf Cargo.lock
	@echo "深度清理完成"

# 开发工具安装
install:
	@echo "🛠️  安装开发工具..."
	@cargo install cargo-audit
	@cargo install cargo-machete
	@cargo install cargo-criterion
	@cargo install cargo-nextest
	@echo "开发工具安装完成"

# 开发模式
dev: check test-unit
	@echo "🚀 开发模式验证完成"

# 发布模式
release: format lint audit build-release test docs
	@echo "🎉 发布模式验证完成"

# CI模式
ci: format-check lint audit build test
	@echo "✅ CI验证完成"

# 性能分析
profile:
	@echo "📈 运行性能分析..."
	@cd crates/rustnum-core && cargo build --release
	@cd crates/rustnum-benches && cargo bench --no-run
	@echo "性能分析准备完成"

# 示例运行
examples:
	@echo "🎯 运行示例..."
	@if [ -d "examples" ]; then \
		for example in examples/*.rs; do \
			echo "运行示例: $$example"; \
			cargo run --example $$(basename $$example .rs); \
		done; \
	else \
		echo "未找到示例目录"; \
	fi

# 状态检查
status:
	@echo "📊 项目状态检查..."
	@echo "Rust版本: $$(rustc --version)"
	@echo "Cargo版本: $$(cargo --version)"
	@echo "项目结构:"
	@find crates -name "Cargo.toml" -exec echo "  {}" \;
	@echo "最近提交:"
	@git log --oneline -5 2>/dev/null || echo "  不是Git仓库"

# 快速修复
fix: format lint-fix
	@echo "🔧 快速修复完成"

# 完整验证
verify: clean check build test lint audit docs
	@echo "✅ 完整验证通过"

# 监视模式（需要安装cargo-watch）
watch:
	@echo "👀 启动监视模式..."
	@cargo watch -x "check --workspace" || (echo "请安装 cargo-watch: cargo install cargo-watch" && exit 1)

watch-test:
	@echo "👀 启动测试监视模式..."
	@cargo watch -x "test --workspace" || (echo "请安装 cargo-watch: cargo install cargo-watch" && exit 1)

# 帮助信息
info:
	@echo "ℹ️  RustNum 项目信息"
	@echo "项目: 高性能科学计算库"
	@echo "语言: Rust"
	@echo "架构: 模块化工作空间"
	@echo "模块:"
	@echo "  - rustnum-core: 核心计算库"
	@echo "  - rustnum-python: Python绑定"
	@echo "  - rustnum-distributed: 分布式计算"
	@echo "  - rustnum-benches: 基准测试"
	@echo "文档: docs/模块隔离编译规划方案.md"